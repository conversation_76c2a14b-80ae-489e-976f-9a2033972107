//go:build wireinject

package main

import (
	"enter/internal/repository"
	"enter/internal/repository/dao"
	"enter/internal/service"
	"enter/internal/web"
	"enter/internal/web/jwt"
	"enter/ioc"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cobra"
)

var thirdSet = wire.NewSet(
	wire.Bind(new(redis.Cmdable), new(*redis.Client)),
	ioc.CreateGormMysql,
	ioc.CreateRedis,
)

var jwtSet = wire.NewSet(
	ioc.CreateJWT,
	wire.Bind(new(jwt.JWT), new(*jwt.NormalJWT)),
)

var webSet = wire.NewSet(
	ioc.CreateWeb,
	ioc.CreateMiddlewares,
)

var jobSet = wire.NewSet(
	ioc.CreateJobs,
)

var logSet = wire.NewSet(
	ioc.CreateLogger,
)

var ossSet = wire.NewSet(
	ioc.CreateOSSClient,
)

var brandMatchSet = wire.NewSet(
	web.NewBrandMatchHandler,
	service.NewBrandMatchService,
)

var brandSet = wire.NewSet(
	repository.NewCacheBrandRepo,
	wire.Bind(new(repository.BrandRepo), new(*repository.CacheBrandRepo)),
	dao.NewGORMBrandDao,
	wire.Bind(new(dao.BrandDao), new(*dao.GORMBrandDao)),
)

var circleShopSet = wire.NewSet(
	repository.NewCacheCircleShopRepo,
	wire.Bind(new(repository.CircleShopRepo), new(*repository.CacheCircleShopRepo)),
	dao.NewGORMCircleShopDao,
	wire.Bind(new(dao.CircleShopDao), new(*dao.GORMCircleShopDao)),
)

var userSet = wire.NewSet(
	service.NewUserService,
	repository.NewCacheUserRepo,
	wire.Bind(new(repository.UserRepo), new(*repository.CacheUserRepo)),
	dao.NewGORMUserDao,
	wire.Bind(new(dao.UserDao), new(*dao.GORMUserDao)),
)

var callSet = wire.NewSet(
	web.NewCallHandler,
	ioc.CreateCallService,
	repository.NewCacheCallRepo,
	wire.Bind(new(repository.CallRepo), new(*repository.CacheCallRepo)),
	dao.NewGORMCallRecordDao,
	wire.Bind(new(dao.CallRecordDao), new(*dao.GORMCallRecordDao)),
)

var operateShopSet = wire.NewSet(
	web.NewOperateShopHandler,
	service.NewOperateShopService,
	repository.NewCacheOperateShopRepo,
	wire.Bind(new(repository.OperateShopRepo), new(*repository.CacheOperateShopRepo)),
	dao.NewGORMOperateShopDao,
	wire.Bind(new(dao.OperateShopDao), new(*dao.GORMOperateShopDao)),
)

func CreateApp() *cobra.Command {
	wire.Build(
		thirdSet,
		jwtSet,
		webSet,
		jobSet,
		logSet,
		wire.Struct(new(ioc.App), "*"),
		ioc.CreateAppCommand,
		brandMatchSet,
		brandSet,
		circleShopSet,
		userSet,
		callSet,
		operateShopSet,
		ossSet,
	)
	return new(cobra.Command)
}

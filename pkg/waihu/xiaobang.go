package waihu

import (
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"sort"
	"strings"
)

var _ WaihuInterface = (*XiaoBang)(nil)

// XiaoBang 营客云服务实现
type XiaoBang struct {
	config *Config
}

// NewXiaoBang 创建销帮服务实例
func NewXiaoBang(config *Config) *XiaoBang {
	if config == nil {
		config = DefaultConfig()
	}
	return &XiaoBang{
		config: config,
	}
}

func (x *XiaoBang) GetAccessToken(ctx context.Context, params CallParam) (string, error) {
	return "", nil
}

func (x *XiaoBang) CreateUser(ctx context.Context, params UnionParam) error {
	return nil
}

func (x *XiaoBang) UpdateUser(ctx context.Context, params UnionParam) error {
	return nil
}

func (x *XiaoBang) DeleteUser(ctx context.Context, params UnionParam) error {
	return nil
}

func (x *XiaoBang) Call(ctx context.Context, params CallParam) (CallResult, error) {
	url := x.config.Host + "/api/webapicall/call.html?act=call"
	reqBody := map[string]any{
		"appid":      x.config.AppKey,
		"mynumber":   params.Caller,
		"callnumber": params.Callee,
	}
	sign := x.getSign(reqBody)
	reqBody["sign"] = sign

	type callResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			ShowNo   string `json:"showno"`
			OrderId  string `json:"orderid"`
			BindId   string `json:"bind_id"`
			CallType string `json:"call_type"`
		} `json:"data"`
	}
	var result callResult
	err := httpPost(ctx, url, reqBody, nil, &result)
	if err != nil {
		return CallResult{}, err
	}
	if result.Code != 1 {
		return CallResult{}, errors.New(result.Msg)
	}

	return CallResult{}, nil

}

func (x *XiaoBang) CallBack(ctx context.Context, fn func() error) error {
	return fn()
}

func (x *XiaoBang) CheckUser(ctx context.Context, params CallParam) (bool, error) {
	return true, nil
}

func (x *XiaoBang) QueryCall(ctx context.Context, orderID string, bindID string) (bool, error) {
	return true, nil
}

func (x *XiaoBang) AsrTxt(ctx context.Context, callIDs []string, telID string) (map[string]any, error) {
	return nil, nil
}

// getSign 生成签名
func (x *XiaoBang) getSign(params map[string]any) string {
	// 1. 获取并排序所有键
	keys := make([]string, 0, len(params))
	for k := range params {
		keys = append(keys, k)
	}
	sort.Strings(keys)

	// 2. 构建签名字符串
	var builder strings.Builder
	builder.WriteString("&act=call") // 固定开头

	for _, k := range keys {
		builder.WriteString("&")
		builder.WriteString(k)
		builder.WriteString("=")
		builder.WriteString(fmt.Sprintf("%v", params[k]))
	}

	// 3. 拼接密钥并生成 MD5
	str := builder.String()
	hash := md5.Sum([]byte(str + x.config.AppSecret))
	sign := hex.EncodeToString(hash[:])
	return sign
}

package waihu

import (
	"context"
	"crypto/md5"
	"errors"
	"fmt"
	"strconv"
	"time"
)

// Ying<PERSON>eYun 营客云服务实现
type Ying<PERSON><PERSON>Yun struct {
	config *Config
}

// NewYingKeYun 创建营客云服务实例
func NewYingKeYun(config *Config) *YingKeYun {
	if config == nil {
		config = DefaultConfig()
	}
	return &YingKeYun{
		config: config,
	}
}

// getSign 生成签名
func (y *YingKeYun) getSign(timestamp int64) string {
	str := fmt.Sprintf("%s%s%d", y.config.AppKey, y.config.AppSecret, timestamp)
	return fmt.Sprintf("%x", md5.Sum([]byte(str)))
}

// GetAccessToken 获取访问令牌
func (y *YingKeYun) GetAccessToken(ctx context.Context, params CallParam) (string, error) {
	timestamp := time.Now().Unix()
	url := y.config.SdkHost + "/pms/account/openApi/yy/user/accessToken"

	reqBody := map[string]interface{}{
		"platform":     y.config.Platform,
		"enterpriseId": y.config.EnterpriseID,
		"userId":       params.UnionId,
		"metaInfo":     params.MetaInfo,
		"debugMode":    1,
	}

	headers := map[string]string{
		"appKey":    y.config.AppKey,
		"timestamp": strconv.FormatInt(timestamp, 10),
		"sign":      y.getSign(timestamp),
	}

	type tokenResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			Token string `json:"token"`
		} `json:"data"`
	}

	var result tokenResult
	err := httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return "", err
	}

	if result.Code != 200 {
		return "", errors.New(result.Msg)
	}

	return result.Data.Token, nil
}

// CreateUser 创建用户
func (y *YingKeYun) CreateUser(ctx context.Context, params UnionParam) error {
	timestamp := time.Now().Unix()
	url := y.config.SdkHost + "/pms/account/openApi/yy/user/createWorker"

	reqBody := map[string]interface{}{
		"platform":     y.config.Platform,
		"enterpriseId": y.config.EnterpriseID,
		"userId":       params.UnionId,
		"userName":     params.UnionName,
		"userPhone":    params.Mobile,
		"userType":     2,
	}

	headers := map[string]string{
		"appKey":    y.config.AppKey,
		"timestamp": strconv.FormatInt(timestamp, 10),
		"sign":      y.getSign(timestamp),
	}

	type createUserResult struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data struct{} `json:"data"`
	}

	var result createUserResult
	err := httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return err
	}
	if result.Code != 200 {
		return errors.New(result.Msg)
	}
	return nil
}

// UpdateUser 更新用户
func (y *YingKeYun) UpdateUser(ctx context.Context, params UnionParam) error {
	timestamp := time.Now().Unix()
	url := y.config.SdkHost + "/pms/account/openApi/yy/user/updateWorker"

	reqBody := map[string]interface{}{
		"platform":     y.config.Platform,
		"enterpriseId": y.config.EnterpriseID,
		"userId":       params.UnionId,
		"userName":     params.UnionName,
		"userPhone":    params.Mobile,
		"userType":     2,
	}

	headers := map[string]string{
		"appKey":    y.config.AppKey,
		"timestamp": strconv.FormatInt(timestamp, 10),
		"sign":      y.getSign(timestamp),
	}

	type updateUserResult struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data struct{} `json:"data"`
	}

	var result updateUserResult
	err := httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return err
	}
	if result.Code != 200 {
		return errors.New(result.Msg)
	}
	return nil
}

// DeleteUser 删除用户
func (y *YingKeYun) DeleteUser(ctx context.Context, params UnionParam) error {
	timestamp := time.Now().Unix()
	url := y.config.SdkHost + "/pms/account/openApi/yy/user/deleteWorker"

	reqBody := map[string]interface{}{
		"platform":     y.config.Platform,
		"enterpriseId": y.config.EnterpriseID,
		"userId":       params.UnionId,
	}

	headers := map[string]string{
		"appKey":    y.config.AppKey,
		"timestamp": strconv.FormatInt(timestamp, 10),
		"sign":      y.getSign(timestamp),
	}

	type deleteUserResult struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data struct{} `json:"data"`
	}

	var result deleteUserResult
	err := httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return err
	}
	if result.Code != 200 {
		return errors.New(result.Msg)
	}
	return nil
}

// Call 拨打电话
func (y *YingKeYun) Call(ctx context.Context, params CallParam) (CallResult, error) {
	// 先检查用户ID
	ok, err := y.CheckUser(ctx, params)
	if err != nil {
		return CallResult{}, err
	}
	if !ok {
		return CallResult{}, fmt.Errorf("invalid user id")
	}

	url := y.config.Host + "/callCenter/callPhone/applyCall"
	reqBody := map[string]interface{}{
		"callType":  1,
		"userId":    params.TelId,
		"phone":     params.Callee,
		"useDevice": "appLink",
		"payload":   params.OrderSn,
	}

	token, err := y.GetAccessToken(ctx, params)
	if err != nil {
		return CallResult{}, err
	}

	headers := y.getHeaders(token)

	type callResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data struct {
			AppLink string `json:"appLink"`
		} `json:"data"`
	}

	var result callResult
	err = httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return CallResult{}, err
	}

	if result.Code != 200 {
		return CallResult{}, errors.New(result.Msg)
	}

	return CallResult{
		AppLink: result.Data.AppLink,
	}, nil
}

// CheckUser 检查用户
func (y *YingKeYun) CheckUser(ctx context.Context, params CallParam) (bool, error) {
	url := y.config.Host + "/callCenter/auth/checkUserId"
	reqBody := map[string]interface{}{
		"userId": params.TelId,
	}

	token, err := y.GetAccessToken(ctx, params)
	if err != nil {
		return false, err
	}

	headers := y.getHeaders(token)

	type checkUserResult struct {
		Code int      `json:"code"`
		Msg  string   `json:"msg"`
		Data struct{} `json:"data"`
	}

	var result checkUserResult
	err = httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return false, err
	}
	if result.Code != 200 {
		return false, errors.New(result.Msg)
	}
	return true, nil
}

// QueryCall 查询通话
func (y *YingKeYun) QueryCall(ctx context.Context, orderID string, bindID string) (bool, error) {
	return true, nil
}

// CallBack 通话回调
func (y *YingKeYun) CallBack(ctx context.Context, fn func() error) error {
	// 实现通话回调逻辑
	return nil
}

// AsrTxt 获取通话文本
func (y *YingKeYun) AsrTxt(ctx context.Context, callIDs []string, telID string) (map[string]any, error) {
	url := y.config.Host + "/callCenter/callPhone/asrTxt"
	reqBody := map[string]interface{}{
		"callIds": callIDs,
	}

	token, err := y.GetAccessToken(ctx, CallParam{
		TelId: telID,
	})
	if err != nil {
		return nil, err
	}

	headers := y.getHeaders(token)

	type asrTxtDetailResult struct {
		SpeakerId   int    `json:"speakerId"`
		Text        string `json:"text"`
		OffsetStart int    `json:"offsetStart"`
		OffsetEnd   int    `json:"offsetEnd"`
	}

	type asrTxtResult struct {
		Code int    `json:"code"`
		Msg  string `json:"msg"`
		Data []struct {
			CallId  string               `json:"callId"`
			Details []asrTxtDetailResult `json:"details"`
		} `json:"data"`
	}

	var result asrTxtResult
	err = httpPost(ctx, url, reqBody, headers, &result)
	if err != nil {
		return nil, err
	}

	if result.Code != 200 {
		return nil, errors.New(result.Msg)
	}

	resultMap := make(map[string]any)
	for _, item := range result.Data {
		resultMap[item.CallId] = item.Details
	}

	return resultMap, nil
}

// getHeaders 获取请求头
func (y *YingKeYun) getHeaders(token string) map[string]string {
	headers := map[string]string{
		"device":      "pc",
		"device_code": "none",
	}

	if token != "" {
		headers["Authorization"] = token
	}

	return headers
}

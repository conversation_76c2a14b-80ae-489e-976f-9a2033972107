package waihu

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"
)

// httpPost 发送POST请求
func httpPost(ctx context.Context, url string, body interface{}, headers map[string]string, respStruct any) error {
	// 将body转换为JSON
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %w", err)
	}

	// 创建请求
	req, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("create request failed: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")
	for k, v := range headers {
		req.Header.Set(k, v)
	}

	// 创建HTTP客户端
	client := &http.Client{
		Timeout: time.Second * 10,
	}

	// 发送请求
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("send request failed: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response body failed: %w", err)
	}

	// 解析响应
	if err := json.Unmarshal(respBody, &respStruct); err != nil {
		return fmt.Errorf("unmarshal response body failed: %w", err)
	}

	return nil
}

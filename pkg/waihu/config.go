package waihu

// Config 外呼服务配置
type Config struct {
	Host         string `json:"host"`
	AppSecret    string `json:"app_secret"`
	AppKey       string `json:"app_key"`
	Platform     string `json:"platform"`
	EnterpriseID int64  `json:"enterprise_id"`
	SdkHost      string `json:"sdk_host"`
	SdkAppSecret string `json:"sdk_app_secret"`
}

// DefaultConfig 默认配置
func DefaultConfig() *Config {
	return &Config{
		Host:         "https://openapi.aganyunke.com",
		AppSecret:    "d6a80debdca441a1b42a453d892803b4",
		AppKey:       "fefeca06a3d9f8b905bad4c8aa79e2b3",
		Platform:     "zhaoshangbao",
		EnterpriseID: 113094,
		SdkHost:      "https://test-api.aganyunke.com",
	}
}

type CallParam struct {
	Caller   string
	Callee   string
	OrderSn  string
	TelId    string
	MetaInfo string
	UnionId  int64
}

type CallResult struct {
	AppLink  string
	ShowNo   string
	OrderId  string
	BindId   string
	CallType string
}

type UnionParam struct {
	UnionId   int64
	UnionName string
	Mobile    string
}

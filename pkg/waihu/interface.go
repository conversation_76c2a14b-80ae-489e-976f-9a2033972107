package waihu

import "context"

// WaihuInterface 外呼服务接口
type WaihuInterface interface {
	// GetAccessToken 获取访问令牌
	GetAccessToken(ctx context.Context, params CallParam) (string, error)

	// CreateUser 创建用户
	CreateUser(ctx context.Context, params UnionParam) error

	// UpdateUser 更新用户
	UpdateUser(ctx context.Context, params UnionParam) error

	// DeleteUser 删除用户
	DeleteUser(ctx context.Context, params UnionParam) error

	// Call 拨打电话
	Call(ctx context.Context, params CallParam) (CallResult, error)

	// CallBack 通话回调
	CallBack(ctx context.Context, fn func() error) error

	// CheckUser 检查用户ID
	CheckUser(ctx context.Context, params CallParam) (bool, error)

	// QueryCall 查询通话
	QueryCall(ctx context.Context, orderID string, bindID string) (bool, error)

	// AsrTxt 获取通话文本
	AsrTxt(ctx context.Context, callIDs []string, telID string) (map[string]any, error)
}

type WaihuService struct {
	channelMap map[int8]WaihuInterface
}

func NewWaihuService(channelMap map[int8]WaihuInterface) *WaihuService {
	return &WaihuService{
		channelMap: channelMap,
	}
}

func (w *WaihuService) GetAccessToken(ctx context.Context, channel int8, params CallParam) (string, error) {
	return w.channelMap[channel].GetAccessToken(ctx, params)
}

func (w *WaihuService) CreateUser(ctx context.Context, channel int8, params UnionParam) error {
	return w.channelMap[channel].CreateUser(ctx, params)
}

func (w *WaihuService) UpdateUser(ctx context.Context, channel int8, params UnionParam) error {
	return w.channelMap[channel].UpdateUser(ctx, params)
}

func (w *WaihuService) DeleteUser(ctx context.Context, channel int8, params UnionParam) error {
	return w.channelMap[channel].DeleteUser(ctx, params)
}

func (w *WaihuService) Call(ctx context.Context, channel int8, params CallParam) (CallResult, error) {
	return w.channelMap[channel].Call(ctx, params)
}

func (w *WaihuService) CallBack(ctx context.Context, channel int8, fn func() error) error {
	return w.channelMap[channel].CallBack(ctx, fn)
}

func (w *WaihuService) CheckUser(ctx context.Context, channel int8, params CallParam) (bool, error) {
	return w.channelMap[channel].CheckUser(ctx, params)
}

func (w *WaihuService) QueryCall(ctx context.Context, channel int8, orderID string, bindID string) (bool, error) {
	return w.channelMap[channel].QueryCall(ctx, orderID, bindID)
}

func (w *WaihuService) AsrTxt(ctx context.Context, channel int8, callIDs []string, telID string) (map[string]any, error) {
	return w.channelMap[channel].AsrTxt(ctx, callIDs, telID)
}

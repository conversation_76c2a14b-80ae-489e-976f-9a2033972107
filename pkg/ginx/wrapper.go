package ginx

import (
	"net/http"

	"enter/internal/errs"
	"enter/pkg/validate"

	"go.opentelemetry.io/otel/trace"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
	"go.uber.org/zap"
)

type Result struct {
	Data    any    `json:"data"`
	Code    int    `json:"code"`
	Message string `json:"message"`
}

func WrapWithReq[Req validate.Validator](f func(ctx *gin.Context, req Req) (Result, error)) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req Req
		if err := ctx.ShouldBind(&req); err != nil {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.CommonInputValid,
				Message: "输入参数错误",
			})
			return
		}
		err := validate.Validate(req)
		if err != nil {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.CommonInputValid,
				Message: err.Error(),
			})
			return
		}
		result, err := f(ctx, req)
		traceId := trace.SpanFromContext(ctx).SpanContext().TraceID().String()
		if err != nil {
			zap.L().Error("处理业务逻辑出错",
				zap.String("route", ctx.FullPath()),
				zap.String("trace_id", traceId),
				zap.Error(err))
		}
		ctx.Header("Trace-Id", traceId)
		ctx.JSON(http.StatusOK, result)
	}
}

func Wrap(fn func(ctx *gin.Context) (Result, error)) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		res, err := fn(ctx)
		traceId := trace.SpanFromContext(ctx).SpanContext().TraceID().String()
		if err != nil {
			zap.L().Error("处理业务逻辑出错",
				zap.String("route", ctx.FullPath()),
				zap.String("trace_id", traceId),
				zap.Error(err))
		}
		ctx.Header("Trace-Id", traceId)
		ctx.JSON(http.StatusOK, res)
	}
}

func WrapWithReqAndClaims[Req validate.Validator, Claims jwt.Claims](
	bizFn func(ctx *gin.Context, req Req, uc Claims) (Result, error),
) gin.HandlerFunc {
	return func(ctx *gin.Context) {
		var req Req
		if err := ctx.ShouldBind(&req); err != nil {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.CommonInputValid,
				Message: "输入参数错误",
			})
			return
		}
		if len(req.Rules()) != 0 && len(req.Messages()) != 0 {
			err := validate.Validate(req)
			if err != nil {
				ctx.JSON(http.StatusOK, Result{
					Code:    errs.CommonInputValid,
					Message: err.Error(),
				})
				return
			}
		}

		// 需要和中间件中设置的 key 一致
		val, ok := ctx.Get("claims")
		if !ok {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.UserForbidden,
				Message: "用户未登录",
			})
			return
		}
		uc, ok := val.(Claims)
		if !ok {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.UserForbidden,
				Message: "用户未登录",
			})
			return
		}
		res, err := bizFn(ctx, req, uc)
		traceId := trace.SpanFromContext(ctx).SpanContext().TraceID().String()
		if err != nil {
			zap.L().Error("处理业务逻辑出错",
				zap.String("route", ctx.FullPath()),
				zap.String("trace_id", traceId),
				zap.Error(err))
		}
		ctx.Header("Trace-Id", traceId)
		ctx.JSON(http.StatusOK, res)
	}
}

func WrapWithClaims[Claims jwt.Claims](
	bizFn func(ctx *gin.Context, uc Claims) (Result, error),
) gin.HandlerFunc {
	return func(ctx *gin.Context) {

		// 需要和中间件中设置的 key 一致
		val, ok := ctx.Get("claims")
		if !ok {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.UserForbidden,
				Message: "用户未登录",
			})
			return
		}
		uc, ok := val.(Claims)
		if !ok {
			ctx.JSON(http.StatusOK, Result{
				Code:    errs.UserForbidden,
				Message: "用户未登录",
			})
			return
		}
		res, err := bizFn(ctx, uc)
		traceId := trace.SpanFromContext(ctx).SpanContext().TraceID().String()
		if err != nil {
			zap.L().Error("处理业务逻辑出错",
				zap.String("route", ctx.FullPath()),
				zap.String("trace_id", traceId),
				zap.Error(err))
		}
		ctx.Header("Trace-Id", traceId)
		ctx.JSON(http.StatusOK, res)
	}
}

package pkg

import (
	"bytes"
	"encoding/json"
	"fmt"
	"net/http"
	"sort"
	"strings"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/spf13/cast"
	"github.com/tidwall/gjson"
)

type Client struct {
	appKey    string
	appSecret string
	version   string
	format    string
	charset   string
	signType  string
	url       string
}

type Option func(client *Client)

func WithAppKey(appKey string) Option {
	return func(client *Client) {
		client.appKey = appKey
	}
}

func WithAppSecret(appSecret string) Option {
	return func(client *Client) {
		client.appSecret = appSecret
	}
}

func WithVersion(version string) Option {
	return func(client *Client) {
		client.version = version
	}
}

func WithUrl(url string) Option {
	return func(client *Client) {
		client.url = url
	}
}

func NewDefaultClient() *Client {
	return NewClient()
}

func NewClient(options ...Option) (client *Client) {
	client = &Client{
		appKey:    "medbxd",
		appSecret: "1592f9e19aa0e707b8e18ac34827f3d9",
		version:   "1.0",
		format:    "json",
		charset:   "utf-8",
		signType:  "md5",
		url:       "https://qyb.biyingniao.com",
	}
	for _, v := range options {
		v(client)
	}
	return
}

func (c *Client) Sign(m map[string]any) string {
	keys := make([]string, 0, len(m))
	for k := range m {
		keys = append(keys, k)
	}
	sort.Strings(keys)
	var (
		buf    bytes.Buffer
		length = len(keys)
	)
	for i, k := range keys {
		buf.WriteString(k)
		buf.WriteString("=")
		buf.WriteString(cast.ToString(m[k]))
		if i < length-1 {
			buf.WriteString("&")
		}
	}
	buf.WriteString(c.appSecret)
	return strings.ToLower(MD5String(buf.String()))
}

type SendWxRoomMessageParam struct {
	WxRoomID   string
	Vars       []TemplateVar
	TemplateID string
}

type TemplateVar struct {
	Key   string
	Value string
}

// doRequest 处理通用的请求逻辑
func (c *Client) doRequest(method string, path string, bizContent interface{}) ([]byte, error) {
	// 构建基础参数
	params := map[string]any{
		"app_key":   c.appKey,
		"timestamp": time.Now().Unix(),
		"version":   c.version,
		"format":    c.format,
		"charset":   c.charset,
		"sign_type": c.signType,
	}

	// 序列化业务参数
	content, err := json.Marshal(bizContent)
	if err != nil {
		return nil, fmt.Errorf("序列化业务参数失败: %w", err)
	}
	fmt.Println(string(content))
	params["biz_content"] = string(content)

	// 添加签名
	params["sign"] = c.Sign(params)

	// 发送请求
	url := c.url + path

	var response *resty.Response
	switch method {
	case http.MethodGet:
		response, err = resty.New().R().SetQueryParams(ToMapString(params)).Get(url)
	case http.MethodPost:
		response, err = resty.New().R().SetBody(params).SetHeader("Content-Type", "application/json").Post(url)
	}

	if err != nil {
		return nil, fmt.Errorf("发送请求失败: %w", err)
	}

	// 处理响应
	return c.handleResponse(response.Body())
}

// handleResponse 统一处理响应
func (c *Client) handleResponse(body []byte) ([]byte, error) {
	parsed := gjson.ParseBytes(body)
	if parsed.Get("code").Int() != 0 {
		return nil, fmt.Errorf("API调用失败: %s", parsed.Get("message").String())
	}
	return []byte(parsed.Get("data").String()), nil
}

// SendWxRoomMessage 发送微信群消息
func (c *Client) SendWxRoomMessage(param SendWxRoomMessageParam) error {
	paramMap := make(map[string]any)
	for _, v := range param.Vars {
		paramMap[v.Key] = v.Value
	}
	paramBytes, err := json.Marshal(paramMap)
	if err != nil {
		return fmt.Errorf("序列化参数失败: %w", err)
	}
	bodyMap := map[string]any{
		"group_wx_id":     cast.ToString(param.WxRoomID),
		"param_map":       string(paramBytes),
		"template_id":     param.TemplateID,
		"is_sync":         true,
		"is_at_all":       true,
		"event_rule_type": 1,
	}
	_, err = c.doRequest(http.MethodPost, "/api/notice/send/tpl", bodyMap)
	return err
}

func ToMapString(in map[string]any) map[string]string {
	res := make(map[string]string)
	for k, v := range in {
		res[k] = cast.ToString(v)
	}
	return res
}

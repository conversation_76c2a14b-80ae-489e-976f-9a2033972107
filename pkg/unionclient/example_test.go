package unionclient_test

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http/cookiejar"
	"net/url"
	"testing"
	"time"

	"enter/pkg/unionclient"
)

func TestExample(t *testing.T) {
	// 创建Cookie Jar
	jar, _ := cookiejar.New(nil)

	// 解析Cookie字符串
	cookieStr := `XSRF-TOKEN=0491b1df-3383-45ed-b8c2-65dbe98f3869;cna=JgmOIPPVbVACAXPNvz3cbkaL;_samesite_flag_=true;cookie2=2de12cbe29766e48a8a88037c785c152e;t=e38a2e4aebc69ccd96b2a08598fda507;_tb_token_=e3b6917367156;xlly_s=1;sgcookie=E100I%2BtcfNgY0JrbYcNGtcl9HC0uO0f4HXK3Fdw%2BonEQCykMmT%2FSXD4rsQElhPvSAmvgeo4h%2B0Qt%2F8C2Cha3oy4oqi6Dg2k0PyGAMBCsHYvGq3A%3D;munb=2204304102293;unb=2204304102293;USERID=57879287;SID=MmRlMTJjYmUyOTc2NmU0OGE4YTg4MDM3Yzc4NWMxNTJltBsPx4SSEdi7VbWB3zxxow==;UTUSER=57879287;isg=BJqaIkNFpp2HuSQhDLD08RQy60a8yx6ldZWi76QTRi34FzpRjFtutWB14eOL9ZY9;tfstk=gAOnqt_TaGZjUIxYvh5In3yCeBkT9y1Wa3FR2_IrQGS_pTpd9LvMDhJdUe3BjbxM0pE-x65orhtnRTnIRCjkAHfJwe5RU3x9f7Ld9UdMZ3KtRH3BJ7vklGtlF2sR4gxJzp3tMjLBR_1yrmhxM5IMoQRh4uCPO17lu28qMjLB8yQEkFlvwyZ1iMjPa9Wz_RS1rk7e4W8ZSa78Y8Pe4FuGfaEUa75P_P7l8y5Pa38ZSasN4fBtmifPsCo6-UXA93-2swXh0eDba7AgM9j28GVrHCbnVi8Fj7PPA9DK7U-rq7C2hhAl33Gu16LDQ17HI4yNYtvevNtoiWSeseRFvBoT9MRB7KdJb4PPqdJVnMJsfb6ehHpVICn31iJwSUBMC4wd2K_yVtO-cWsezEOXHscurNvD71jz6RyVWvF574daF86F5NjvLbh5tqNqj2uiS-gOLN_QDV0gFXXF5NjxSV24t971RoC..;`
	cookies := unionclient.ParseCookieString(cookieStr)

	// 创建配置选项
	opts := []unionclient.Option{
		unionclient.WithBaseURL("https://union.ele.me"),
		unionclient.WithTimeout(30 * time.Second),
		unionclient.WithRetry(3, 100*time.Millisecond),
		unionclient.WithHeader("accept", "application/json, text/plain, */*"),
		unionclient.WithHeader("accept-language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6"),
		unionclient.WithHeader("bx-v", "2.5.11"),
		unionclient.WithCookieJar(jar),
		unionclient.WithCSRFToken("XSRF-TOKEN", true),
	}

	// 添加Cookie配置
	for _, cookie := range cookies {
		opts = append(opts, unionclient.WithCookie(cookie))
	}

	// 创建客户端
	client := unionclient.NewClient(opts...)

	// 构建查询参数
	query := url.Values{}
	query.Set("page", "2")
	query.Set("limit", "10")
	query.Set("supplierType", "1")
	query.Set("businessType", "1")
	query.Set("activityId", "716002")

	// 定义响应结构
	type Shop struct {
		ID   string `json:"id"`
		Name string `json:"name"`
	}

	type Response struct {
		Result []struct {
			ActivityId           int     `json:"activityId"`
			AuditState           int     `json:"auditState"`
			AuditType            int     `json:"auditType"`
			BonusFee             int     `json:"bonusFee"`
			BonusOrderNum        int     `json:"bonusOrderNum"`
			BusinessDistrictName string  `json:"businessDistrictName"`
			ChannelServicesFee   float64 `json:"channelServicesFee"`
			CityServicesFee      float64 `json:"cityServicesFee"`
			CommentReward        int     `json:"commentReward"`
			CommosionRate        string  `json:"commosionRate"`
			Discount             float64 `json:"discount"`
			EndDate              string  `json:"endDate"`
			EnrollDate           string  `json:"enrollDate"`
			Id                   int     `json:"id"`
			Link                 string  `json:"link"`
			Logo                 string  `json:"logo"`
			OrderAMTLimit        int     `json:"orderAMTLimit"`
			PromotionState       int     `json:"promotionState"`
			PromotionTerminals   string  `json:"promotionTerminals"`
			SalesNum             int     `json:"salesNum"`
			ServicesFee          int     `json:"servicesFee"`
			ShopId               string  `json:"shopId"`
			ShopName             string  `json:"shopName"`
			ShopScore            string  `json:"shopScore"`
			StartDate            string  `json:"startDate"`
			UnitPrice            float64 `json:"unitPrice,omitempty"`
			UserPayThreshold     int     `json:"userPayThreshold"`
			ZoneName             string  `json:"zoneName"`
		} `json:"result"`
		Success    bool `json:"success"`
		TotalCount int  `json:"totalCount"`
	}

	// 发送请求并解析响应
	var result Response
	ctx := context.Background()
	err := client.GetJSON(ctx, "/cityservices/merchant/shoplist", query, &result)
	if err != nil {
		t.Errorf("request failed: %v", err)
		return
	}

	t.Logf("response: %+v", result)
}

func ExampleClient_Post() {
	client := unionclient.NewClient(
		unionclient.WithBaseURL("https://api.example.com"),
		unionclient.WithErrorHandler(func(statusCode int, body []byte) error {
			// 处理业务错误码
			var resp struct {
				Code    int    `json:"code"`
				Message string `json:"message"`
			}
			if err := json.Unmarshal(body, &resp); err != nil {
				return err
			}
			if resp.Code != 0 {
				return fmt.Errorf("business error: code=%d, message=%s", resp.Code, resp.Message)
			}
			return nil
		}),
	)

	// POST请求体
	body := map[string]interface{}{
		"name": "test",
		"age":  18,
	}

	// 发送POST请求
	var result struct {
		Code    int         `json:"code"`
		Message string      `json:"message"`
		Data    interface{} `json:"data"`
	}
	ctx := context.Background()
	err := client.PostJSON(ctx, "/users", body, &result)
	if err != nil {
		fmt.Printf("request failed: %v\n", err)
		return
	}

	fmt.Printf("response: %+v\n", result)
}

package unionclient

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"path"
	"time"
)

// Client HTTP客户端
type Client struct {
	config *Config
	client *http.Client
}

// NewClient 创建新的HTTP客户端
func NewClient(opts ...Option) *Client {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}

	transport := &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: config.InsecureSkipVerify,
		},
	}

	// 设置代理
	if config.ProxyURL != "" {
		proxyURL, err := url.Parse(config.ProxyURL)
		if err == nil {
			transport.Proxy = http.ProxyURL(proxyURL)
		}
	}

	// 使用自定义Transport
	if config.Transport != nil {
		transport = config.Transport.(*http.Transport)
	}

	client := &http.Client{
		Timeout:   config.Timeout,
		Transport: transport,
		Jar:       config.CookieJar,
	}

	return &Client{
		config: config,
		client: client,
	}
}

// Request 请求结构体
type Request struct {
	Method  string
	Path    string
	Query   url.Values
	Headers map[string]string
	Cookies []*http.Cookie
	Body    interface{}
}

// Response 响应结构体
type Response struct {
	StatusCode int
	Headers    http.Header
	Cookies    []*http.Cookie
	Body       []byte
}

// Do 执行HTTP请求
func (c *Client) Do(ctx context.Context, req *Request) (*Response, error) {
	var err error
	var httpReq *http.Request
	var httpResp *http.Response

	// 构建URL
	reqURL, err := c.buildURL(req)
	if err != nil {
		return nil, fmt.Errorf("build url failed: %w", err)
	}

	// 处理请求体
	var bodyReader io.Reader
	if req.Body != nil {
		var bodyBytes []byte
		bodyBytes, err = json.Marshal(req.Body)
		if err != nil {
			return nil, fmt.Errorf("marshal request body failed: %w", err)
		}
		bodyReader = bytes.NewReader(bodyBytes)
	}

	// 创建请求
	httpReq, err = http.NewRequestWithContext(ctx, req.Method, reqURL, bodyReader)
	if err != nil {
		return nil, fmt.Errorf("create request failed: %w", err)
	}

	// 添加默认请求头
	for k, v := range c.config.DefaultHeaders {
		httpReq.Header.Set(k, v)
	}

	// 添加自定义请求头
	for k, v := range req.Headers {
		httpReq.Header.Set(k, v)
	}

	// 如果有请求体，设置Content-Type
	if req.Body != nil {
		httpReq.Header.Set("Content-Type", "application/json")
	}

	// 添加默认Cookie
	for _, cookie := range c.config.DefaultCookies {
		httpReq.AddCookie(cookie)
	}

	// 添加自定义Cookie
	for _, cookie := range req.Cookies {
		httpReq.AddCookie(cookie)
	}

	// 处理CSRF Token
	if c.config.AutoHandleCSRFToken {
		if err := c.handleCSRFToken(httpReq); err != nil {
			return nil, fmt.Errorf("handle csrf token failed: %w", err)
		}
	}

	// 添加请求签名
	if c.config.SignatureKey != "" {
		if err := c.signRequest(httpReq); err != nil {
			return nil, fmt.Errorf("sign request failed: %w", err)
		}
	}

	// 执行请求(带重试)
	var respBody []byte
	var lastErr error
	for i := 0; i <= c.config.RetryCount; i++ {
		if i > 0 {
			time.Sleep(c.config.RetryInterval)
		}

		httpResp, err = c.client.Do(httpReq)
		if err != nil {
			lastErr = fmt.Errorf("request failed: %w", err)
			if i == c.config.RetryCount {
				return nil, fmt.Errorf("request failed after %d retries: %w", c.config.RetryCount, lastErr)
			}
			continue
		}

		respBody, err = io.ReadAll(httpResp.Body)
		httpResp.Body.Close()
		if err != nil {
			lastErr = fmt.Errorf("read response body failed: %w", err)
			if i == c.config.RetryCount {
				return nil, fmt.Errorf("read response body failed after %d retries: %w", c.config.RetryCount, lastErr)
			}
			continue
		}

		// 处理HTTP状态码错误
		if httpResp.StatusCode >= 400 {
			if c.config.ErrorHandler != nil {
				if err := c.config.ErrorHandler(httpResp.StatusCode, respBody); err != nil {
					lastErr = err
					if i == c.config.RetryCount {
						return nil, fmt.Errorf("request failed after %d retries: %w", c.config.RetryCount, lastErr)
					}
					continue
				}
			} else {
				lastErr = fmt.Errorf("request failed with status code: %d", httpResp.StatusCode)
				if i == c.config.RetryCount {
					return nil, fmt.Errorf("request failed after %d retries: %w", c.config.RetryCount, lastErr)
				}
				continue
			}
		}

		break
	}

	return &Response{
		StatusCode: httpResp.StatusCode,
		Headers:    httpResp.Header,
		Cookies:    httpResp.Cookies(),
		Body:       respBody,
	}, nil
}

// handleCSRFToken 处理CSRF Token
func (c *Client) handleCSRFToken(req *http.Request) error {
	var csrfToken string
	for _, cookie := range req.Cookies() {
		if cookie.Name == c.config.CSRFTokenCookieName {
			csrfToken = cookie.Value
			break
		}
	}

	if csrfToken != "" {
		req.Header.Set("X-CSRF-TOKEN", csrfToken)
		req.Header.Set("X-XSRF-TOKEN", csrfToken)
	}

	return nil
}

// signRequest 签名请求
func (c *Client) signRequest(req *http.Request) error {
	// TODO: 实现请求签名逻辑
	return nil
}

// buildURL 构建完整的请求URL
func (c *Client) buildURL(req *Request) (string, error) {
	baseURL := c.config.BaseURL
	if baseURL == "" {
		baseURL = "/"
	}

	u, err := url.Parse(baseURL)
	if err != nil {
		return "", err
	}

	u.Path = path.Join(u.Path, req.Path)

	// 添加查询参数
	if req.Query != nil {
		u.RawQuery = req.Query.Encode()
	}

	return u.String(), nil
}

// Get 发送GET请求
func (c *Client) Get(ctx context.Context, path string, query url.Values) (*Response, error) {
	return c.Do(ctx, &Request{
		Method: http.MethodGet,
		Path:   path,
		Query:  query,
	})
}

// Post 发送POST请求
func (c *Client) Post(ctx context.Context, path string, body interface{}) (*Response, error) {
	return c.Do(ctx, &Request{
		Method: http.MethodPost,
		Path:   path,
		Body:   body,
	})
}

// Put 发送PUT请求
func (c *Client) Put(ctx context.Context, path string, body interface{}) (*Response, error) {
	return c.Do(ctx, &Request{
		Method: http.MethodPut,
		Path:   path,
		Body:   body,
	})
}

// Delete 发送DELETE请求
func (c *Client) Delete(ctx context.Context, path string) (*Response, error) {
	return c.Do(ctx, &Request{
		Method: http.MethodDelete,
		Path:   path,
	})
}

// GetJSON 发送GET请求并解析JSON响应
func (c *Client) GetJSON(ctx context.Context, path string, query url.Values, v interface{}) error {
	resp, err := c.Get(ctx, path, query)
	if err != nil {
		return err
	}
	return resp.Unmarshal(v)
}

// PostJSON 发送POST请求并解析JSON响应
func (c *Client) PostJSON(ctx context.Context, path string, body interface{}, v interface{}) error {
	resp, err := c.Post(ctx, path, body)
	if err != nil {
		return err
	}
	return resp.Unmarshal(v)
}

// PutJSON 发送PUT请求并解析JSON响应
func (c *Client) PutJSON(ctx context.Context, path string, body interface{}, v interface{}) error {
	resp, err := c.Put(ctx, path, body)
	if err != nil {
		return err
	}
	return resp.Unmarshal(v)
}

// DeleteJSON 发送DELETE请求并解析JSON响应
func (c *Client) DeleteJSON(ctx context.Context, path string, v interface{}) error {
	resp, err := c.Delete(ctx, path)
	if err != nil {
		return err
	}
	return resp.Unmarshal(v)
}

// Unmarshal 解析响应体到指定结构
func (r *Response) Unmarshal(v interface{}) error {
	return json.Unmarshal(r.Body, v)
}

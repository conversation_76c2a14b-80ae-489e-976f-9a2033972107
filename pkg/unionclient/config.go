package unionclient

import (
	"net/http"
	"time"
)

// Config HTTP客户端配置
type Config struct {
	// 基础URL
	BaseURL string
	// 超时时间
	Timeout time.Duration
	// 重试次数
	RetryCount int
	// 重试间隔
	RetryInterval time.Duration
	// 默认请求头
	DefaultHeaders map[string]string
	// 默认Cookie
	DefaultCookies []*http.Cookie
	// Transport
	Transport http.RoundTripper
	// 代理URL
	ProxyURL string
	// CSRF Token
	CSRFTokenCookieName string
	// 是否自动处理CSRF Token
	AutoHandleCSRFToken bool
	// 是否验证SSL证书
	InsecureSkipVerify bool
	// 请求签名密钥
	SignatureKey string
	// 业务错误码处理函数
	ErrorHandler func(statusCode int, body []byte) error
	// Cookie管理器
	CookieJar http.CookieJar
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Timeout:             30 * time.Second,
		RetryCount:          3,
		RetryInterval:       100 * time.Millisecond,
		CSRFTokenCookieName: "XSRF-TOKEN",
		AutoHandleCSRFToken: true,
		DefaultHeaders: map[string]string{
			"User-Agent":      "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
			"Accept":          "application/json, text/plain, */*",
			"Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7,zh-TW;q=0.6",
			"Sec-Fetch-Mode":  "cors",
			"Sec-Fetch-Site":  "same-origin",
			"Sec-Fetch-Dest":  "empty",
		},
	}
}

// Option 配置选项函数
type Option func(*Config)

// WithBaseURL 设置基础URL
func WithBaseURL(baseURL string) Option {
	return func(c *Config) {
		c.BaseURL = baseURL
	}
}

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) Option {
	return func(c *Config) {
		c.Timeout = timeout
	}
}

// WithRetry 设置重试配置
func WithRetry(count int, interval time.Duration) Option {
	return func(c *Config) {
		c.RetryCount = count
		c.RetryInterval = interval
	}
}

// WithHeader 添加默认请求头
func WithHeader(key, value string) Option {
	return func(c *Config) {
		if c.DefaultHeaders == nil {
			c.DefaultHeaders = make(map[string]string)
		}
		c.DefaultHeaders[key] = value
	}
}

// WithCookie 添加默认Cookie
func WithCookie(cookie *http.Cookie) Option {
	return func(c *Config) {
		c.DefaultCookies = append(c.DefaultCookies, cookie)
	}
}

// WithTransport 设置Transport
func WithTransport(transport http.RoundTripper) Option {
	return func(c *Config) {
		c.Transport = transport
	}
}

// WithProxy 设置代理
func WithProxy(proxyURL string) Option {
	return func(c *Config) {
		c.ProxyURL = proxyURL
	}
}

// WithCSRFToken 设置CSRF Token配置
func WithCSRFToken(cookieName string, autoHandle bool) Option {
	return func(c *Config) {
		c.CSRFTokenCookieName = cookieName
		c.AutoHandleCSRFToken = autoHandle
	}
}

// WithInsecureSkipVerify 设置是否跳过SSL证书验证
func WithInsecureSkipVerify(skip bool) Option {
	return func(c *Config) {
		c.InsecureSkipVerify = skip
	}
}

// WithSignatureKey 设置请求签名密钥
func WithSignatureKey(key string) Option {
	return func(c *Config) {
		c.SignatureKey = key
	}
}

// WithErrorHandler 设置错误处理函数
func WithErrorHandler(handler func(statusCode int, body []byte) error) Option {
	return func(c *Config) {
		c.ErrorHandler = handler
	}
}

// WithCookieJar 设置Cookie管理器
func WithCookieJar(jar http.CookieJar) Option {
	return func(c *Config) {
		c.CookieJar = jar
	}
}

// ParseCookieString 解析Cookie字符串
func ParseCookieString(cookieStr string) []*http.Cookie {
	header := http.Header{}
	header.Add("Cookie", cookieStr)
	request := http.Request{Header: header}
	return request.Cookies()
}

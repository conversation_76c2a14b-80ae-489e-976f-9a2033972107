package logger

import (
	"context"

	"enter/pkg/syncx"

	"go.opentelemetry.io/otel/trace"

	"go.uber.org/zap"
)

type ZapLogger struct {
	logger *syncx.Value[*zap.Logger]
}

func NewZapLogger(l *zap.Logger) *ZapLogger {
	return &ZapLogger{
		logger: syncx.NewValueOf[*zap.Logger](l),
	}
}

func (z *ZapLogger) UpdateZapLogger(l *zap.Logger) {
	z.logger.Store(l)
}

func (z *ZapLogger) Debug(ctx context.Context, msg string, args ...Field) {
	z.logger.Load().Debug(msg, z.toZapFields(ctx, args)...)
}

func (z *ZapLogger) Info(ctx context.Context, msg string, args ...Field) {
	z.logger.Load().Info(msg, z.toZapFields(ctx, args)...)
}

func (z *ZapLogger) Warn(ctx context.Context, msg string, args ...Field) {
	z.logger.Load().Warn(msg, z.toZapFields(ctx, args)...)
}

func (z *ZapLogger) Error(ctx context.Context, msg string, args ...Field) {
	z.logger.Load().Error(msg, z.toZapFields(ctx, args)...)
}

func (z *ZapLogger) Fatal(ctx context.Context, msg string, args ...Field) {
	z.logger.Load().Fatal(msg, z.toZapFields(ctx, args)...)
}

// toZapFields 会有性能损耗
func (z *ZapLogger) toZapFields(ctx context.Context, args []Field) []zap.Field {
	fs := make([]zap.Field, len(args)+1)
	for i, arg := range args {
		fs[i] = zap.Any(arg.Key, arg.Value)
	}
	// 追加 trace_id
	fs[len(fs)-1] = zap.String("trace_id", trace.SpanFromContext(ctx).SpanContext().TraceID().String())
	return fs
}

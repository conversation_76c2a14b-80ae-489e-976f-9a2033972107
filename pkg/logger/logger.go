package logger

import (
	"context"
)

type Field struct {
	Key   string
	Value any
}

type Logger interface {
	Debug(ctx context.Context, msg string, args ...Field)
	Info(ctx context.Context, msg string, args ...Field)
	Warn(ctx context.Context, msg string, args ...Field)
	Error(ctx context.Context, msg string, args ...Field)
	Fatal(ctx context.Context, msg string, args ...Field)
}

func Error(val any) Field {
	return Field{
		Key:   "error",
		Value: val,
	}
}

func String(key string, val string) Field {
	return Field{
		Key:   key,
		Value: val,
	}
}

func Int32(key string, val int32) Field {
	return Field{
		Key:   key,
		Value: val,
	}
}

func Int64(key string, val int64) Field {
	return Field{
		Key:   key,
		Value: val,
	}
}

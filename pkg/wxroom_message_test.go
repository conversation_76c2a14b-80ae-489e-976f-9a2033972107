package pkg

import (
	"fmt"
	"regexp"
	"testing"
)

func TestSendWxRoomMessage(t *testing.T) {
	client := NewClient()
	fmt.Println(client.SendWxRoomMessage(SendWxRoomMessageParam{
		TemplateID: "a90296b8b25bf426ec2023fc04b173a2",
		WxRoomID:   "10959053545941193",
		Vars: []TemplateVar{
			{Key: "msg", Value: "\n\n尊敬的商家朋友您好，您的饿了么店铺鱼你在一起（未来科技城店）收到黑名单顾客再次下单，已为您拦截，请您知晓，订单编号：12345678"},
		},
	}))
}

func Test(t *testing.T) {
	fmt.Println(regexp.MustCompile(`【[^】]*】`).ReplaceAllString("【神犬商家】尊敬的商家朋友您好，您的饿了么店铺神犬（评价帮手）测试店铺收到一条差评，请您及时关注处理。", ""))
}

package dingtalk

import (
	"bytes"
	"context"
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"time"
)

// Client 钉钉客户端
type Client struct {
	config     *Config
	httpClient *http.Client
}

// NewClient 创建钉钉客户端
func NewClient(opts ...Option) *Client {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}

	return &Client{
		config: config,
		httpClient: &http.Client{
			Timeout: config.Timeout,
		},
	}
}

// SendMessage 发送消息
func (c *Client) SendMessage(ctx context.Context, message Message) error {
	if err := message.Validate(); err != nil {
		return err
	}

	// 构建请求体
	req := c.buildSendRequest(message)

	// 构建完整的Webhook URL
	webhookURL, err := c.buildWebhookURL()
	if err != nil {
		return err
	}

	// 发送请求
	return c.sendRequest(ctx, webhookURL, req)
}

// SendTextMessage 发送文本消息
func (c *Client) SendTextMessage(ctx context.Context, content string, atUsers ...string) error {
	message := &TextMessage{
		Content: content,
		AtUsers: atUsers,
	}
	return c.SendMessage(ctx, message)
}

// SendMarkdownMessage 发送Markdown消息
func (c *Client) SendMarkdownMessage(ctx context.Context, title, text string, atUsers ...string) error {
	message := &MarkdownMessage{
		Title:   title,
		Text:    text,
		AtUsers: atUsers,
	}
	return c.SendMessage(ctx, message)
}

// SendLinkMessage 发送链接消息
func (c *Client) SendLinkMessage(ctx context.Context, title, text, messageURL, picURL string) error {
	message := &LinkMessage{
		Title:      title,
		Text:       text,
		MessageURL: messageURL,
		PicURL:     picURL,
	}
	return c.SendMessage(ctx, message)
}

// buildSendRequest 构建发送请求
func (c *Client) buildSendRequest(message Message) *SendRequest {
	req := &SendRequest{
		MsgType: message.GetMsgType(),
	}

	switch msg := message.(type) {
	case *TextMessage:
		req.Text = map[string]string{"content": msg.Content}
		if len(msg.AtUsers) > 0 || msg.AtAll {
			req.At = &AtInfo{
				AtMobiles: msg.AtUsers,
				IsAtAll:   msg.AtAll,
			}
		}
	case *MarkdownMessage:
		req.Markdown = map[string]string{
			"title": msg.Title,
			"text":  msg.Text,
		}
		if len(msg.AtUsers) > 0 || msg.AtAll {
			req.At = &AtInfo{
				AtMobiles: msg.AtUsers,
				IsAtAll:   msg.AtAll,
			}
		}
	case *LinkMessage:
		req.Link = map[string]string{
			"title":      msg.Title,
			"text":       msg.Text,
			"messageUrl": msg.MessageURL,
			"picUrl":     msg.PicURL,
		}
	case *ActionCardMessage:
		req.ActionCard = map[string]string{
			"title":          msg.Title,
			"text":           msg.Text,
			"singleTitle":    msg.SingleTitle,
			"singleURL":      msg.SingleURL,
			"btnOrientation": msg.BtnOrientation,
			"hideAvatar":     msg.HideAvatar,
		}
	case *FeedCardMessage:
		req.FeedCard = map[string]interface{}{
			"links": msg.Links,
		}
	}

	return req
}

// buildWebhookURL 构建Webhook URL
func (c *Client) buildWebhookURL() (string, error) {
	if c.config.WebhookURL == "" {
		return "", ErrEmptyWebhookURL
	}

	// 如果没有密钥，直接返回Webhook URL
	if c.config.Secret == "" {
		return c.config.WebhookURL, nil
	}

	// 生成签名
	timestamp := time.Now().UnixMilli()
	sign, err := c.generateSign(timestamp)
	if err != nil {
		return "", err
	}

	// 构建带签名的URL
	u, err := url.Parse(c.config.WebhookURL)
	if err != nil {
		return "", fmt.Errorf("解析Webhook URL失败: %w", err)
	}

	query := u.Query()
	query.Set("timestamp", strconv.FormatInt(timestamp, 10))
	query.Set("sign", sign)
	u.RawQuery = query.Encode()

	return u.String(), nil
}

// generateSign 生成签名
func (c *Client) generateSign(timestamp int64) (string, error) {
	if c.config.Secret == "" {
		return "", ErrEmptySecret
	}

	stringToSign := fmt.Sprintf("%d\n%s", timestamp, c.config.Secret)
	h := hmac.New(sha256.New, []byte(c.config.Secret))
	h.Write([]byte(stringToSign))
	signature := base64.StdEncoding.EncodeToString(h.Sum(nil))
	return url.QueryEscape(signature), nil
}

// sendRequest 发送HTTP请求
func (c *Client) sendRequest(ctx context.Context, webhookURL string, req *SendRequest) error {
	var lastErr error

	for i := 0; i <= c.config.RetryCount; i++ {
		if i > 0 {
			time.Sleep(c.config.RetryInterval)
		}

		err := c.doRequest(ctx, webhookURL, req)
		if err == nil {
			return nil
		}

		lastErr = err
	}

	return fmt.Errorf("发送钉钉消息失败，重试%d次后仍然失败: %w", c.config.RetryCount, lastErr)
}

// doRequest 执行HTTP请求
func (c *Client) doRequest(ctx context.Context, webhookURL string, req *SendRequest) error {
	// 序列化请求体
	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("序列化请求体失败: %w", err)
	}

	// 创建HTTP请求
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, webhookURL, bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("创建HTTP请求失败: %w", err)
	}

	httpReq.Header.Set("Content-Type", "application/json")

	// 发送请求
	resp, err := c.httpClient.Do(httpReq)
	if err != nil {
		return fmt.Errorf("发送HTTP请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应体失败: %w", err)
	}

	// 解析响应
	var sendResp SendResponse
	if err := json.Unmarshal(respBody, &sendResp); err != nil {
		return fmt.Errorf("解析响应失败: %w", err)
	}

	// 检查响应状态
	if !sendResp.IsSuccess() {
		return fmt.Errorf("钉钉API返回错误: code=%d, msg=%s", sendResp.ErrCode, sendResp.ErrMsg)
	}

	return nil
}

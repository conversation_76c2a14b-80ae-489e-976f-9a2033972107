package dingtalk

import (
	"context"
	"testing"
	"time"
)

func TestTextMessage_Validate(t *testing.T) {
	tests := []struct {
		name    string
		message *TextMessage
		wantErr bool
	}{
		{
			name: "valid text message",
			message: &TextMessage{
				Content: "Hello, World!",
				AtUsers: []string{"13800138000"},
			},
			wantErr: false,
		},
		{
			name: "empty content",
			message: &TextMessage{
				Content: "",
				AtUsers: []string{"13800138000"},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.message.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("TextMessage.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestMarkdownMessage_Validate(t *testing.T) {
	tests := []struct {
		name    string
		message *MarkdownMessage
		wantErr bool
	}{
		{
			name: "valid markdown message",
			message: &MarkdownMessage{
				Title: "Test Title",
				Text:  "## Test Content",
			},
			wantErr: false,
		},
		{
			name: "empty title",
			message: &MarkdownMessage{
				Title: "",
				Text:  "## Test Content",
			},
			wantErr: true,
		},
		{
			name: "empty text",
			message: &MarkdownMessage{
				Title: "Test Title",
				Text:  "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.message.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("MarkdownMessage.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestLinkMessage_Validate(t *testing.T) {
	tests := []struct {
		name    string
		message *LinkMessage
		wantErr bool
	}{
		{
			name: "valid link message",
			message: &LinkMessage{
				Title:      "Test Link",
				Text:       "Click here",
				MessageURL: "https://example.com",
				PicURL:     "https://example.com/pic.jpg",
			},
			wantErr: false,
		},
		{
			name: "empty title",
			message: &LinkMessage{
				Title:      "",
				Text:       "Click here",
				MessageURL: "https://example.com",
			},
			wantErr: true,
		},
		{
			name: "empty message url",
			message: &LinkMessage{
				Title:      "Test Link",
				Text:       "Click here",
				MessageURL: "",
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := tt.message.Validate()
			if (err != nil) != tt.wantErr {
				t.Errorf("LinkMessage.Validate() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestDefaultConfig(t *testing.T) {
	config := DefaultConfig()
	
	if config.Timeout != 30*time.Second {
		t.Errorf("Expected timeout 30s, got %v", config.Timeout)
	}
	
	if config.RetryCount != 3 {
		t.Errorf("Expected retry count 3, got %d", config.RetryCount)
	}
	
	if config.RetryInterval != 100*time.Millisecond {
		t.Errorf("Expected retry interval 100ms, got %v", config.RetryInterval)
	}
}

func TestNewClient(t *testing.T) {
	client := NewClient(
		WithWebhookURL("https://example.com/webhook"),
		WithSecret("test_secret"),
		WithTimeout(10*time.Second),
		WithRetry(5, 200*time.Millisecond),
	)
	
	if client == nil {
		t.Error("Expected client to be created, got nil")
	}
	
	if client.config.WebhookURL != "https://example.com/webhook" {
		t.Errorf("Expected webhook URL to be set, got %s", client.config.WebhookURL)
	}
	
	if client.config.Secret != "test_secret" {
		t.Errorf("Expected secret to be set, got %s", client.config.Secret)
	}
}

func TestDefaultDingConfigProvider(t *testing.T) {
	provider := NewDefaultDingConfigProvider()
	
	// Test adding config
	config := CreateDingConfig(1, "test", "token", "secret")
	provider.AddConfig(config)
	
	// Test getting config
	ctx := context.Background()
	retrievedConfig, err := provider.GetDingConfig(ctx, 1)
	if err != nil {
		t.Errorf("Expected no error, got %v", err)
	}
	
	if retrievedConfig.ID != 1 {
		t.Errorf("Expected ID 1, got %d", retrievedConfig.ID)
	}
	
	if retrievedConfig.Name != "test" {
		t.Errorf("Expected name 'test', got %s", retrievedConfig.Name)
	}
	
	// Test getting non-existent config
	_, err = provider.GetDingConfig(ctx, 999)
	if err != ErrDingConfigNotFound {
		t.Errorf("Expected ErrDingConfigNotFound, got %v", err)
	}
}

func TestCreateDingConfig(t *testing.T) {
	config := CreateDingConfig(1, "test", "token", "secret")
	
	if config.ID != 1 {
		t.Errorf("Expected ID 1, got %d", config.ID)
	}
	
	if config.Name != "test" {
		t.Errorf("Expected name 'test', got %s", config.Name)
	}
	
	if config.AccessToken != "token" {
		t.Errorf("Expected access token 'token', got %s", config.AccessToken)
	}
	
	if config.Secret != "secret" {
		t.Errorf("Expected secret 'secret', got %s", config.Secret)
	}
	
	if config.CreateTime == 0 {
		t.Error("Expected create time to be set")
	}
	
	if config.UpdateTime == 0 {
		t.Error("Expected update time to be set")
	}
}

func TestSendResponse_IsSuccess(t *testing.T) {
	tests := []struct {
		name     string
		response *SendResponse
		want     bool
	}{
		{
			name: "success response",
			response: &SendResponse{
				ErrCode: 0,
				ErrMsg:  "ok",
			},
			want: true,
		},
		{
			name: "error response",
			response: &SendResponse{
				ErrCode: 400,
				ErrMsg:  "bad request",
			},
			want: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := tt.response.IsSuccess(); got != tt.want {
				t.Errorf("SendResponse.IsSuccess() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestService_SendMessageByDingID_InvalidID(t *testing.T) {
	provider := NewDefaultDingConfigProvider()
	service := NewService(provider)
	
	ctx := context.Background()
	message := &TextMessage{Content: "test"}
	
	// Test with invalid ID
	err := service.SendMessageByDingID(ctx, 0, message)
	if err != ErrInvalidDingID {
		t.Errorf("Expected ErrInvalidDingID, got %v", err)
	}
	
	err = service.SendMessageByDingID(ctx, -1, message)
	if err != ErrInvalidDingID {
		t.Errorf("Expected ErrInvalidDingID, got %v", err)
	}
}

func TestService_SendMessageByDingID_ConfigNotFound(t *testing.T) {
	provider := NewDefaultDingConfigProvider()
	service := NewService(provider)
	
	ctx := context.Background()
	message := &TextMessage{Content: "test"}
	
	// Test with non-existent config
	err := service.SendMessageByDingID(ctx, 999, message)
	if err == nil {
		t.Error("Expected error for non-existent config")
	}
}

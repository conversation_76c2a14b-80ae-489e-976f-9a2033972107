# DingTalk 钉钉消息发送包

这个包提供了完整的钉钉机器人消息发送功能，支持从数据库中的 `sq_ding` 表读取配置并发送各种类型的消息。

## 功能特性

- 支持多种消息类型：文本、Markdown、链接、ActionCard、FeedCard
- 支持钉钉机器人签名验证
- 支持@指定用户或@所有人
- 支持重试机制和超时控制
- 支持批量发送消息
- 与项目数据库模型完全集成
- 提供简单易用的API接口


## 快速开始

### 1. 基本使用

```go
package main

import (
    "context"
    "log"
    
    "enter/pkg/dingtalk"
    "enter/internal/repository"
    "enter/ioc"
)

func main() {
    // 初始化数据库连接
    db := ioc.CreateGormMysql()
    
    // 创建钉钉仓储
    dingDao := dao.NewGORMDingDao(db)
    dingRepo := repository.NewCacheDingRepo(dingDao)
    
    // 创建钉钉服务
    dingService := dingtalk.NewDingTalkService(dingRepo)
    
    // 发送文本消息
    err := dingService.SendTextMessage(
        context.Background(),
        1, // 钉钉配置ID
        "Hello, DingTalk!",
        "13800138000", // @指定用户手机号
    )
    if err != nil {
        log.Printf("发送消息失败: %v", err)
    }
}
```

### 2. 发送不同类型的消息

```go
// 发送Markdown消息
err := dingService.SendMarkdownMessage(
    ctx,
    1,
    "项目更新通知",
    "## 更新内容\n- 新增功能A\n- 修复bug B",
    "13800138000",
)

// 发送链接消息
err := dingService.SendLinkMessage(
    ctx,
    1,
    "新功能上线",
    "点击查看详情",
    "https://example.com",
    "https://example.com/pic.jpg",
)

// 发送ActionCard消息
err := dingService.SendActionCardMessage(
    ctx,
    1,
    "代码审查",
    "请审查PR #123",
    "查看详情",
    "https://github.com/repo/pull/123",
)
```

### 3. 批量发送消息

```go
// 批量发送到多个钉钉群
dingIDs := []int64{1, 2, 3}
errors := dingService.BatchSendTextMessage(
    ctx,
    dingIDs,
    "系统维护通知",
    "13800138000",
)

// 检查发送结果
for i, err := range errors {
    if err != nil {
        log.Printf("发送到群%d失败: %v", dingIDs[i], err)
    }
}
```

### 4. 使用低级API

```go
// 直接使用客户端
client := dingtalk.NewClient(
    dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=xxx"),
    dingtalk.WithSecret("your_secret"),
    dingtalk.WithTimeout(30*time.Second),
    dingtalk.WithRetry(3, 100*time.Millisecond),
)

err := client.SendTextMessage(ctx, "Hello!", "13800138000")
```

## API 参考

### DingTalkService

主要的服务接口，提供高级API：

- `SendTextMessage(ctx, dingID, content, atUsers...)` - 发送文本消息
- `SendMarkdownMessage(ctx, dingID, title, text, atUsers...)` - 发送Markdown消息
- `SendLinkMessage(ctx, dingID, title, text, url, picURL)` - 发送链接消息
- `SendActionCardMessage(ctx, dingID, title, text, btnTitle, btnURL)` - 发送ActionCard消息
- `SendMessage(ctx, dingID, message)` - 发送自定义消息
- `BatchSendTextMessage(ctx, dingIDs, content, atUsers...)` - 批量发送文本消息
- `BatchSendMessage(ctx, dingIDs, message)` - 批量发送自定义消息

### Client

低级客户端接口：

- `SendMessage(ctx, message)` - 发送消息
- `SendTextMessage(ctx, content, atUsers...)` - 发送文本消息
- `SendMarkdownMessage(ctx, title, text, atUsers...)` - 发送Markdown消息
- `SendLinkMessage(ctx, title, text, url, picURL)` - 发送链接消息

### 消息类型

- `TextMessage` - 文本消息
- `MarkdownMessage` - Markdown消息
- `LinkMessage` - 链接消息
- `ActionCardMessage` - ActionCard消息
- `FeedCardMessage` - FeedCard消息

## 配置选项

```go
// 客户端配置选项
dingtalk.WithWebhookURL("webhook_url")     // 设置Webhook地址
dingtalk.WithSecret("secret")              // 设置加密密钥
dingtalk.WithTimeout(30*time.Second)       // 设置超时时间
dingtalk.WithRetry(3, 100*time.Millisecond) // 设置重试次数和间隔
```

## 错误处理

包中定义了常见的错误类型：

- `ErrEmptyContent` - 内容为空
- `ErrEmptyTitle` - 标题为空
- `ErrEmptyURL` - URL为空
- `ErrInvalidDingID` - 无效的钉钉ID
- `ErrDingConfigNotFound` - 钉钉配置未找到
- `ErrSendFailed` - 发送失败

## 注意事项

1. 确保数据库中的 `sq_ding` 表有正确的配置数据
2. `access_token` 和 `secret` 需要从钉钉机器人管理页面获取
3. 发送消息时注意频率限制，避免被钉钉限流
4. 批量发送时建议添加适当的延时
5. 生产环境中建议启用重试机制

## 示例

更多使用示例请参考 `example_test.go` 文件。

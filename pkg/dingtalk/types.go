package dingtalk

// MessageType 消息类型
type MessageType string

const (
	// MessageTypeText 文本消息
	MessageTypeText MessageType = "text"
	// MessageTypeMarkdown Markdown消息
	MessageTypeMarkdown MessageType = "markdown"
	// MessageTypeLink 链接消息
	MessageTypeLink MessageType = "link"
	// MessageTypeActionCard 整体跳转ActionCard消息
	MessageTypeActionCard MessageType = "actionCard"
	// MessageTypeFeedCard FeedCard消息
	MessageTypeFeedCard MessageType = "feedCard"
)

// Message 钉钉消息接口
type Message interface {
	GetMsgType() MessageType
	Validate() error
}

// TextMessage 文本消息
type TextMessage struct {
	Content string   `json:"content"`
	AtAll   bool     `json:"atAll,omitempty"`
	AtUsers []string `json:"atMobiles,omitempty"`
}

func (m *TextMessage) GetMsgType() MessageType {
	return MessageTypeText
}

func (m *TextMessage) Validate() error {
	if m.Content == "" {
		return ErrEmptyContent
	}
	return nil
}

// MarkdownMessage Markdown消息
type MarkdownMessage struct {
	Title   string   `json:"title"`
	Text    string   `json:"text"`
	AtAll   bool     `json:"atAll,omitempty"`
	AtUsers []string `json:"atMobiles,omitempty"`
}

func (m *MarkdownMessage) GetMsgType() MessageType {
	return MessageTypeMarkdown
}

func (m *MarkdownMessage) Validate() error {
	if m.Title == "" {
		return ErrEmptyTitle
	}
	if m.Text == "" {
		return ErrEmptyContent
	}
	return nil
}

// LinkMessage 链接消息
type LinkMessage struct {
	Title      string `json:"title"`
	Text       string `json:"text"`
	MessageURL string `json:"messageUrl"`
	PicURL     string `json:"picUrl,omitempty"`
}

func (m *LinkMessage) GetMsgType() MessageType {
	return MessageTypeLink
}

func (m *LinkMessage) Validate() error {
	if m.Title == "" {
		return ErrEmptyTitle
	}
	if m.Text == "" {
		return ErrEmptyContent
	}
	if m.MessageURL == "" {
		return ErrEmptyURL
	}
	return nil
}

// ActionCardMessage 整体跳转ActionCard消息
type ActionCardMessage struct {
	Title          string `json:"title"`
	Text           string `json:"text"`
	SingleTitle    string `json:"singleTitle"`
	SingleURL      string `json:"singleURL"`
	BtnOrientation string `json:"btnOrientation,omitempty"` // 0-按钮竖直排列，1-按钮横向排列
	HideAvatar     string `json:"hideAvatar,omitempty"`     // 0-正常发消息者头像，1-隐藏发消息者头像
}

func (m *ActionCardMessage) GetMsgType() MessageType {
	return MessageTypeActionCard
}

func (m *ActionCardMessage) Validate() error {
	if m.Title == "" {
		return ErrEmptyTitle
	}
	if m.Text == "" {
		return ErrEmptyContent
	}
	if m.SingleTitle == "" {
		return ErrEmptyTitle
	}
	if m.SingleURL == "" {
		return ErrEmptyURL
	}
	return nil
}

// FeedCardLink FeedCard链接
type FeedCardLink struct {
	Title      string `json:"title"`
	MessageURL string `json:"messageURL"`
	PicURL     string `json:"picURL,omitempty"`
}

// FeedCardMessage FeedCard消息
type FeedCardMessage struct {
	Links []FeedCardLink `json:"links"`
}

func (m *FeedCardMessage) GetMsgType() MessageType {
	return MessageTypeFeedCard
}

func (m *FeedCardMessage) Validate() error {
	if len(m.Links) == 0 {
		return ErrEmptyLinks
	}
	for _, link := range m.Links {
		if link.Title == "" {
			return ErrEmptyTitle
		}
		if link.MessageURL == "" {
			return ErrEmptyURL
		}
	}
	return nil
}

// DingConfig 钉钉配置信息
type DingConfig struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	AccessToken string `json:"access_token"`
	Secret      string `json:"secret"`
	CreateTime  int64  `json:"create_time"`
	UpdateTime  int64  `json:"update_time"`
}

// SendRequest 发送请求结构
type SendRequest struct {
	MsgType MessageType `json:"msgtype"`
	Text    interface{} `json:"text,omitempty"`
	Link    interface{} `json:"link,omitempty"`
	Markdown interface{} `json:"markdown,omitempty"`
	ActionCard interface{} `json:"actionCard,omitempty"`
	FeedCard interface{} `json:"feedCard,omitempty"`
	At      *AtInfo     `json:"at,omitempty"`
}

// AtInfo @信息
type AtInfo struct {
	AtMobiles []string `json:"atMobiles,omitempty"`
	AtUserIds []string `json:"atUserIds,omitempty"`
	IsAtAll   bool     `json:"isAtAll,omitempty"`
}

// SendResponse 发送响应结构
type SendResponse struct {
	ErrCode int    `json:"errcode"`
	ErrMsg  string `json:"errmsg"`
}

// IsSuccess 判断是否发送成功
func (r *SendResponse) IsSuccess() bool {
	return r.ErrCode == 0
}

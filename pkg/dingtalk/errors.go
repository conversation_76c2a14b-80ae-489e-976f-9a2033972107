package dingtalk

import "errors"

var (
	// ErrEmptyContent 内容为空错误
	ErrEmptyContent = errors.New("消息内容不能为空")
	// ErrEmptyTitle 标题为空错误
	ErrEmptyTitle = errors.New("消息标题不能为空")
	// ErrEmptyURL URL为空错误
	ErrEmptyURL = errors.New("消息URL不能为空")
	// ErrEmptyLinks 链接为空错误
	ErrEmptyLinks = errors.New("FeedCard链接不能为空")
	// ErrEmptyWebhookURL Webhook地址为空错误
	ErrEmptyWebhookURL = errors.New("Webhook地址不能为空")
	// ErrEmptySecret 密钥为空错误
	ErrEmptySecret = errors.New("加密密钥不能为空")
	// ErrInvalidDingID 无效的钉钉ID错误
	ErrInvalidDingID = errors.New("无效的钉钉ID")
	// ErrDingConfigNotFound 钉钉配置未找到错误
	ErrDingConfigNotFound = errors.New("钉钉配置未找到")
	// ErrSendFailed 发送失败错误
	ErrSendFailed = errors.New("钉钉消息发送失败")
)

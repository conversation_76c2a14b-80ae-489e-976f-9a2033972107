package dingtalk_test

import (
	"context"
	"fmt"
	"log"

	"enter/pkg/dingtalk"
)

// ExampleClient_SendTextMessage 发送文本消息示例
func ExampleClient_SendTextMessage() {
	// 创建钉钉客户端
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 发送文本消息
	err := client.SendTextMessage(context.Background(), "Hello, DingTalk!", "13800138000")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	fmt.Println("消息发送成功")
}

// ExampleClient_SendMarkdownMessage 发送Markdown消息示例
func ExampleClient_SendMarkdownMessage() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	title := "项目更新通知"
	text := `
## 项目更新通知
### 更新内容
- 新增钉钉消息发送功能
- 优化数据库查询性能
- 修复已知bug

### 发布时间
2024-01-01 10:00:00

> 请相关同事及时关注更新内容
`

	err := client.SendMarkdownMessage(context.Background(), title, text, "13800138000")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	fmt.Println("Markdown消息发送成功")
}

// ExampleDingTalkService 使用DingTalkService示例
func ExampleDingTalkService() {
	// 注意: 这里需要实际的repository实现
	// var dingRepo repository.DingRepo = yourDingRepoImplementation

	// 创建钉钉服务
	// service := dingtalk.NewDingTalkService(dingRepo)

	// 发送文本消息
	// err := service.SendTextMessage(context.Background(), 1, "Hello from DingTalkService!")
	// if err != nil {
	//     log.Printf("发送消息失败: %v", err)
	//     return
	// }

	fmt.Println("DingTalkService示例 (需要实际的repository实现)")
}

// ExampleCustomMessage 发送自定义消息示例
func ExampleCustomMessage() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 创建链接消息
	linkMessage := &dingtalk.LinkMessage{
		Title:      "新功能上线通知",
		Text:       "我们很高兴地宣布新功能已经上线，点击查看详情。",
		MessageURL: "https://example.com/new-feature",
		PicURL:     "https://example.com/image.jpg",
	}

	err := client.SendMessage(context.Background(), linkMessage)
	if err != nil {
		log.Printf("发送自定义消息失败: %v", err)
		return
	}

	fmt.Println("自定义消息发送成功")
}

// ExampleActionCard 发送ActionCard消息示例
func ExampleActionCard() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 创建ActionCard消息
	actionCard := &dingtalk.ActionCardMessage{
		Title:       "代码审查请求",
		Text:        "## 代码审查请求\n\n**提交者**: 张三\n**分支**: feature/new-api\n**描述**: 新增用户管理API\n\n请及时进行代码审查。",
		SingleTitle: "查看详情",
		SingleURL:   "https://github.com/example/repo/pull/123",
	}

	err := client.SendMessage(context.Background(), actionCard)
	if err != nil {
		log.Printf("发送ActionCard消息失败: %v", err)
		return
	}

	fmt.Println("ActionCard消息发送成功")
}

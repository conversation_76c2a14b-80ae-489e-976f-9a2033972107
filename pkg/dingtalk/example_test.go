package dingtalk_test

import (
	"context"
	"fmt"
	"log"

	"enter/pkg/dingtalk"
)

// ExampleClient_SendTextMessage 发送文本消息示例
func ExampleClient_SendTextMessage() {
	// 创建钉钉客户端
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 发送文本消息
	err := client.SendTextMessage(context.Background(), "Hello, DingTalk!", "13800138000")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	fmt.Println("消息发送成功")
}

// ExampleClient_SendMarkdownMessage 发送Markdown消息示例
func ExampleClient_SendMarkdownMessage() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	title := "项目更新通知"
	text := `
## 项目更新通知
### 更新内容
- 新增钉钉消息发送功能
- 优化数据库查询性能
- 修复已知bug

### 发布时间
2024-01-01 10:00:00

> 请相关同事及时关注更新内容
`

	err := client.SendMarkdownMessage(context.Background(), title, text, "13800138000")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	fmt.Println("Markdown消息发送成功")
}

// ExampleService_SendMessageByDingID 根据钉钉ID发送消息示例
func ExampleService_SendMessageByDingID() {
	// 创建配置提供者
	configProvider := dingtalk.NewDefaultDingConfigProvider()
	
	// 添加钉钉配置
	config := dingtalk.CreateDingConfig(
		1,
		"测试群",
		"your_access_token",
		"your_secret",
	)
	configProvider.AddConfig(config)

	// 创建钉钉服务
	service := dingtalk.NewService(configProvider)

	// 发送文本消息
	err := service.SendTextMessageByDingID(context.Background(), 1, "Hello from Service!", "13800138000")
	if err != nil {
		log.Printf("发送消息失败: %v", err)
		return
	}

	fmt.Println("服务消息发送成功")
}

// ExampleDingTalkService 使用DingTalkService示例
func ExampleDingTalkService() {
	// 注意: 这里需要实际的repository实现
	// var dingRepo repository.DingRepo = yourDingRepoImplementation

	// 创建钉钉服务
	// service := dingtalk.NewDingTalkService(dingRepo)

	// 发送文本消息
	// err := service.SendTextMessage(context.Background(), 1, "Hello from DingTalkService!")
	// if err != nil {
	//     log.Printf("发送消息失败: %v", err)
	//     return
	// }

	fmt.Println("DingTalkService示例 (需要实际的repository实现)")
}

// ExampleBatchSend 批量发送消息示例
func ExampleBatchSend() {
	configProvider := dingtalk.NewDefaultDingConfigProvider()
	
	// 添加多个钉钉配置
	configs := []*dingtalk.DingConfig{
		dingtalk.CreateDingConfig(1, "群1", "token1", "secret1"),
		dingtalk.CreateDingConfig(2, "群2", "token2", "secret2"),
		dingtalk.CreateDingConfig(3, "群3", "token3", "secret3"),
	}
	
	for _, config := range configs {
		configProvider.AddConfig(config)
	}

	service := dingtalk.NewService(configProvider)

	// 批量发送消息
	dingIDs := []int64{1, 2, 3}
	errors := service.BatchSendTextMessageByDingIDs(
		context.Background(),
		dingIDs,
		"批量消息通知：系统维护将在今晚进行",
		"13800138000",
	)

	// 检查发送结果
	for i, err := range errors {
		if err != nil {
			log.Printf("发送到群%d失败: %v", dingIDs[i], err)
		} else {
			fmt.Printf("发送到群%d成功\n", dingIDs[i])
		}
	}
}

// ExampleCustomMessage 发送自定义消息示例
func ExampleCustomMessage() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 创建链接消息
	linkMessage := &dingtalk.LinkMessage{
		Title:      "新功能上线通知",
		Text:       "我们很高兴地宣布新功能已经上线，点击查看详情。",
		MessageURL: "https://example.com/new-feature",
		PicURL:     "https://example.com/image.jpg",
	}

	err := client.SendMessage(context.Background(), linkMessage)
	if err != nil {
		log.Printf("发送自定义消息失败: %v", err)
		return
	}

	fmt.Println("自定义消息发送成功")
}

// ExampleActionCard 发送ActionCard消息示例
func ExampleActionCard() {
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL("https://oapi.dingtalk.com/robot/send?access_token=your_access_token"),
		dingtalk.WithSecret("your_secret"),
	)

	// 创建ActionCard消息
	actionCard := &dingtalk.ActionCardMessage{
		Title:       "代码审查请求",
		Text:        "## 代码审查请求\n\n**提交者**: 张三\n**分支**: feature/new-api\n**描述**: 新增用户管理API\n\n请及时进行代码审查。",
		SingleTitle: "查看详情",
		SingleURL:   "https://github.com/example/repo/pull/123",
	}

	err := client.SendMessage(context.Background(), actionCard)
	if err != nil {
		log.Printf("发送ActionCard消息失败: %v", err)
		return
	}

	fmt.Println("ActionCard消息发送成功")
}

package dingtalk

import (
	"context"
	"fmt"

	"enter/internal/domain"
	"enter/internal/repository"
)

// DomainDingConfigProvider 基于domain层的钉钉配置提供者
type DomainDingConfigProvider struct {
	dingRepo repository.DingRepo
}

// NewDomainDingConfigProvider 创建基于domain层的钉钉配置提供者
func NewDomainDingConfigProvider(dingRepo repository.DingRepo) *DomainDingConfigProvider {
	return &DomainDingConfigProvider{
		dingRepo: dingRepo,
	}
}

// GetDingConfig 从domain层获取钉钉配置
func (p *DomainDingConfigProvider) GetDingConfig(ctx context.Context, id int64) (*DingConfig, error) {
	if id <= 0 {
		return nil, ErrInvalidDingID
	}

	// 从repository获取domain对象
	domainDing, err := p.dingRepo.FindByID(ctx, id)
	if err != nil {
		return nil, fmt.Errorf("查询钉钉配置失败: %w", err)
	}

	// 转换为钉钉配置
	return p.domainToDingConfig(domainDing), nil
}

// domainToDingConfig 将domain对象转换为钉钉配置
func (p *DomainDingConfigProvider) domainToDingConfig(d domain.Ding) *DingConfig {
	return &DingConfig{
		ID:          d.ID,
		Name:        d.Name,
		AccessToken: d.AccessToken,
		Secret:      d.Secret,
		CreateTime:  d.CreateTime,
		UpdateTime:  d.UpdateTime,
	}
}

// DingTalkService 钉钉服务封装
type DingTalkService struct {
	service *Service
}

// NewDingTalkService 创建钉钉服务
func NewDingTalkService(dingRepo repository.DingRepo, opts ...Option) *DingTalkService {
	configProvider := NewDomainDingConfigProvider(dingRepo)
	service := NewService(configProvider, opts...)
	
	return &DingTalkService{
		service: service,
	}
}

// SendTextMessage 发送文本消息
func (s *DingTalkService) SendTextMessage(ctx context.Context, dingID int64, content string, atUsers ...string) error {
	return s.service.SendTextMessageByDingID(ctx, dingID, content, atUsers...)
}

// SendMarkdownMessage 发送Markdown消息
func (s *DingTalkService) SendMarkdownMessage(ctx context.Context, dingID int64, title, text string, atUsers ...string) error {
	return s.service.SendMarkdownMessageByDingID(ctx, dingID, title, text, atUsers...)
}

// SendLinkMessage 发送链接消息
func (s *DingTalkService) SendLinkMessage(ctx context.Context, dingID int64, title, text, messageURL, picURL string) error {
	return s.service.SendLinkMessageByDingID(ctx, dingID, title, text, messageURL, picURL)
}

// SendActionCardMessage 发送ActionCard消息
func (s *DingTalkService) SendActionCardMessage(ctx context.Context, dingID int64, title, text, singleTitle, singleURL string) error {
	return s.service.SendActionCardMessageByDingID(ctx, dingID, title, text, singleTitle, singleURL)
}

// SendMessage 发送自定义消息
func (s *DingTalkService) SendMessage(ctx context.Context, dingID int64, message Message) error {
	return s.service.SendMessageByDingID(ctx, dingID, message)
}

// BatchSendTextMessage 批量发送文本消息
func (s *DingTalkService) BatchSendTextMessage(ctx context.Context, dingIDs []int64, content string, atUsers ...string) []error {
	return s.service.BatchSendTextMessageByDingIDs(ctx, dingIDs, content, atUsers...)
}

// BatchSendMessage 批量发送自定义消息
func (s *DingTalkService) BatchSendMessage(ctx context.Context, dingIDs []int64, message Message) []error {
	return s.service.BatchSendMessageByDingIDs(ctx, dingIDs, message)
}

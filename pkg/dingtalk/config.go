package dingtalk

import (
	"time"
)

// Config 钉钉配置
type Config struct {
	// 钉钉机器人Webhook地址
	WebhookURL string
	// 加密密钥
	Secret string
	// 超时时间
	Timeout time.Duration
	// 重试次数
	RetryCount int
	// 重试间隔
	RetryInterval time.Duration
}

// DefaultConfig 返回默认配置
func DefaultConfig() *Config {
	return &Config{
		Timeout:       30 * time.Second,
		RetryCount:    3,
		RetryInterval: 100 * time.Millisecond,
	}
}

// Option 配置选项函数
type Option func(*Config)

// WithWebhookURL 设置Webhook地址
func WithWebhookURL(webhookURL string) Option {
	return func(c *Config) {
		c.WebhookURL = webhookURL
	}
}

// WithSecret 设置加密密钥
func WithSecret(secret string) Option {
	return func(c *Config) {
		c.Secret = secret
	}
}

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) Option {
	return func(c *Config) {
		c.Timeout = timeout
	}
}

// WithRetry 设置重试配置
func WithRetry(count int, interval time.Duration) Option {
	return func(c *Config) {
		c.RetryCount = count
		c.RetryInterval = interval
	}
}

package dingtalk

import (
	"context"
	"fmt"
	"time"
)

// DingConfigProvider 钉钉配置提供者接口
type DingConfigProvider interface {
	// GetDingConfig 根据ID获取钉钉配置
	GetDingConfig(ctx context.Context, id int64) (*DingConfig, error)
}

// Service 钉钉消息服务
type Service struct {
	configProvider DingConfigProvider
	defaultConfig  *Config
}

// NewService 创建钉钉消息服务
func NewService(configProvider DingConfigProvider, opts ...Option) *Service {
	config := DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}

	return &Service{
		configProvider: configProvider,
		defaultConfig:  config,
	}
}

// SendMessageByDingID 根据钉钉ID发送消息
func (s *Service) SendMessageByDingID(ctx context.Context, dingID int64, message Message) error {
	if dingID <= 0 {
		return ErrInvalidDingID
	}

	// 获取钉钉配置
	dingConfig, err := s.configProvider.GetDingConfig(ctx, dingID)
	if err != nil {
		return fmt.Errorf("获取钉钉配置失败: %w", err)
	}

	if dingConfig == nil {
		return ErrDingConfigNotFound
	}

	// 构建Webhook URL
	webhookURL := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s", dingConfig.AccessToken)

	// 创建客户端
	client := NewClient(
		WithWebhookURL(webhookURL),
		WithSecret(dingConfig.Secret),
		WithTimeout(s.defaultConfig.Timeout),
		WithRetry(s.defaultConfig.RetryCount, s.defaultConfig.RetryInterval),
	)

	// 发送消息
	return client.SendMessage(ctx, message)
}

// SendTextMessageByDingID 根据钉钉ID发送文本消息
func (s *Service) SendTextMessageByDingID(ctx context.Context, dingID int64, content string, atUsers ...string) error {
	message := &TextMessage{
		Content: content,
		AtUsers: atUsers,
	}
	return s.SendMessageByDingID(ctx, dingID, message)
}

// SendMarkdownMessageByDingID 根据钉钉ID发送Markdown消息
func (s *Service) SendMarkdownMessageByDingID(ctx context.Context, dingID int64, title, text string, atUsers ...string) error {
	message := &MarkdownMessage{
		Title:   title,
		Text:    text,
		AtUsers: atUsers,
	}
	return s.SendMessageByDingID(ctx, dingID, message)
}

// SendLinkMessageByDingID 根据钉钉ID发送链接消息
func (s *Service) SendLinkMessageByDingID(ctx context.Context, dingID int64, title, text, messageURL, picURL string) error {
	message := &LinkMessage{
		Title:      title,
		Text:       text,
		MessageURL: messageURL,
		PicURL:     picURL,
	}
	return s.SendMessageByDingID(ctx, dingID, message)
}

// SendActionCardMessageByDingID 根据钉钉ID发送ActionCard消息
func (s *Service) SendActionCardMessageByDingID(ctx context.Context, dingID int64, title, text, singleTitle, singleURL string) error {
	message := &ActionCardMessage{
		Title:       title,
		Text:        text,
		SingleTitle: singleTitle,
		SingleURL:   singleURL,
	}
	return s.SendMessageByDingID(ctx, dingID, message)
}

// BatchSendMessageByDingIDs 批量发送消息到多个钉钉群
func (s *Service) BatchSendMessageByDingIDs(ctx context.Context, dingIDs []int64, message Message) []error {
	if len(dingIDs) == 0 {
		return []error{ErrInvalidDingID}
	}

	errors := make([]error, len(dingIDs))
	for i, dingID := range dingIDs {
		errors[i] = s.SendMessageByDingID(ctx, dingID, message)
	}

	return errors
}

// BatchSendTextMessageByDingIDs 批量发送文本消息到多个钉钉群
func (s *Service) BatchSendTextMessageByDingIDs(ctx context.Context, dingIDs []int64, content string, atUsers ...string) []error {
	message := &TextMessage{
		Content: content,
		AtUsers: atUsers,
	}
	return s.BatchSendMessageByDingIDs(ctx, dingIDs, message)
}

// DefaultDingConfigProvider 默认钉钉配置提供者实现
type DefaultDingConfigProvider struct {
	configs map[int64]*DingConfig
}

// NewDefaultDingConfigProvider 创建默认钉钉配置提供者
func NewDefaultDingConfigProvider() *DefaultDingConfigProvider {
	return &DefaultDingConfigProvider{
		configs: make(map[int64]*DingConfig),
	}
}

// AddConfig 添加钉钉配置
func (p *DefaultDingConfigProvider) AddConfig(config *DingConfig) {
	if config != nil && config.ID > 0 {
		p.configs[config.ID] = config
	}
}

// GetDingConfig 获取钉钉配置
func (p *DefaultDingConfigProvider) GetDingConfig(ctx context.Context, id int64) (*DingConfig, error) {
	config, exists := p.configs[id]
	if !exists {
		return nil, ErrDingConfigNotFound
	}
	return config, nil
}

// DatabaseDingConfigProvider 数据库钉钉配置提供者
type DatabaseDingConfigProvider struct {
	repo DingRepository
}

// DingRepository 钉钉仓储接口
type DingRepository interface {
	FindByID(ctx context.Context, id int64) (*DingConfig, error)
}

// NewDatabaseDingConfigProvider 创建数据库钉钉配置提供者
func NewDatabaseDingConfigProvider(repo DingRepository) *DatabaseDingConfigProvider {
	return &DatabaseDingConfigProvider{
		repo: repo,
	}
}

// GetDingConfig 从数据库获取钉钉配置
func (p *DatabaseDingConfigProvider) GetDingConfig(ctx context.Context, id int64) (*DingConfig, error) {
	return p.repo.FindByID(ctx, id)
}

// SimpleDingRepository 简单钉钉仓储实现
type SimpleDingRepository struct {
	findByIDFunc func(ctx context.Context, id int64) (*DingConfig, error)
}

// NewSimpleDingRepository 创建简单钉钉仓储
func NewSimpleDingRepository(findByIDFunc func(ctx context.Context, id int64) (*DingConfig, error)) *SimpleDingRepository {
	return &SimpleDingRepository{
		findByIDFunc: findByIDFunc,
	}
}

// FindByID 根据ID查找钉钉配置
func (r *SimpleDingRepository) FindByID(ctx context.Context, id int64) (*DingConfig, error) {
	if r.findByIDFunc != nil {
		return r.findByIDFunc(ctx, id)
	}
	return nil, ErrDingConfigNotFound
}

// CreateDingConfig 创建钉钉配置
func CreateDingConfig(id int64, name, accessToken, secret string) *DingConfig {
	now := time.Now().Unix()
	return &DingConfig{
		ID:          id,
		Name:        name,
		AccessToken: accessToken,
		Secret:      secret,
		CreateTime:  now,
		UpdateTime:  now,
	}
}

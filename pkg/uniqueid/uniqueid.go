package uniqueid

import (
	"fmt"
	"math/rand"
	"time"
)

func Generate() string {
	seed := time.Now().UnixNano()

	// 创建一个新的随机数源，并使用该种子初始化
	source := rand.NewSource(seed)

	// 使用新的随机数源创建一个新的随机数生成器
	random := rand.New(source)
	return fmt.Sprintf("%d%0.3d", time.Now().UnixNano(), random.Intn(1000))
}

func GetRandomString(l int) string {
	str := "0123456789AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz"
	bytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < l; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

func GetRandomLotteryCode() string {
	str := "0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	bytes := []byte(str)
	var result []byte
	r := rand.New(rand.NewSource(time.Now().UnixNano()))
	for i := 0; i < 5; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}

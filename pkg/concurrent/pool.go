package concurrent

import (
	"errors"
	"sync"
)

// ErrPoolClosed is returned when trying to add a task to a closed pool.
var ErrPoolClosed = errors.New("pool closed")

// Pool manages a pool of goroutines to execute tasks concurrently.
type Pool struct {
	tasks      chan func()
	wg         sync.WaitGroup
	maxWorkers int
	isClosed   bool
}

// NewPool creates a new pool with a specified number of maximum workers.
func NewPool(maxWorkers int) *Pool {
	return &Pool{
		tasks:      make(chan func()),
		maxWorkers: maxWorkers,
	}
}

// Start starts the pool and begins listening for tasks.
func (p *Pool) Start() {
	for i := 0; i < p.maxWorkers; i++ {
		p.wg.Add(1)
		go p.worker()
	}
}

// worker is the goroutine that executes tasks from the tasks channel.
func (p *Pool) worker() {
	defer p.wg.Done()
	for task := range p.tasks {
		task()
	}
}

// Submit adds a task to the pool.
func (p *Pool) Submit(task func()) error {
	if p.isClosed {
		return ErrPoolClosed
	}
	p.tasks <- task
	return nil
}

// Close closes the pool and waits for all workers to finish their tasks.
func (p *Pool) Close() {
	p.isClosed = true
	close(p.tasks)
	p.wg.Wait()
}

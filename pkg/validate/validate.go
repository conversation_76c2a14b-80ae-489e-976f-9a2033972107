package validate

import (
	"errors"

	"github.com/thedevsaddam/govalidator"
)

type Validator interface {
	Rules() map[string][]string
	Messages() map[string][]string
}

func Validate(data Validator) error {
	opts := govalidator.Options{
		Data:          data,
		Rules:         data.Rules(),
		Messages:      data.Messages(),
		TagIdentifier: "valid",
	}
	return ErrValid(govalidator.New(opts).ValidateStruct()).Error()
}

type ErrValid map[string][]string

// Error 只取第一个
func (e ErrValid) Error() error {
	msg := ""
	for _, err := range e {
		if len(err) > 0 {
			msg = err[0]
			break
		}
	}
	if msg == "" {
		return nil
	}
	return errors.New(msg)
}

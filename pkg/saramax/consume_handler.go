package saramax

import (
	"context"
	"encoding/json"

	"enter/pkg/logger"

	"github.com/IBM/sarama"
)

type Handler[T any] struct {
	logger      logger.Logger
	handlerFunc func(message *sarama.ConsumerMessage, t T) error
}

func NewHandler[T any](logger logger.Logger, handlerFunc func(message *sarama.ConsumerMessage, t T) error) *Handler[T] {
	return &Handler[T]{
		logger:      logger,
		handlerFunc: handlerFunc,
	}
}

func (h *Handler[T]) Setup(session sarama.ConsumerGroupSession) error {
	return nil
}

func (h *Handler[T]) Cleanup(session sarama.ConsumerGroupSession) error {
	return nil
}

func (h *Handler[T]) ConsumeClaim(session sarama.ConsumerGroupSession, claim sarama.ConsumerGroupClaim) error {
	messages := claim.Messages()
	for message := range messages {
		var t T
		err := json.Unmarshal(message.Value, &t)
		if err != nil {
			h.logger.Error(
				context.Background(),
				"解析消息失败",
				logger.Error(err),
				logger.String("topic", message.Topic),
				logger.Int32("partition", message.Partition),
				logger.Int64("offset", message.Offset),
			)
			continue
		}

		err = h.handlerFunc(message, t)
		if err != nil {
			h.logger.Error(
				context.Background(),
				"处理消息失败",
				logger.Error(err),
				logger.String("topic", message.Topic),
				logger.Int32("partition", message.Partition),
				logger.Int64("offset", message.Offset),
			)
		} else {
			// 处理成功，标记消息为已处理
			session.MarkMessage(message, "")
		}

	}
	return nil
}

package saramax

import (
	"context"
	"testing"

	"enter/pkg/logger"

	"github.com/IBM/sarama"
)

type TestMessage struct{}

type TestConsumer struct {
	client sarama.Client
	logger logger.Logger
}

func NewTestConsumer(logger logger.Logger, client sarama.Client) *TestConsumer {
	return &TestConsumer{
		client: client,
		logger: logger,
	}
}

func (t *TestConsumer) Start(ctx context.Context) error {
	cg, err := sarama.NewConsumerGroupFromClient("test", t.client)
	if err != nil {
		return err
	}
	go func() {
		er := cg.Consume(ctx,
			[]string{"test"},
			NewHandler[TestMessage](t.logger, func(message *sarama.ConsumerMessage, t TestMessage) error {
				// 消费
				return nil
			}))
		if er != nil {
			t.logger.Error(context.Background(), "Error from consumer: ", logger.Error(er))
		}
	}()
	return nil
}

func Test(t *testing.T) {
}

# 编码规范

- 每个函数/方法第一个参数必须是 context.Context 


- 方法、函数必须返回 error ，当有多个值的时候，返回 error 的话其他值都是无效的，error == nil 的话其他值都是有效的，因此在调用函数的时候只需要判断 error 是否为nil 即可，不用对值判空


- 实体对象的组装尽量放在 repository 层（ service层也是可以接收的 ）


- redis 的 key 命名：使用 `冒号` 分割


- 查询需要指定 select 的字段，除非需要获取完整的记录。其次不要使用 gorm 的 save 操作，使用 updates 指定字段更新


- 始终遵循依赖注入的原则，不要在 new 等构造函数中构建依赖，由外部在初始化的时候传入


- 接收者推荐使用指针，方法的输入和输出推荐使用结构体
> 结构体很大的时候，避免拷贝开销，可以考虑使用指针


- 参数和返回值不要使用 map 尽量使用结构体替代，可读性更好


- 适当的重复（ 复制-粘贴 ）要比强行抽象要更好一点
> 抽象会导致可读性变差



- service 层方法命名偏业务，repository 、dao层方法命名偏存储


- vo 层代表和前端的交互，包括请求和响应结构体的定义


- 如果预期 struct 只有当前上下文使用，则使用局部结构体定义，遵循最小原则


- dao 层的方法名尽量小而具体，不要定义一些大而全的方法，比如 FindById 可读性更好

- dao层维护事务


- 业务相关的状态、类型等等维护在domain层，用衍生类型定义，其他层使用基本类型即可


- 错误由 %w 包装之后返回到最外层（ web 层 ）打印日志


- 每层之间不要有依赖，比如一些包变量等等

![](https://img.dac6.cn/uploads/images/20240515/2024051517263236c9f5630.png)



- dao 层操作mysql时，需要用注释写出自己期望的SQL，方便 review

![](https://img.dac6.cn/uploads/images/20240515/20240515172758ec1740765.png)



- service、web、repository 层必须写单元测试，保证代码质量


- 对于一些方法，如果预期其他人也会使用，则放到 pkg 中，如果只有在自己上下文中使用，则定义为私有


- 在定义方法或者工具类时，先查看 pkg 是否有相关的工具类，如果有，则使用 pkg 中的工具类，避免重复造轮子

- 时间使用数字类型，精确到毫秒，并且 update 时间必须维护
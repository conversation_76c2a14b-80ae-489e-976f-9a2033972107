def version = "latest"

pipeline {
    agent any

    //     triggers {
    //         GenericTrigger(
    //             genericVariables: [
    //                 [key: 'BRANCH', value: '$.ref'],
    //                 [key: 'COMMIT', value: '$.after'],
    //                 [key: 'REPOSITORY', value: '$.repository.name'],
    //             token: 'warehouse',
    //             printContributedVariables: true,
    //             printPostResult: true,
    //     }

    parameters {
        gitParameter(
              branchFilter: 'origin/(.*)',
                         defaultValue: 'test',
                         description: '请选择GIT分支',
                         name: 'git_branch',
                         selectedValue: 'NONE',
                         useRepository:'****************:nb-shenyang/byn/evaluation_tool_back.git',
                         // tagFilter: '*',
                         type: 'PT_BRANCH')
            choice(
              choices: "test\nprod",
              description: "选择环境",
              name: "deploy_env"
              )
    }

    stages {
        stage('输出变量') {
            steps {
                sh """
                echo branch = ${git_branch}
                echo dev = ${deploy_env}
                """
            }
        }
        stage('构建镜像') {
            steps {
                script {
                    version = "test"
                    if (deploy_env == 'prod') {
                        version = "release"
                    }
                }
                sh """
                make build VERSION=$version
                """
            }
        }
//         //         stage('拉取代码') {
//         //             steps {
//         //                 checkout([
//         //                     $class: 'GitSCM',
//         //                     branches: [[name: "${branch}"]],
//         //                     doGenerateSubmoduleConfigurations: true,
//         //                     userRemoteConfigs: [[credentialsId: 'coding', url: '****************:nb-shenyang/byn/warehouse.git']]
//         //                 ])
//         //
//         //             }
//         //         }
        stage('登录 docker') {
            steps {
                withCredentials([usernamePassword(credentialsId: 'harbor', passwordVariable: 'password', usernameVariable: 'username')]) {
                    sh """
                    docker version
                    docker login --username=${username} --password=${password} harbor.biyingniao.com
                    """
                }
            }
        }

        stage('推送镜像并更新') {
            steps {
                script{

                    try {
                        sh """
                            docker push harbor.biyingniao.com/byn/evaluation_tool_back:${version}
                            if [ ${deploy_env} = "test" ];then
                              echo "测试环境部署"
                              ssh root@*********** "cd /root/evaluation_tool_back && make pull && make stop && make start"
                            elif [ ${deploy_env} = "prod" ];then
                              echo "生产环境部署"
                              ssh root@************* "cd /www/evaluation_tool/back && make pull && make stop && make start"
                            fi
                        """
                    }catch (Exception e){
                        error("错误: ${e.getMessage()}")
                        throw e
                    }finally {
                        sh """
                            docker system prune -f
                        """
                    }
                }

            }
        }



    }
}
package ioc

import (
	"enter/internal/repository"
	"enter/internal/service"
	"enter/pkg/waihu"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/spf13/viper"
)

func CreateCallService(callRepo repository.CallRepo, ossClient *oss.Client) service.CallService {
	type xiaobangConfig struct {
		AppKey    string `mapstructure:"app_key"`
		AppSecret string `mapstructure:"app_secret"`
		Host      string `mapstructure:"host"`
	}
	var xiaobangC xiaobangConfig
	if err := viper.UnmarshalKey("xiaobang", &xiaobangC); err != nil {
		panic(err)
	}

	type yingkeyunConfig struct {
		Host         string `mapstructure:"host"`
		AppKey       string `mapstructure:"app_key"`
		AppSecret    string `mapstructure:"app_secret"`
		Platform     string `mapstructure:"platform"`
		EnterpriseID int64  `mapstructure:"enterprise_id"`
		SdkHost      string `mapstructure:"sdk_host"`
		SdkAppSecret string `mapstructure:"sdk_app_secret"`
	}
	var yingkeyunC yingkeyunConfig
	if err := viper.UnmarshalKey("yingkeyun", &yingkeyunC); err != nil {
		panic(err)
	}
	return service.NewCallService(callRepo, waihu.NewWaihuService(map[int8]waihu.WaihuInterface{
		1: waihu.NewXiaoBang(&waihu.Config{
			Host:      xiaobangC.Host,
			AppSecret: xiaobangC.AppSecret,
			AppKey:    xiaobangC.AppKey,
		}),
		2: waihu.NewYingKeYun(&waihu.Config{
			Host:         yingkeyunC.Host,
			AppSecret:    yingkeyunC.AppSecret,
			AppKey:       yingkeyunC.AppKey,
			Platform:     yingkeyunC.Platform,
			EnterpriseID: yingkeyunC.EnterpriseID,
			SdkHost:      yingkeyunC.SdkHost,
			SdkAppSecret: yingkeyunC.SdkAppSecret,
		}),
	}), ossClient)
}

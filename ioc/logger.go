package ioc

import (
	"strings"

	"github.com/fsnotify/fsnotify"

	"enter/pkg/logger"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"gopkg.in/natefinch/lumberjack.v2"
)

func getZapLoggerAndLevel() (*zap.Logger, string) {
	type Cfg struct {
		Level      string `mapstructure:"level"`
		Filename   string `mapstructure:"filename"`
		MaxSize    int    `mapstructure:"max_size"`
		MaxBackups int    `mapstructure:"max_backups"`
		MaxAge     int    `mapstructure:"max_age"`
	}
	var c Cfg
	err := viper.UnmarshalKey("log", &c)
	if err != nil {
		panic(err)
	}

	lumberjackLogger := &lumberjack.Logger{
		Filename:   c.Filename,
		MaxSize:    c.MaxSize,
		MaxBackups: c.Max<PERSON>ackups,
		MaxAge:     c.<PERSON>,
		Compress:   true,
	}

	level := zapcore.DebugLevel
	switch strings.ToLower(c.Level) {
	case "error":
		level = zapcore.ErrorLevel
	case "info":
		level = zapcore.InfoLevel
	case "warn":
		level = zapcore.WarnLevel
	}

	core := zapcore.NewCore(
		zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig()),
		zapcore.AddSync(lumberjackLogger),
		level,
	)

	l := zap.New(core, zap.AddCaller())

	return l, c.Level
}

func CreateLogger() logger.Logger {
	zapLogger, level := getZapLoggerAndLevel()
	res := logger.NewZapLogger(zapLogger)
	viper.OnConfigChange(func(e fsnotify.Event) {
		newZapLogger, newLevel := getZapLoggerAndLevel()
		if newLevel != level {
			res.UpdateZapLogger(newZapLogger)
			zap.ReplaceGlobals(newZapLogger)
		}
		level = newLevel
	})

	// 为了方便 ginx.wrapper.go 中日志的打印，不然从参数中传递 logger.Logger 太麻烦
	zap.ReplaceGlobals(zapLogger)
	return res
}

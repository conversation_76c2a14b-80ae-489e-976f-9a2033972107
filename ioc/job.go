package ioc

import (
	"enter/internal/job"
	"enter/internal/repository"
	"enter/pkg/logger"

	"github.com/robfig/cron/v3"
)

func CreateJobs(
	logger logger.Logger,
	brandRepo repository.BrandRepo,
	circleShopRepo repository.CircleShopRepo,
) *cron.Cron {
	//service.NewBrandMatchService(brandRepo, circleShopRepo).MatchAndUpdateBrands(context.Background(), 20000)
	c := cron.New(cron.WithSeconds())
	builder := job.NewCronJobBuilder()
	// 添加刷新token任务
	if _, err := c.AddJob("0 0 1 * * *", builder.Build(&job.TestJob{})); err != nil {
		panic(err)
	}
	return c
}

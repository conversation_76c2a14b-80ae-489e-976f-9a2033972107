package ioc

import (
	"enter/internal/web"
	"time"

	"enter/internal/web/jwt"
	"enter/internal/web/middleware"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
)

func CreateWeb(
	ms []gin.HandlerFunc,
	brandMatchHdl *web.BrandMatchHandler,
	callHdl *web.CallHandler,
	operateShopHdl *web.OperateShopHandler,
) *gin.Engine {
	engine := gin.Default()
	// engine.ContextWithFallback = true
	engine.Use(ms...)
	engine.GET("/ping", func(ctx *gin.Context) {
		ctx.JSON(200, gin.H{
			"message": "pong",
		})
	})

	brandMatchHdl.RegisterRoutes(engine)
	callHdl.RegisterRoutes(engine)
	operateShopHdl.RegisterRoutes(engine)
	// 注册路由
	return engine
}

func CreateJWT() *jwt.NormalJWT {
	return jwt.NewNormalJWT()
}

func CreateMiddlewares(
	auth jwt.JWT,
) []gin.HandlerFunc {
	return []gin.HandlerFunc{
		// 跨域
		cors.New(cors.Config{
			AllowMethods:     []string{"PUT", "PATCH", "POST", "GET", "DELETE", "OPTIONS"},
			AllowHeaders:     []string{"Authorization", "Content-Type"},
			ExposeHeaders:    []string{"X-Jwt-Token", "X-Refresh-Token"},
			AllowCredentials: true,
			AllowOriginFunc: func(origin string) bool {
				return true
			},
			MaxAge: 12 * time.Hour,
		}),

		// 登录验证
		middleware.NewLoginJwtMiddlewareBuilder(auth).
			IgnorePaths(
				"/ping",
			).
			Build(),
	}
}

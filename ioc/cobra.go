package ioc

import (
	"context"
	"errors"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/spf13/cobra"
	"github.com/spf13/viper"
	"go.uber.org/zap"
)

func CreateAppCommand(
	app *App,
) *cobra.Command {
	rootCmd := &cobra.Command{
		Use:   "tool",
		Short: "工具应用",
		Long:  `工具应用`,
		Run: func(cmd *cobra.Command, args []string) {
			_ = cmd.Help()
		},
	}
	rootCmd.AddCommand(CreateWebCommand(app))
	rootCmd.AddCommand(CreateJobCommand(app))
	return rootCmd
}

func CreateJobCommand(app *App) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "job",
		Short: "定时任务服务",
		Long:  `定时任务服务`,
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		app.Job.Start()
		select {}
	}
	return cmd
}

func CreateWebCommand(app *App) *cobra.Command {
	cmd := &cobra.Command{
		Use:   "web",
		Short: "web 服务",
		Long:  `web 服务`,
	}
	cmd.RunE = func(cmd *cobra.Command, args []string) error {
		port := viper.GetString("port")
		server := &http.Server{
			Addr:    fmt.Sprintf(":%s", port),
			Handler: app.Engine,
		}

		startErrChain := make(chan error, 2)
		go func() {
			startErrChain <- server.ListenAndServe()
		}()

		go func() {
			// 2秒后没有报错，则任务web服务启动成功
			ticker := time.NewTimer(time.Second * 2)
			select {
			case err := <-startErrChain:
				ticker.Stop()
				if err != nil && !errors.Is(err, http.ErrServerClosed) {
					panic(err)
				}
			case <-ticker.C:
				ticker.Stop()
				zap.L().Info("web 服务启动成功")
			}
		}()

		quit := make(chan os.Signal, 1)
		signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM) // 此处不会阻塞
		<-quit
		zap.L().Info("正在尝试关闭 web 服务 ...")

		ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
		defer cancel()
		if err := server.Shutdown(ctx); err != nil {
			zap.L().Error("关闭 web 服务失败", zap.Error(err))
		}
		zap.L().Info("关闭 web 服务成功")
		return nil
	}
	return cmd
}

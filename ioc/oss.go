package ioc

import (
	"fmt"

	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/spf13/viper"
)

func CreateOSSClient() *oss.Client {
	type Config struct {
		Endpoint        string `mapstructure:"endpoint"`
		AccessKeyId     string `mapstructure:"access_key_id"`
		AccessKeySecret string `mapstructure:"access_key_secret"`
	}
	var cfg Config
	err := viper.UnmarshalKey("oss", &cfg)
	if err != nil {
		panic(fmt.<PERSON>rrorf("读取OSS配置失败: %w", err))
	}

	client, err := oss.New(cfg.Endpoint, cfg.AccessKeyId, cfg.AccessKeySecret)
	if err != nil {
		panic(fmt.Errorf("创建OSS客户端失败: %w", err))
	}
	return client
}

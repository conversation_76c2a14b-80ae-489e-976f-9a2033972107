package ioc

import (
	"fmt"
	"time"

	"gorm.io/plugin/opentelemetry/tracing"

	"github.com/spf13/viper"
	"go.uber.org/zap"
	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	glogger "gorm.io/gorm/logger"
)

func CreateGormMysql() *gorm.DB {
	type Cfg struct {
		DSN         string `mapstructure:"dsn"`
		MaxOpenConn int    `mapstructure:"max_open_conn"`
		MaxIdleConn int    `mapstructure:"max_idle_conn"`
		MaxIdleTime int    `mapstructure:"max_idle_time"`
	}
	var c Cfg
	err := viper.UnmarshalKey("mysql", &c)
	if err != nil {
		panic(fmt.Errorf("初始化数据库配置失败:%w", err))
	}
	db, err := gorm.Open(mysql.New(mysql.Config{
		DSN: c.<PERSON>,
	}), &gorm.Config{
		Logger: glogger.New(gormLoggerFunc(func(msg string, fields ...zap.Field) {
			fmt.Println(msg)
			zap.L().Info(msg, fields...)
		}), glogger.Config{
			SlowThreshold:             time.Millisecond * 500,
			Colorful:                  true,
			IgnoreRecordNotFoundError: false,
			ParameterizedQueries:      false,
			LogLevel:                  glogger.Info,
		}),
	})
	if err != nil {
		panic(err)
	}
	err = db.Use(tracing.NewPlugin(
		tracing.WithDBName("ad"),
		tracing.WithoutMetrics(),
		tracing.WithoutQueryVariables(),
	))
	if err != nil {
		panic(err)
	}
	sqlDB, err := db.DB()
	if err != nil {
		panic(err)
	}

	sqlDB.SetMaxIdleConns(c.MaxIdleConn)
	sqlDB.SetConnMaxIdleTime(time.Duration(c.MaxIdleTime) * time.Second)
	sqlDB.SetMaxOpenConns(c.MaxOpenConn)
	return db
}

type gormLoggerFunc func(msg string, fields ...zap.Field)

func (f gormLoggerFunc) Printf(m string, args ...interface{}) {
	f(fmt.Sprintf(m, args...))
}

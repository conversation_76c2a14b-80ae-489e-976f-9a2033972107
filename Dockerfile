# 构建阶段
FROM golang:1.20-alpine AS builder

LABEL stage=gobuilder

# 设置环境变量
ENV CGO_ENABLED 0
ENV GOPROXY https://goproxy.cn,direct

# 优化alpine源
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories

# 安装基础工具
RUN apk update --no-cache && apk add --no-cache tzdata

# 设置工作目录
WORKDIR /build

# 复制go.mod和go.sum
ADD go.mod .
ADD go.sum .
RUN go mod download

# 复制源代码和配置文件
COPY . .
COPY config.yaml /app/config.yaml

# 编译（添加了编译优化参数）
RUN go build -ldflags="-s -w" -o /app/app .

# 使用scratch作为基础镜像
FROM scratch

# 复制证书和时区文件
COPY --from=builder /etc/ssl/certs/ca-certificates.crt /etc/ssl/certs/ca-certificates.crt
COPY --from=builder /usr/share/zoneinfo/Asia/Shanghai /usr/share/zoneinfo/Asia/Shanghai
ENV TZ Asia/Shanghai

# 设置工作目录
WORKDIR /app

# 复制二进制文件和配置文件
COPY --from=builder /app/app /app/app
COPY --from=builder /app/config.yaml /app/config.yaml

# 设置入口点
ENTRYPOINT ["./app"]

CMD [""]

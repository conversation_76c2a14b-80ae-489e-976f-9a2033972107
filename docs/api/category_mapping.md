# 类目映射 API 文档

## 简介

类目映射 API 提供了在不同平台之间建立类目映射关系的功能。支持多对一的映射关系，即多个源平台类目可以映射到一个目标平台类目。

## 接口列表

### 1. 创建类目映射

创建多对一的类目映射关系。

**请求路径**

```
POST /category-mappings/create
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| source_platform | int | 是 | 源平台 (1:饿了么 2:美团 3:京东外卖) |
| source_cids | []uint | 是 | 源类目ID列表 |
| target_platform | int | 是 | 目标平台 (1:饿了么 2:美团 3:京东外卖) |
| target_cid | uint | 是 | 目标类目ID |

**请求示例**

```json
{
    "source_platform": 1,
    "source_cids": [1, 2],
    "target_platform": 2,
    "target_cid": 3
}
```

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码 (0:成功) |
| message | string | 响应信息 |

**响应示例**

```json
{
    "code": 0,
    "message": "创建成功"
}
```

**错误码**

| 错误码 | 说明 |
|--------|------|
| 409001 | 输入参数验证失败 |
| 409002 | 源平台和目标平台不能相同 |
| 409003 | 类目不存在 |
| 409004 | 类目不是叶子节点 |
| 409005 | 类目已被映射 |
| 509001 | 系统内部错误 |

### 2. 获取源平台映射列表

获取指定源平台的所有类目映射关系。

**请求路径**

```
POST /category-mappings/list/source
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| platform | int | 是 | 源平台 (1:饿了么 2:美团 3:京东外卖) |

**请求示例**

```json
{
    "platform": 1
}
```

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码 (0:成功) |
| message | string | 响应信息 |
| data | array | 映射列表 |
| data[].id | uint64 | 映射ID |
| data[].source_platform | int | 源平台 |
| data[].source_cid | uint | 源类目ID |
| data[].target_platform | int | 目标平台 |
| data[].target_cid | uint | 目标类目ID |

**响应示例**

```json
{
    "code": 0,
    "data": [
        {
            "id": 1,
            "source_platform": 1,
            "source_cid": 1,
            "target_platform": 2,
            "target_cid": 3
        }
    ]
}
```

### 3. 获取目标平台映射列表

获取指定目标平台的所有类目映射关系。

**请求路径**

```
POST /category-mappings/list/target
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| platform | int | 是 | 目标平台 (1:饿了么 2:美团 3:京东外卖) |

**请求示例**

```json
{
    "platform": 2
}
```

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码 (0:成功) |
| message | string | 响应信息 |
| data | array | 映射列表 |
| data[].id | uint64 | 映射ID |
| data[].source_platform | int | 源平台 |
| data[].source_cid | uint | 源类目ID |
| data[].target_platform | int | 目标平台 |
| data[].target_cid | uint | 目标类目ID |

**响应示例**

```json
{
    "code": 0,
    "data": [
        {
            "id": 1,
            "source_platform": 1,
            "source_cid": 1,
            "target_platform": 2,
            "target_cid": 3
        }
    ]
}
```

### 4. 删除类目映射

删除指定的类目映射关系。

**请求路径**

```
POST /category-mappings/delete
```

**请求参数**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | uint | 是 | 映射ID |

**请求示例**

```json
{
    "id": 1
}
```

**响应参数**

| 参数名 | 类型 | 说明 |
|--------|------|------|
| code | int | 响应码 (0:成功) |
| message | string | 响应信息 |

**响应示例**

```json
{
    "code": 0,
    "message": "删除成功"
}
```

## 业务规则

1. 类目映射关系为多对一，即多个源平台类目可以映射到一个目标平台类目。
2. 源平台和目标平台不能相同。
3. 只能映射叶子节点类目。
4. 一个源类目只能映射到一个目标类目。
5. 一个目标类目可以被多个源类目映射。 
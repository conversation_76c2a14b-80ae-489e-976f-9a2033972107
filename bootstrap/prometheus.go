package bootstrap

import (
	"fmt"
	"net/http"

	"github.com/spf13/viper"

	"go.uber.org/zap"

	"github.com/prometheus/client_golang/prometheus/promhttp"
)

func InitPrometheus() {
	type Cfg struct {
		Port int64 `mapstructure:"port"`
	}
	var c Cfg
	err := viper.UnmarshalKey("prometheus", &c)
	if err != nil {
		panic(err)
	}
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", c.Port), nil)
		if err != nil {
			zap.L().Error("启动 prometheus 失败", zap.Error(err))
		}
	}()
}

version: '3.8'

services:
  web:
    build:
      context: .
      dockerfile: Dockerfile
    command: ["./app", "web"]
    ports:
      - "8080:8080"
    environment:
      - PORT=8080
      - GIN_MODE=release
    depends_on:
      - mysql
      - redis
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  job:
    build:
      context: .
      dockerfile: Dockerfile
    command: ["./app", "job"]
    depends_on:
      - mysql
      - redis
    networks:
      - app-network
    restart: unless-stopped

  mysql:
    image: mysql:8.0
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=enter
      - MYSQL_USER=enter
      - MYSQL_PASSWORD=enter
    volumes:
      - mysql_data:/var/lib/mysql
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost"]
      interval: 30s
      timeout: 10s
      retries: 3

  redis:
    image: redis:7.0-alpine
    command: redis-server --appendonly yes
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

networks:
  app-network:
    driver: bridge

volumes:
  mysql_data:
  redis_data: 
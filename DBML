Table sq_user {
  id int [pk, increment, note: '主键']
  sn int [default: 0, note: '编号']
  avatar varchar(200) [default: '', note: '头像']
  real_name varchar(32) [default: '', note: '真实姓名']
  nickname varchar(32) [default: '', note: '用户昵称']
  account varchar(32) [default: '', note: '用户账号']
  password varchar(32) [default: '', note: '用户密码']
  mobile varchar(32) [default: '', note: '用户电话']
  sex tinyint [default: 0, note: '用户性别: [1=男, 2=女]']
  channel tinyint [default: 0, note: '注册渠道']
  is_disable tinyint [default: 0, note: '是否禁用']
  login_ip varchar(30) [default: '', note: '最后登录IP']
  login_time int [default: 0, note: '最后登录时间']
  is_new_user tinyint [default: 0, note: '是否是新注册用户']
  user_money decimal(10,2) [default: 0.00, note: '用户余额']
  total_recharge_amount decimal(10,2) [default: 0.00, note: '累计充值']
  employee_id int [default: 0, note: '员工Id']
  system tinyint [default: 0, note: '系统账号']
  bdm_id int [default: 0, note: '所属BDM']
  business_type tinyint [default: 1, note: '业务类型']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
  delete_time int [note: '删除时间']
  ding_id bigint [default: 0, note: '钉钉通知群id']
  city varchar(200) [default: '', note: '城市']
  tel_id varchar(100) [default: '', note: '外呼id']
  is_call_user int [default: 0, note: '是否创建赢客云用户']
  role_id bigint [default: 1, note: '角色id']
  call_type tinyint [default: 1, note: '呼叫类型']
  parent_id int [default: 0, note: '上级id']
  level int [default: 0, note: '等级']
  version varchar(50) [default: '', note: '版本']

  indexes {
    account [unique, name: 'account']
    sn [unique, name: 'sn']
  }
  
  note: '用户表'
}

Table sq_user_session {
  id int [pk, increment]
  user_id int [not null, note: '用户id']
  terminal tinyint [default: 1, note: '客户端类型']
  token varchar(32) [not null, note: '令牌']
  update_time int [note: '更新时间']
  expire_time int [not null, note: '到期时间']
  status tinyint [default: 1, note: '0无效1有效']

  indexes {
    token [unique, name: 'token']
    (user_id, terminal) [name: 'admin_id_client', note: '一个用户在一个终端只有一个token']
  }
  
  note: '用户会话表'
}

Table sq_track_records {
  id bigint [pk, increment]
  clue_id bigint [default: 0, note: '线索id']
  clue_type tinyint [default: 1, note: '1-品牌跟进 2代运营']
  user_id bigint [default: 0, note: '用户id']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
  status int [default: 0, note: '状态']
  cancel_time int [default: 0, note: '取消时间']
  submit_time int [default: 0, note: '提报时间']
  store_num int [default: 0, note: '谈店数量']
  activity_name varchar(100) [default: '', note: '活动id']
  activity_city varchar(50) [default: '', note: '活动区域']
  bounty decimal(10,2) [default: 0.00, note: '奖励金']
  bounty_limit_cent decimal(10,2) [default: 0.00, note: '奖励金门槛']
  daily_quantity int [default: 0, note: '活动日库存']
  if_customer tinyint [default: 0, note: '是否添加客服']
  platform int [default: 0, note: '平台']
  store_id varchar(50) [note: '店铺id']
  belong_id bigint [default: 0, note: '归属id']

  indexes {
    clue_id [name: 'sq_track_records_clue_id_index']
    user_id [name: 'sq_track_records_user_id_index']
  }
}

Table sq_point_reports {
  id bigint [pk, increment]
  city varchar(50) [default: '', note: '城市']
  number int [default: 0, note: '轮次']
  wait_count bigint [default: 0, note: '等待点位']
  exec_count bigint [default: 0, note: '执行点位']
  finish_count bigint [default: 0, note: '完成点位']
  list_gather_count bigint [default: 0, note: '列表采集数量']
  detail_gather_count bigint [default: 0, note: '详情采集数量']
  new_shop_gather_count bigint [default: 0, note: '新店采集数量']
  brand_gather_count bigint [default: 0, note: '品牌采集数量']
  brand_shop_gather_count bigint [default: 0, note: '品牌店铺采集数量']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
}

Table sq_operate_promotion {
  id bigint [pk, increment]
  title varchar(255) [default: '', note: '标题']
  content text
  img varchar(255) [default: '', note: '图片']
  type tinyint [default: 0, note: '类型']
  create_time int [note: '创建时间']
  update_time int [note: '更新时间']
  pv bigint [default: 0, note: '点击']
}

Table sq_operate_click {
  id int [pk, increment]
  user_id int [default: 0]
  create_time timestamp [default: `CURRENT_TIMESTAMP`]
  update_time timestamp [default: `CURRENT_TIMESTAMP`, note: 'on update CURRENT_TIMESTAMP']
  promotion_id bigint [default: 0, note: '推广id']
  num bigint [default: 0, note: '次数']
  type int [default: 0, note: '类型']

  indexes {
    (user_id, type, promotion_id) [unique, name: 'sq_operate_click_pk']
  }
}

Table sq_operate_settlements {
  id int [pk, increment, note: '唯一标识符']
  settlement_date date [not null, note: '结算日期']
  merchant_name varchar(255) [default: '', note: '商家名称']
  store_id int [default: 0, note: '门店ID']
  settlement_period varchar(50) [default: '', note: '结算周期']
  fee_type varchar(50) [default: '', note: '费用类型']
  settlement_amount decimal(10,2) [default: 0.00, note: '结算金额(元)']
  deduction_description text [note: '扣费说明']
  platform tinyint [default: 0, note: '平台']
  contract_status varchar(50) [default: '', note: '合同状态']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at timestamp [default: `CURRENT_TIMESTAMP`, note: 'on update CURRENT_TIMESTAMP']

  indexes {
    (settlement_date, platform) [name: 'idx_settlement_date_platform']
  }
}

Table sq_operate_shops {
  id bigint [pk, increment]
  name varchar(150) [default: '', note: '店铺名']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
  category_id int [default: 0, note: '分类id']
  tel varchar(100) [default: '', note: '联系方式']
  address varchar(255) [default: '', note: '地址']
  is_dine tinyint [default: 0, note: '有无堂食']
  is_new tinyint [default: 0, note: '是否新店']
  store_area int [default: 0, note: '门店面积']
  brand_id bigint [default: 0, note: '品牌id']
  platform int [default: 0, note: '平台']
  shop_code varchar(100) [default: '', note: '店铺编码']
  is_charge tinyint [default: 0, note: '前置收费']
  fee decimal(10,2) [default: 0.00, note: '收费金额']
  commission_rate decimal(10,2) [default: 0.00, note: '佣金比例']
  remark varchar(255) [default: '', note: '备注']
  clue_id bigint [default: 0, note: '线索id']
  city varchar(50) [default: '', note: '城市']
  start_time int [default: 0, note: '签约时间']
  end_time int [default: 0, note: '结束时间']
  user_id bigint [default: 0, note: '用户id']
  status tinyint [default: 1, note: '状态']
  level_tier char [default: '', note: '分层等级']
  has_promotion int [default: -1, note: '有无推广']
  has_bwc int [default: -1, note: '推霸王餐']
  main_issues varchar(255) [default: '', note: '主要问题']
  termination_time int [default: 0, note: '解约时间']
  is_scaling int [default: -1, note: '是否起量']
  reason varchar(255) [default: '']
  user_type tinyint [default: 1, note: '用户类型']
  mode tinyint [default: 0, note: '模式']
  store_id varchar(50) [default: '', note: '店铺id']
}

Table sq_messages {
  id int [pk, increment]
  admin_user_name varchar(100) [not null, note: '接收用户名称']
  message_type tinyint [not null, note: '消息类型']
  content text [not null, note: '消息内容']
  is_read tinyint [default: 0, note: '是否已读']
  created_at timestamp [default: `CURRENT_TIMESTAMP`, note: '创建时间']
  updated_at timestamp [default: `0000-00-00 00:00:00`, note: 'on update CURRENT_TIMESTAMP']
  date_time date [note: '日期']
}

Table sq_ding {
  id bigint [pk, increment]
  name varchar(200) [default: '', note: '钉钉名字']
  access_token varchar(255) [default: '', note: '钉钉token']
  secret varchar(255) [default: '', note: '加密密钥']
  create_time int [default: 0]
  update_time int [default: 0]
}

Table sq_device_rec {
  id int [pk, increment]
  date_time date [note: '时间']
  code varchar(100) [default: '', note: '编码']
  status tinyint [default: 0, note: '状态']
  create_time int [default: 0]
  update_time int [default: 0]

  indexes {
    code [name: 'sq_device_rec_code_index']
  }
}

Table sq_data_rule {
  id bigint [pk, increment]
  title varchar(200) [default: '', note: '标题']
  type int [default: 1, note: '类型']
  user int [default: 0, note: '主管/用户']
  limits varchar(255) [default: '', note: '限制数据范围']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
}

Table sq_complaint_recs {
  id bigint [pk, increment]
  complaint_id bigint [default: 0, note: '客诉id']
  content varchar(300) [default: '', note: '跟进描述']
  certificates text [note: '凭证']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']

  indexes {
    complaint_id [name: 'sq_complaint_recs_complaint_id_index']
  }
}

Table sq_complaints {
  id bigint [pk, increment]
  code varchar(50) [default: '', note: '工单编号']
  clue_id int [default: 0, note: '线索id']
  user_id int [default: 0, note: '定责人']
  type tinyint [default: 0, note: '类型']
  level int [default: 0, note: '等级']
  status tinyint [default: 0, note: '状态']
  follow_time int [default: 0, note: '跟进事件']
  close_time int [default: 0, note: '关闭时间']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']

  indexes {
    clue_id [name: 'sq_complaints_clue_id_index']
    clue_id [name: 'sq_complaints_clue_id_index_2']
  }
}

Table sq_config {
  id int [pk, increment]
  type varchar(30) [note: '类型']
  name varchar(60) [default: '', note: '名称']
  value text [note: '值']
  create_time int [note: '创建时间']
  update_time int [note: '更新时间']
  
  note: '配置表'
}

Table sq_call_statis {
  id int [pk, increment]
  day char(10) [default: '', note: '日期']
  employee_id int [default: 0, note: '员工ID']
  employee_name varchar(50) [default: '', note: '员工名字']
  times int [default: 0, note: '电话呼出次数']
  customers int [default: 0, note: '电话呼出客户数']
  connected_times int [default: 0, note: '电话呼出接通次数']
  connected_customers int [default: 0, note: '电话呼出接通客户数']
  total_connected_times int [default: 0, note: '总接通次数']
  total_connected_customers int [default: 0, note: '总接通客户数']
  duration int [default: 0, note: '总通时/分钟']
  bill_time int [default: 0, note: '总计费时长/分钟']
  average_duration int [default: 0, note: '平均通话时长/秒']
  valid_times int [default: 0, note: '有效通话次数']
  valid_customers int [default: 0, note: '有效通话客户数']
  ringing_times int [default: 0]
  ringing_customers int [default: 0]
  d1 int [default: 0, note: '通话时长1分钟次数']
  d2 int [default: 0, note: '通话时长2分钟次数']
  d3 int [default: 0, note: '通话时长3分钟次数']
  d4 int [default: 0, note: '通话时长超过3分钟次数']
  created_at datetime [note: '创建时间']
  updated_at datetime [note: '更新时间']

  indexes {
    day [name: 'idx_day']
    employee_id [name: 'idx_employee_id']
    employee_name [name: 'idx_employee_name']
  }
  
  note: '通话统计'
}

Table sq_clue_filter_report {
  id bigint [pk, increment]
  keyword varchar(100) [default: '', note: '关键词']
  field varchar(100) [default: '', note: '字段']
  quantity int [default: 0, note: '数量']
  type tinyint [default: 1, note: '类型']

  indexes {
    (field, keyword, type) [unique, name: 'sq_clue_filter_report_pk_2']
  }
}

Table sq_call_record {
  id bigint [pk, increment]
  create_time int
  update_time int
  caller varchar(50) [default: '', note: '主叫号码']
  callee varchar(50) [default: '', note: '被叫号码']
  show_no varchar(50) [default: '', note: '中间号']
  order_id varchar(150) [default: '', note: '话单唯一id']
  audio_url varchar(255) [default: '', note: '音频地址']
  bind_id text [note: '绑定id']
  hold_time bigint [default: 0, note: '通话时间']
  event_type int [default: 0, note: '话单类型']
  error_msg varchar(255) [default: '', note: '错误原因']
  status tinyint [default: 0, note: '状态']
  clue_id bigint [default: 0, note: '线索id']
  user_id bigint [default: 0, note: '用户id']
  start_time bigint [default: 0, note: '开始时间']
  end_time bigint [default: 0, note: '结束时间']
  call_type varchar(100) [default: '', note: '接口类型']
  remark varchar(255) [default: '', note: '备注']
  ori_audio_url varchar(255) [default: '']
  order_sn varchar(255) [default: '', note: '内部订单号']
  channel_type tinyint [default: 1, note: '渠道类型']
  tel_id varchar(100) [default: '', note: '外呼id']
  clue_type tinyint [default: 1, note: '线索类型']
  tags varchar(255) [default: '', note: '标签']
  asr_status varchar(50) [default: '', note: '数位存储']

  indexes {
    order_sn [name: 'idx_order_sn']
    callee [name: 'sq_call_record_callee_index']
    caller [name: 'sq_call_record_caller_index']
    clue_id [name: 'sq_call_record_clue_id_index']
    order_id [name: 'sq_call_record_order_id_index']
    user_id [name: 'sq_call_record_user_id_index']
  }
}

Table sq_clue_audit {
  id bigint [pk, increment]
  create_time int [default: 0]
  update_time int [default: 0]
  audit_code varchar(255) [default: '', note: '审核编号']
  clue_id bigint [default: 0, note: '线索id']
  activity_id bigint [default: 0, note: '活动id']
  bounty decimal(10,2) [default: 0.00, note: '奖励金']
  bounty_limit_cent decimal(10,2) [default: 0.00, note: '奖励金门槛']
  daily_quantity int [default: 0, note: '活动日库存']
  user_id bigint [default: 0, note: '用户id']
  if_customer tinyint [default: 0, note: '是否添加客服']
  start_time int [default: 0, note: '开始时间']
  end_time int [default: 0, note: '结束时间']
  status int [default: 0, note: '状态']
  reason varchar(255) [default: '', note: '拒绝原因']
  create_type tinyint [default: 0, note: '创建类型']
  submit_time int [default: 0, note: '报名时间']
  shop_act_id bigint [default: 0, note: '店铺活动id']
  close_time int [note: '关闭时间']
  transit_time int [default: 0, note: '通过时间']
  system tinyint [default: 0, note: '是否系统']
  refuse_time int [default: 0, note: '拒绝时间']
  clue_type tinyint [default: 1, note: '线索类型']
  tries int [default: 0, note: '尝试次数']
  cause varchar(255) [default: '', note: '分配原因']
  sign tinyint [default: 0, note: '签约类型']

  indexes {
    create_time [name: 'idx_create_time']
    status [name: 'idx_status']
    clue_id [name: 'sq_clue_audit_clue_id_index']
    shop_act_id [name: 'sq_clue_audit_shop_act_id_index']
    user_id [name: 'sq_clue_audit_user_id_index']
  }
}

Table sq_clue_task_info {
  id bigint [pk, increment]
  task_id bigint [default: 0, note: '任务id']
  clue_id bigint [default: 0, note: '线索id']
  user_id bigint [default: 0, note: '用户id']
  call_status tinyint [default: 0, note: '呼叫状态']
  call_time int [default: 0, note: '呼叫时间']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
  note varchar(255) [default: '', note: '小记']
  submit_status int [default: 0, note: '提报状态']

  indexes {
    clue_id [name: 'sq_clue_task_info_clue_id_index']
    task_id [name: 'sq_clue_task_info_task_id_index']
    user_id [name: 'sq_clue_task_info_user_id_index']
  }
}

Table sq_clue_task {
  id bigint [pk, increment]
  people_num int [default: 0, note: '人数']
  allotment_num int [default: 0, note: '分配数']
  type tinyint [default: 0, note: '分配类型']
  appoint varchar(255) [default: '0', note: '指定信息']
  city varchar(50) [default: '', note: '分配城市']
  clue_limit tinyint [default: 0, note: '过滤类型']
  create_time int [note: '创建时间']
  update_time int [note: '更新时间']
  admin_id int [default: 0, note: '创建人']
  status tinyint [default: 0, note: '执行状态']
  call_count int [default: 0, note: '呼叫数量']
  connect_count int [default: 0, note: '接通数']
  category_id varchar(100) [default: '', note: '分类']
  monthly_sales varchar(100) [default: '', note: '月销范围']
  start_time int [default: 0, note: '上次拨打的时间']
  end_time int [default: 0, note: '拨打的结束时间']
  notes varchar(200) [default: '', note: '联系情况']
  is_cps_open tinyint [default: 0, note: '是否开启CPS']
  is_new tinyint [default: 0, note: '是否新店']
  version int [note: '锁']
  admin_name varchar(100) [default: '', note: '创建人']
  time_type tinyint [default: 1, note: '时间类型']
  limit_note_count varchar(50) [default: '', note: '小记数量']
  limit_call_count varchar(50) [default: '', note: '限制通话数量']
  appoint_type tinyint [default: 1, note: '分配类型']
  clue_ids text [note: '线索ids']
  business_districts text [note: '商圈信息']
  shop_level varchar(50) [default: '']
  shop_status int [note: '店铺状态']
  is_valid tinyint [default: 0, note: '是否有效']
  take_start_time int [default: 0, note: '爬虫更新时间']
  take_end_time int [default: 0, note: '爬虫更新时间']
  title varchar(100) [default: '', note: '任务标题']
}

Table sq_clue_report {
  id bigint [pk, increment]
  clue_id bigint [default: 0, note: '线索id']
  note_count bigint [default: 0, note: '小记数量']
  last_note_id bigint [default: 0, note: '最近一次的小记id']
  call_count bigint [default: 0, note: '电话数']
  last_call_time int [default: 0, note: '最后一次通话时间']
  last_note_type int [default: 0, note: '最后的小记类型']
  last_dial_time int [default: 0, note: '最后一次呼叫时间']
  last_note_time int [default: 0, note: '最后一次小记时间']

  indexes {
    clue_id [unique, name: 'sq_clue_report_pk_2']
  }
}

Table sq_clue_notes {
  id bigint [pk, increment]
  clue_id bigint [default: 0, note: '线索id']
  user_id bigint [default: 0, note: '用户id']
  content varchar(150) [default: '', note: '小记内容']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
  next_time int [default: 0]
  type int [default: 0, note: '小记类型']
  clue_type tinyint [default: 1, note: '线索类型']

  indexes {
    clue_id [name: 'sq_clue_notes_clue_id_index']
    user_id [name: 'sq_clue_notes_user_id_index']
  }
  
  note: '沟通小记'
}

Table sq_business_district_grade_info {
  id bigint [pk, increment]
  grade_id int [default: 0, note: '商圈规则id']
  business_district varchar(255) [default: '', note: '店铺数量']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
}

Table sq_business_district_grade {
  id bigint [pk, increment]
  level varchar(100) [default: '', note: '商圈等级']
  business_district_count int [default: 0, note: '商圈数量']
  shop_count int [default: 0, note: '店铺数量']
  desc varchar(200) [default: '', note: '描述']
  type tinyint [default: 0, note: '类型']
  status tinyint [note: '状态']
  start_num int [default: 0, note: '平均结算订单区间']
  end_num int [default: 0, note: '平均结算订单区间']
  create_time int [default: 0, note: '创建时间']
  update_time int [default: 0, note: '更新时间']
}

// Define relationships between tables
Ref: sq_user_session.user_id > sq_user.id
Ref: sq_track_records.user_id > sq_user.id
Ref: sq_call_record.user_id > sq_user.id
Ref: sq_clue_audit.user_id > sq_user.id
Ref: sq_clue_task_info.user_id > sq_user.id
Ref: sq_clue_notes.user_id > sq_user.id
Ref: sq_operate_click.user_id > sq_user.id
Ref: sq_operate_shops.user_id > sq_user.id
Ref: sq_complaints.user_id > sq_user.id

Ref: sq_call_record.clue_id > sq_track_records.clue_id
Ref: sq_clue_audit.clue_id > sq_track_records.clue_id
Ref: sq_clue_task_info.clue_id > sq_track_records.clue_id
Ref: sq_clue_notes.clue_id > sq_track_records.clue_id
Ref: sq_complaints.clue_id > sq_track_records.clue_id
Ref: sq_complaint_recs.complaint_id > sq_complaints.id

Table brands {
  id bigint [pk, increment]
  created_at datetime(3)
  updated_at datetime(3)
  brand_name varchar(150) [not null, default: '', note: '品牌名称']
  brand_logo varchar(255) [default: '', note: '品牌logo']
  detail_img varchar(255) [default: '', note: '详情图']
  desc varchar(255) [default: '', note: '描述']
  detail_desc varchar(255) [default: '', note: '详细描述']
  status tinyint [default: 0, note: '0 下架 1 上架']
  weight int [default: 0, note: '权重']
  policy text [not null, note: '招商政策']
  store_num int [default: 0, note: '总门店数']
  contact_name varchar(100) [default: '', note: '联系人']
  contact_phone varchar(100) [default: '', note: '联系方式']
  company varchar(255) [default: '', note: '所属公司名称']
  average decimal(6,2) [default: 0.00, note: '人均客单价，元']
  category_id bigint [default: 0, note: '分类ID']
  in_store_num bigint [default: 0, note: '谈成门店数']
  city varchar(100) [default: '', note: '城市']
  province varchar(255) [default: '', note: '省份']
  create_type tinyint [default: 0, note: '0手动创建 1爬虫']
  crawl_time int [default: 0, note: '爬取时间']
  lock_status tinyint [default: 0, note: '0未锁定 1已锁定']
  operation_mode varchar(100) [default: '', note: '经营类目']
  brand_code varchar(100) [default: '', note: '品牌编号']
  area_min bigint [default: 0, note: '最小面积']
  area_max bigint [default: 0, note: '最大面积']
  show_type int [default: 0, note: '展示类型']
  platform_store_num int [default: 0, note: '平台门店数']
  min_month_sale int [default: 0, note: '最小月售']
  max_month_sale int [default: 0, note: '最大月售']

  indexes {
    brand_name [unique]
  }
}

Table category {
  id bigint [pk, increment]
  name varchar(100) [default: '', note: '分类名称']
  created_at datetime [note: '创建时间']
  updated_at datetime [note: '更新时间']
  type tinyint [default: 0, note: '0神犬 1品牌']
  level int [default: 0, note: '层级']
  parent_id bigint [default: 0, note: '父级id']
}

Table circle_shops {
  id bigint [pk, increment]
  created_at datetime
  updated_at datetime
  name varchar(150) [not null, default: '', note: '店铺名称']
  circle_id bigint [default: 0, note: '商圈ID']
  crawl_time int [default: 0, note: '爬取时间']
  distance int [default: 0, note: '距离，米']
  tags varchar(255) [note: '标签']
  month_sale int [default: 0, note: '月售']
  score decimal(2,1) [default: 0.0, note: '评分']
  category_name varchar(255) [default: '', note: '分类名称']
  lower_price decimal(10,2) [default: 0.00, note: '低价均值']
  tall_price decimal(10,2) [default: 0.00, note: '高价均值']
  is_brand tinyint [default: 0, note: '是否是品牌']
  is_new tinyint [default: 0, note: '是否是新店']
  last_update_time datetime [not null, note: '最近更新时间']
  brand_id bigint [default: 0, note: '品牌id']

  indexes {
    (circle_id) [name: 'idx_circle_id']
    (crawl_time) [name: 'idx_crawl_time']
    (is_brand) [name: 'idx_is_brand']
    (name) [name: 'idx_name']
  }
  
  note: '爬虫商圈店铺名称'
}

Table business_district_location {
  id int [pk, increment]
  created_at datetime
  updated_at datetime
  business_district varchar(255) [default: '', note: '商圈名称']
  province varchar(255) [default: '', note: '省份']
  city varchar(255) [default: '', note: '城市']
  district varchar(255) [default: '', note: '区域']
  location varchar(255) [default: '', note: '经纬度']
  formatted_address varchar(255) [default: '', note: '格式化地址']
  crawl_time int [default: 0, note: '爬取时间']
  level int [default: 99, note: '等级']
  type tinyint [default: 0]
  crawl_num int [default: 0]

  indexes {
    (city) [name: 'idx_city']
    (crawl_time) [name: 'idx_crawl_time']
  }
  
  note: '爬虫任务'
}

Table shop_reports {
  id bigint [pk, increment]
  created_at datetime
  updated_at datetime
  shop_id bigint [default: 0, note: '店铺ID']
  shop_code varchar(255) 
  shop_name varchar(255) [default: '', note: '店铺名称']
  brand_name varchar(150) [default: '', note: '品牌名称']
  uv bigint [default: 0, note: '引流uv']
  promote_taoke_num bigint [default: 0, note: '推广淘客数']
  pay_amount decimal(10,2) [default: 0.00, note: '付款金额']
  pay_num bigint [default: 0, note: '付款笔数']
  order_original_service_fee decimal(10,2) [default: 0.00, note: '预估订单原始服务费']
  order_services_fee decimal(10,2) [default: 0.00, note: '预估订单招商服务费']
  user_settle_amount decimal(10,2) [default: 0.00, note: '用户结算金额']
  shop_settle_amount decimal(10,2) [default: 0.00, note: '商家结算金额']
  settle_num bigint [default: 0, note: '结算笔数']
  original_service_fee decimal(10,2) [default: 0.00, note: '总预估结算原始服务费']
  settle_fee decimal(10,2) [default: 0.00, note: '总预估结算招商服务费']
  city_service_fee decimal(10,2) [default: 0.00, note: '总预估订单城市服务商服务费']
  city_settle_fee decimal(10,2) [default: 0.00, note: '总预估结算城市服务商服务费']
  order_service_fee decimal(10,2) [default: 0.00, note: '总预估订单招商服务商服务费']
  order_settle_fee decimal(10,2) [default: 0.00, note: '总预估结算招商服务商服务费']
  city varchar(100) [default: '', note: '城市']
  district varchar(100) [default: '', note: '区域']
  business_district varchar(255) [default: '', note: '商圈']
  create_time datetime [note: '创建时间']
  employee_id int [default: 0, note: '员工表id']
  is_new_shop tinyint [default: 0, note: '是否新店']
  timeline tinyint [default: 0, note: '时间线']
  new_signature tinyint [default: 0, note: '新签']
  type tinyint [default: 1, note: '1-非品牌 2-品牌']
  official_uv bigint [default: 0, note: '官方引流uv']
  official_settle_num bigint [default: 0, note: '官方结算笔数']
  official_settle_fee decimal(10,2) [default: 0.00, note: '官方总预估结算招商服务费']

  indexes {
    (brand_name) [name: 'idx_brand_name']
    (business_district) [name: 'idx_business_district']
    (create_time) [name: 'idx_create_time']
    (employee_id, create_time, settle_num) [name: 'idx_employeeid_createtime_settlenum']
    (employee_id, timeline, new_signature, create_time, settle_num) [name: 'idx_employeeid_timeline_newsignature_createtime_settlenum']
    (shop_code, create_time) [name: 'idx_shopcode_createtime']
    (shop_id, create_time, settle_num) [name: 'idx_shopid_createtime_settlenum']
    shop_name [name: 'shop_reports_shop_name_index']
    (create_time, shop_code, type) [unique, name: 'unique_create_time_shop_code_type']
  }
  
  note: '推广明细导入数据'
}

Table shop_act_reports {
  id bigint [pk, increment]
  created_at datetime
  updated_at datetime
  shop_id bigint [default: 0, note: '店铺ID']
  shop_code varchar(255)
  shop_name varchar(255) [default: '', note: '店铺名称']
  act_name varchar(255) [default: '', note: '活动名称']
  brand_name varchar(150) [default: '', note: '品牌名称']
  uv bigint [default: 0, note: '引流uv']
  promote_taoke_num bigint [default: 0, note: '推广淘客数']
  pay_amount decimal(10,2) [default: 0.00, note: '付款金额']
  pay_num bigint [default: 0, note: '付款笔数']
  order_original_service_fee decimal(10,2) [default: 0.00, note: '预估订单原始服务费']
  order_services_fee decimal(10,2) [default: 0.00, note: '预估订单招商服务费']
  user_settle_amount decimal(10,2) [default: 0.00, note: '用户结算金额']
  shop_settle_amount decimal(10,2) [default: 0.00, note: '商家结算金额']
  settle_num bigint [default: 0, note: '结算笔数']
  original_service_fee decimal(10,2) [default: 0.00, note: '总预估结算原始服务费']
  settle_fee decimal(10,2) [default: 0.00, note: '总预估结算招商服务费']
  city_service_fee decimal(10,2) [default: 0.00, note: '总预估订单城市服务商服务费']
  city_settle_fee decimal(10,2) [default: 0.00, note: '总预估结算城市服务商服务费']
  order_service_fee decimal(10,2) [default: 0.00, note: '总预估订单招商服务商服务费']
  order_settle_fee decimal(10,2) [default: 0.00, note: '总预估结算招商服务商服务费']
  city varchar(100) [default: '', note: '城市']
  district varchar(100) [default: '', note: '区域']
  business_district varchar(255) [default: '', note: '商圈']
  create_time datetime [note: '创建时间']
  employee_id int [default: 0, note: '员工表id']
  is_new_shop tinyint [default: 0, note: '是否新店']
  bounty decimal(8,2) [default: 0.00, note: '设置活动奖励金']
  user_pay_threshold decimal(8,2) [default: 0.00, note: '活动最低门槛']
  act_id bigint [default: 0, note: '活动id']
  type tinyint [default: 1, note: '1-非品牌 2-品牌']

  indexes {
    create_time [name: 'idx_create_time']
    (shop_code, act_id) [name: 'shop_act_reports_shop_code_act_id_index']
    (shop_code, act_name, create_time) [name: 'shop_act_reports_shop_code_act_name_create_time_index']
  }
  
  note: '推广活动明细导入数据'
}

// Define relationships between tables
Ref: brands.category_id > category.id
Ref: circle_shops.brand_id > brands.id
Ref: circle_shops.circle_id > business_district_location.id
Ref: shop_reports.brand_name > brands.brand_name
Ref: shop_act_reports.brand_name > brands.brand_name

Table elm_shop_change {
  id bigint [pk, increment]
  elm_shop_id bigint [not null, default: 0, note: '店铺id']
  old_name varchar(255) [not null, default: '', note: '老的店铺名称']
  new_name varchar(255) [not null, default: '', note: '新的店铺名称']
  created_at datetime
  updated_at datetime

  indexes {
    (elm_shop_id) [name: 'idx_elm_shop_id']
  }
  
  note: '店铺名称变更记录表'
}

// 表名: elm_shop_code_employee_rels (店铺-员工关联表)
Table elm_shop_code_employee_rels {
  id int [pk, increment]
  employee_id int [not null, note: '员工ID']
  start_time datetime [not null, note: '开始时间']
  end_time datetime [not null, note: '结束时间']
  tag_id int [default: 0, note: '类型 1--新签 0--其他']
  remark varchar(255) [default: '', note: '备注']
  created_at timestamp
  updated_at timestamp
  deleted_at timestamp
  city varchar(255) [default: '杭州', note: '城市']
  shop_code varchar(255) [not null, default: '', note: '店铺的code']
  shop_name varchar(255) [note: '店铺名称']
  type tinyint [not null, default: 1, note: '1-非品牌 2-品牌']
  brand_name varchar(100) [not null, default: '', note: '品牌名称']
  expire_time int [not null, default: 0, note: '过期时间']

  indexes {
    (employee_id) [name: 'elm_shop_employee_rels_employee_id_index']
    (shop_code) [name: 'idx_shop_code']
    (shop_code, start_time) [name: 'idx_shop_code_start_time']
    (type, brand_name, employee_id) [name: 'idx_type_brand_employee']
    (type, expire_time) [name: 'idx_type_expire']
  }
}

// 表名: elm_shops (霸王餐导入店铺·)
Table elm_shops {
  id bigint [pk, increment]
  created_at datetime(3)
  updated_at datetime(3)
  name varchar(150) [not null, default: '', note: '店铺名称']
  logo varchar(255) [note: '店铺LOGO']
  employee_id bigint [default: 0, note: '员工ID']
  ori_shop_id varchar(100) [not null, default: '', note: '原始店铺ID']
  shop_code varchar(200) [not null, default: '', note: '店铺编码']
  longitude decimal(10,7) [note: '经度']
  latitude decimal(10,7) [note: '纬度']
  province varchar(100) [not null, default: '', note: '店铺所在省']
  city varchar(100) [not null, default: '', note: '店铺所在城市']
  district varchar(100) [not null, default: '', note: '店铺所在区或县']
  address varchar(255) [default: '', note: '详细地址']
  business_district varchar(255) [not null, default: '', note: '商圈']
  type int [not null, default: 0, note: '1elm 2美团']
  status int [not null, default: 1, note: '0失效，1-在线，2-未报名，3-不在线']
  yesterday_order_num int [not null, default: 0, note: '昨日订单']
  before_day_order_num int [not null, default: 0, note: '前日订单']
  month_order_num int [not null, default: 0, note: '本月订单']
  last_month_order_num int [not null, default: 0, note: '上月订单']
  week_order_num int [not null, default: 0, note: '本周订单']
  total_order_num int [not null, default: 0, note: '总订单']
  shop_link varchar(1024) [note: '门店链接']
  is_open boolean [not null, default: false, note: '是否营业']
  is_valid boolean [not null, default: false, note: '是否有效']
  job_priority boolean [not null, default: false, note: '优先级']
  tags varchar(255) [note: '标签']
  tel varchar(255) [not null, default: '', note: '联系电话']
  month_sale int [default: 0, note: '月售']
  score decimal(2,1) [default: 0.0, note: '评分']
  evaluate int [default: 0, note: '评价数']
  crawl_time int [not null, default: 0, note: '爬取时间']
  source_type boolean [not null, default: false, note: '来源类型']
  claim_id bigint [not null, default: 0, note: '认领id']
  used_name varchar(150) [not null, default: '', note: '曾用名']
  district_code varchar(20) [not null, default: '', note: '行政区ID']
  intent_level int [not null, default: 0, note: '意向等级']
  call_time int [not null, default: 0, note: '外呼时间']
  priority int [not null, default: 0, note: '意向优先级']
  brand_leader_id bigint [not null, default: 0, note: '品牌负责人']
  brand_type int [default: 0, note: '品牌类型']
  brand_id int [default: 0, note: '品牌ID']
  category_name varchar(255) [not null, default: '', note: '分类名称']
  lower_price decimal(10,2) [not null, default: 0.00, note: '低价均值']
  tall_price decimal(10,2) [not null, default: 0.00, note: '高价均值']
  is_brand boolean [not null, default: false, note: '是否是品牌']
  is_new boolean [not null, default: false, note: '是否是新店']
  order_snapshot varchar(1024) [not null, default: '', note: '点餐快照']
  evaluate_snapshot varchar(255) [not null, default: '', note: '评价快照']
  shop_snapshot varchar(255) [not null, default: '', note: '商家快照']
  is_cps_open int [not null, default: 0, note: '是否开启cps']
  category_id int [not null, default: 0, note: '分类id']
  is_dp_effect int [not null, default: 0, note: '是否开启大牌']
  business_update_time int [not null, default: 0, note: '商机更新时间']
  business_type varchar(50) [not null, default: '', note: '业务类型']
  cps_claim_id bigint [not null, default: 0, note: 'cps认领员工id']
  cps_employee_id bigint [not null, default: 0, note: 'cps归属员工id']
  shop_type int [not null, default: 1, note: '店铺类型']
  bonus_fee varchar(50) [not null, default: '', note: '红包推荐']
  service_fee varchar(50) [not null, default: '', note: '服务费推荐']
  match_name varchar(255) [not null, default: '', note: '匹配名称']
  source_id int [not null, default: 0, note: '来源id']
  last_take_time timestamp [default: CURRENT_TIMESTAMP, note: '上次爬取时间']

  note: '霸王餐导入店铺'

  indexes {
    (address) [name: 'idx_address']
    (crawl_time) [name: 'idx_crawl_time']
    (match_name) [name: 'idx_match_name']
    (name) [name: 'idx_name']
    (ori_shop_id) [name: 'idx_ori_shop_id']
    (priority) [name: 'idx_priority']
    (tel, status, is_valid, type, crawl_time) [name: 'idx_tel_status_isvalid_type_crawltime']
    (brand_id) [name: 'index_brand_id']
    (employee_id) [name: 'index_employee_id']
    (shop_code) [name: 'index_shop_code']
  }
}

Table elm_account_settings {
  id INT [primary key, auto_increment, note: '自增ID']
  username VARCHAR(255) [default "", not null, note: '用户名']
  password VARCHAR(255) [default "", not null, note: '密码']
  city VARCHAR(255) [default "", not null, note: '城市']
  cookie TEXT [note: '登录cookie']
  appid VARCHAR(255) [default "", null, note: '应用id']
  open_crawl BOOLEAN [null, note: '是否开启爬虫']
  created_at TIMESTAMP [null]
  updated_at TIMESTAMP [null]

  Note: '饿了么账号信息表'
} 

Table elm_shop_act_rels {
  id BIGINT [primary key, auto_increment, note: '自增ID']
  shop_id BIGINT [null, note: '店铺ID']
  act_rec_id BIGINT [not null, note: '报名ID']
  activity_id INT [not null, note: '活动ID']
  ori_shop_id VARCHAR(255) [not null, note: '饿了么店铺ID']
  ori_shop_name VARCHAR(255) [not null, note: '店铺名称']
  shop_score DECIMAL(8,2) [not null, note: '店铺评分']
  audit_status TINYINT [default: 0, not null, note: '审核状态 0-待审核，1-人审通过，2-拒绝，3-免审规则通过']
  bonus_fee INT [default: 0, not null, note: '奖励金']
  bouns_order_num INT [default: 0, not null, note: '每日活动库存']
  channel_service_fee DECIMAL(10,2) [default: 20.00, not null, note: '渠道推广费率']
  city_services_fee DECIMAL(10,2) [default: 80.00, not null, note: '招商费率']
  commission_rate DECIMAL(10,2) [default: 6.00, not null, note: '佣金比率']
  promotion_state INT [default: 1, not null, note: '推广状态 0- --，1-未开始，2-进行中，3-已暂停，4-已结束，5-退出中，6-已退出']
  sales_num INT [default: 0, not null, note: '销量']
  services_fee INT [default: 3, not null, note: '招商服务费']
  order_amt_limit INT [not null, note: '满XX元']
  city_name VARCHAR(255) [null, note: '城市']
  district_name VARCHAR(255) [null, note: '区']
  enroll_date DATETIME [not null, note: '报名时间']
  start_date DATETIME [null, note: '开始时间']
  end_date DATETIME [not null, note: '结束时间']
  created_at TIMESTAMP [null]
  updated_at TIMESTAMP [null]
  shop_code VARCHAR(255) [default: "", not null, note: '店铺的code']
  terminate_time DATETIME [null, note: '退出时间']
  is_deleted TINYINT(1) [default: 0, not null]
  match_name VARCHAR(255) [default: "", not null, note: '去除所有特殊字符的店铺名']
  business_district VARCHAR(255) [default: "", not null, note: '商圈']
  refuse_date DATETIME [null, note: '拒绝时间']
  pass_date DATETIME [null, note: '通过时间']
  brand_id INT [default: 0, not null, note: '品牌id']

  indexes {
    (act_rec_id) [name: 'act_rec_id_idx']
    (activity_id) [name: 'elm_shop_act_rels_activity_id_index']
    (ori_shop_name) [name: 'elm_shop_act_rels_ori_shop_name_index']
    (end_date, start_date) [name: 'idx_act_time_range']
    (enroll_date, act_rec_id, audit_status, promotion_state, is_deleted) [name: 'idx_enroll_rec_audit_promotion']
    (enroll_date) [name: 'idx_enroll_time']
    (ori_shop_name, audit_status, promotion_state, end_date) [name: 'idx_name_audit_promotion_end']
    (ori_shop_id) [name: 'idx_ori_shop_id']
    (ori_shop_name, audit_status, promotion_state) [name: 'idx_ori_shop_name_audit_status_promotion_state']
    (shop_code) [name: 'idx_shop_code']
    (shop_id, promotion_state) [name: 'idx_shop_id_promotion_state']
    (terminate_time) [name: 'idx_terminate_time']
  }
  Note: '饿了么店铺活动关系表'
}
// Code generated by Wire. DO NOT EDIT.

//go:generate go run -mod=mod github.com/google/wire/cmd/wire
//go:build !wireinject
// +build !wireinject

package main

import (
	"enter/internal/repository"
	"enter/internal/repository/dao"
	"enter/internal/service"
	"enter/internal/web"
	"enter/internal/web/jwt"
	"enter/ioc"
	"github.com/google/wire"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/cobra"
)

// Injectors from wire.go:

func CreateApp() *cobra.Command {
	normalJWT := ioc.CreateJWT()
	v := ioc.CreateMiddlewares(normalJWT)
	db := ioc.CreateGormMysql()
	gormBrandDao := dao.NewGORMBrandDao(db)
	cacheBrandRepo := repository.NewCacheBrandRepo(gormBrandDao)
	gormCircleShopDao := dao.NewGORMCircleShopDao(db)
	cacheCircleShopRepo := repository.NewCacheCircleShopRepo(gormCircleShopDao)
	brandMatchService := service.NewBrandMatchService(cacheBrandRepo, cacheCircleShopRepo)
	brandMatchHandler := web.NewBrandMatchHandler(brandMatchService)
	gormUserDao := dao.NewGORMUserDao(db)
	cacheUserRepo := repository.NewCacheUserRepo(gormUserDao)
	userSvc := service.NewUserService(cacheUserRepo)
	gormCallRecordDao := dao.NewGORMCallRecordDao(db)
	cacheCallRepo := repository.NewCacheCallRepo(gormCallRecordDao)
	client := ioc.CreateOSSClient()
	callService := ioc.CreateCallService(cacheCallRepo, client)
	callHandler := web.NewCallHandler(userSvc, callService)
	engine := ioc.CreateWeb(v, brandMatchHandler, callHandler)
	logger := ioc.CreateLogger()
	cron := ioc.CreateJobs(logger, cacheBrandRepo, cacheCircleShopRepo)
	app := &ioc.App{
		Engine: engine,
		Job:    cron,
	}
	command := ioc.CreateAppCommand(app)
	return command
}

// wire.go:

var thirdSet = wire.NewSet(wire.Bind(new(redis.Cmdable), new(*redis.Client)), ioc.CreateGormMysql, ioc.CreateRedis)

var jwtSet = wire.NewSet(ioc.CreateJWT, wire.Bind(new(jwt.JWT), new(*jwt.NormalJWT)))

var webSet = wire.NewSet(ioc.CreateWeb, ioc.CreateMiddlewares)

var jobSet = wire.NewSet(ioc.CreateJobs)

var logSet = wire.NewSet(ioc.CreateLogger)

var ossSet = wire.NewSet(ioc.CreateOSSClient)

var brandMatchSet = wire.NewSet(web.NewBrandMatchHandler, service.NewBrandMatchService)

var brandSet = wire.NewSet(repository.NewCacheBrandRepo, wire.Bind(new(repository.BrandRepo), new(*repository.CacheBrandRepo)), dao.NewGORMBrandDao, wire.Bind(new(dao.BrandDao), new(*dao.GORMBrandDao)))

var circleShopSet = wire.NewSet(repository.NewCacheCircleShopRepo, wire.Bind(new(repository.CircleShopRepo), new(*repository.CacheCircleShopRepo)), dao.NewGORMCircleShopDao, wire.Bind(new(dao.CircleShopDao), new(*dao.GORMCircleShopDao)))

var userSet = wire.NewSet(service.NewUserService, repository.NewCacheUserRepo, wire.Bind(new(repository.UserRepo), new(*repository.CacheUserRepo)), dao.NewGORMUserDao, wire.Bind(new(dao.UserDao), new(*dao.GORMUserDao)))

var callSet = wire.NewSet(web.NewCallHandler, ioc.CreateCallService, repository.NewCacheCallRepo, wire.Bind(new(repository.CallRepo), new(*repository.CacheCallRepo)), dao.NewGORMCallRecordDao, wire.Bind(new(dao.CallRecordDao), new(*dao.GORMCallRecordDao)))

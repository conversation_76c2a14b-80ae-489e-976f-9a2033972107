package repository

import (
	"context"
	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ElmShopRepo 饿了么店铺仓储接口
type ElmShopRepo interface {
	// Create 创建店铺
	Create(ctx context.Context, shop domain.ElmShop) error
	// Update 更新店铺
	Update(ctx context.Context, shop domain.ElmShop) error
	// Delete 删除店铺
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询店铺
	GetByID(ctx context.Context, id int64) (domain.ElmShop, error)
	// GetByCode 根据店铺编码查询店铺
	GetByCode(ctx context.Context, code string) (domain.ElmShop, error)
	// ListByEmployeeID 根据员工ID查询店铺列表
	ListByEmployeeID(ctx context.Context, employeeID int64) ([]domain.ElmShop, error)
	// List 分页查询店铺列表
	List(ctx context.Context, offset, limit int) ([]domain.ElmShop, error)
	// Count 统计店铺总数
	Count(ctx context.Context) (int64, error)
}

type CacheElmShopRepo struct {
	dao dao.ElmShopDao
}

func NewCacheElmShopRepo(dao dao.ElmShopDao) *CacheElmShopRepo {
	return &CacheElmShopRepo{dao: dao}
}

func (r *CacheElmShopRepo) toModel(shop domain.ElmShop) model.ElmShop {
	return model.ElmShop{
		ID:                 shop.ID,
		CreatedAt:          shop.CreatedAt,
		UpdatedAt:          shop.UpdatedAt,
		Name:               shop.Name,
		Logo:               shop.Logo,
		EmployeeID:         shop.EmployeeID,
		OriShopID:          shop.OriShopID,
		ShopCode:           shop.ShopCode,
		Longitude:          shop.Longitude,
		Latitude:           shop.Latitude,
		Province:           shop.Province,
		City:               shop.City,
		District:           shop.District,
		Address:            shop.Address,
		BusinessDistrict:   shop.BusinessDistrict,
		Type:               int64(shop.Type),
		Status:             int64(shop.Status),
		YesterdayOrderNum:  int64(shop.YesterdayOrderNum),
		BeforeDayOrderNum:  int64(shop.BeforeDayOrderNum),
		MonthOrderNum:      int64(shop.MonthOrderNum),
		LastMonthOrderNum:  int64(shop.LastMonthOrderNum),
		WeekOrderNum:       int64(shop.WeekOrderNum),
		TotalOrderNum:      int64(shop.TotalOrderNum),
		ShopLink:           shop.ShopLink,
		IsOpen:             shop.IsOpen,
		IsValid:            shop.IsValid,
		JobPriority:        shop.JobPriority,
		Tags:               shop.Tags,
		Tel:                shop.Tel,
		MonthSale:          int64(shop.MonthSale),
		Score:              shop.Score,
		Evaluate:           int64(shop.Evaluate),
		CrawlTime:          int64(shop.CrawlTime),
		SourceType:         shop.SourceType,
		ClaimID:            shop.ClaimID,
		UsedName:           shop.UsedName,
		DistrictCode:       shop.DistrictCode,
		IntentLevel:        int64(shop.IntentLevel),
		CallTime:           int64(shop.CallTime),
		Priority:           int64(shop.Priority),
		BrandLeaderID:      shop.BrandLeaderID,
		BrandType:          int64(shop.BrandType),
		BrandID:            int64(shop.BrandID),
		CategoryName:       shop.CategoryName,
		LowerPrice:         shop.LowerPrice,
		TallPrice:          shop.TallPrice,
		IsBrand:            shop.IsBrand,
		IsNew:              shop.IsNew,
		OrderSnapshot:      shop.OrderSnapshot,
		EvaluateSnapshot:   shop.EvaluateSnapshot,
		ShopSnapshot:       shop.ShopSnapshot,
		IsCpsOpen:          int64(shop.IsCpsOpen),
		CategoryID:         int64(shop.CategoryID),
		IsDpEffect:         int64(shop.IsDpEffect),
		BusinessUpdateTime: int64(shop.BusinessUpdateTime),
		BusinessType:       shop.BusinessType,
		CpsClaimID:         shop.CpsClaimID,
		CpsEmployeeID:      shop.CpsEmployeeID,
		ShopType:           int64(shop.ShopType),
		BonusFee:           shop.BonusFee,
		ServiceFee:         shop.ServiceFee,
		MatchName:          shop.MatchName,
		SourceID:           int64(shop.SourceID),
		LastTakeTime:       shop.LastTakeTime,
	}
}

func (r *CacheElmShopRepo) toDomain(shop model.ElmShop) domain.ElmShop {
	return domain.ElmShop{
		ID:                 shop.ID,
		CreatedAt:          shop.CreatedAt,
		UpdatedAt:          shop.UpdatedAt,
		Name:               shop.Name,
		Logo:               shop.Logo,
		EmployeeID:         shop.EmployeeID,
		OriShopID:          shop.OriShopID,
		ShopCode:           shop.ShopCode,
		Longitude:          shop.Longitude,
		Latitude:           shop.Latitude,
		Province:           shop.Province,
		City:               shop.City,
		District:           shop.District,
		Address:            shop.Address,
		BusinessDistrict:   shop.BusinessDistrict,
		Type:               int(shop.Type),
		Status:             int(shop.Status),
		YesterdayOrderNum:  int(shop.YesterdayOrderNum),
		BeforeDayOrderNum:  int(shop.BeforeDayOrderNum),
		MonthOrderNum:      int(shop.MonthOrderNum),
		LastMonthOrderNum:  int(shop.LastMonthOrderNum),
		WeekOrderNum:       int(shop.WeekOrderNum),
		TotalOrderNum:      int(shop.TotalOrderNum),
		ShopLink:           shop.ShopLink,
		IsOpen:             shop.IsOpen,
		IsValid:            shop.IsValid,
		JobPriority:        shop.JobPriority,
		Tags:               shop.Tags,
		Tel:                shop.Tel,
		MonthSale:          int(shop.MonthSale),
		Score:              shop.Score,
		Evaluate:           int(shop.Evaluate),
		CrawlTime:          int(shop.CrawlTime),
		SourceType:         shop.SourceType,
		ClaimID:            shop.ClaimID,
		UsedName:           shop.UsedName,
		DistrictCode:       shop.DistrictCode,
		IntentLevel:        int(shop.IntentLevel),
		CallTime:           int(shop.CallTime),
		Priority:           int(shop.Priority),
		BrandLeaderID:      shop.BrandLeaderID,
		BrandType:          int(shop.BrandType),
		BrandID:            int(shop.BrandID),
		CategoryName:       shop.CategoryName,
		LowerPrice:         shop.LowerPrice,
		TallPrice:          shop.TallPrice,
		IsBrand:            shop.IsBrand,
		IsNew:              shop.IsNew,
		OrderSnapshot:      shop.OrderSnapshot,
		EvaluateSnapshot:   shop.EvaluateSnapshot,
		ShopSnapshot:       shop.ShopSnapshot,
		IsCpsOpen:          int(shop.IsCpsOpen),
		CategoryID:         int(shop.CategoryID),
		IsDpEffect:         int(shop.IsDpEffect),
		BusinessUpdateTime: int(shop.BusinessUpdateTime),
		BusinessType:       shop.BusinessType,
		CpsClaimID:         shop.CpsClaimID,
		CpsEmployeeID:      shop.CpsEmployeeID,
		ShopType:           int(shop.ShopType),
		BonusFee:           shop.BonusFee,
		ServiceFee:         shop.ServiceFee,
		MatchName:          shop.MatchName,
		SourceID:           int(shop.SourceID),
		LastTakeTime:       shop.LastTakeTime,
	}
}

func (r *CacheElmShopRepo) Create(ctx context.Context, shop domain.ElmShop) error {
	model := r.toModel(shop)
	_, err := r.dao.CreateShop(ctx, &model)
	return err
}

func (r *CacheElmShopRepo) Update(ctx context.Context, shop domain.ElmShop) error {
	return r.dao.UpdateShop(ctx, r.toModel(shop), []string{
		"name", "logo", "employee_id", "ori_shop_id", "shop_code", "longitude", "latitude", "province", "city",
		"district", "address", "business_district", "type", "status", "yesterday_order_num", "before_day_order_num",
		"month_order_num", "last_month_order_num", "week_order_num", "total_order_num", "shop_link", "is_open",
		"is_valid", "job_priority", "tags", "tel", "month_sale", "score", "evaluate", "crawl_time", "source_type",
		"claim_id", "used_name", "district_code", "intent_level", "call_time", "priority", "brand_leader_id",
		"brand_type", "brand_id", "category_name", "lower_price", "tall_price", "is_brand", "is_new",
		"order_snapshot", "evaluate_snapshot", "shop_snapshot", "is_cps_open", "category_id", "is_dp_effect",
		"business_update_time", "business_type", "cps_claim_id", "cps_employee_id", "shop_type", "bonus_fee",
		"service_fee", "match_name", "source_id", "last_take_time", "updated_at",
	})
}

func (r *CacheElmShopRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.DeleteShop(ctx, id)
}

func (r *CacheElmShopRepo) GetByID(ctx context.Context, id int64) (domain.ElmShop, error) {
	shop, err := r.dao.GetShopByID(ctx, id)
	if err != nil {
		return domain.ElmShop{}, err
	}
	return r.toDomain(shop), nil
}

func (r *CacheElmShopRepo) GetByCode(ctx context.Context, code string) (domain.ElmShop, error) {
	shop, err := r.dao.GetShopByCode(ctx, code)
	if err != nil {
		return domain.ElmShop{}, err
	}
	return r.toDomain(shop), nil
}

func (r *CacheElmShopRepo) ListByEmployeeID(ctx context.Context, employeeID int64) ([]domain.ElmShop, error) {
	shops, err := r.dao.ListShopsByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheElmShopRepo) List(ctx context.Context, offset, limit int) ([]domain.ElmShop, error) {
	shops, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheElmShopRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type BrandRepo interface {
	Create(ctx context.Context, brand domain.Brand) (int64, error)
	Update(ctx context.Context, brand domain.Brand, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.Brand, error)
	FindByName(ctx context.Context, name string) (domain.Brand, error)
	List(ctx context.Context, offset, limit int) ([]domain.Brand, error)
	Count(ctx context.Context) (int64, error)
	FindByFuzzyName(ctx context.Context, name string) ([]domain.Brand, error)
}

type CacheBrandRepo struct {
	brandDao dao.BrandDao
}

func NewCacheBrandRepo(brandDao dao.BrandDao) *CacheBrandRepo {
	return &CacheBrandRepo{
		brandDao: brandDao,
	}
}

func (r *CacheBrandRepo) Create(ctx context.Context, brand domain.Brand) (int64, error) {
	return r.brandDao.Create(ctx, r.toModel(brand))
}

func (r *CacheBrandRepo) Update(ctx context.Context, brand domain.Brand, fields []string) error {
	return r.brandDao.Update(ctx, r.toModel(brand), fields)
}

func (r *CacheBrandRepo) Delete(ctx context.Context, id int64) error {
	return r.brandDao.Delete(ctx, id)
}

func (r *CacheBrandRepo) FindByID(ctx context.Context, id int64) (domain.Brand, error) {
	brand, err := r.brandDao.FindByID(ctx, id)
	if err != nil {
		return domain.Brand{}, err
	}
	return r.toDomain(brand), nil
}

func (r *CacheBrandRepo) FindByName(ctx context.Context, name string) (domain.Brand, error) {
	brand, err := r.brandDao.FindByName(ctx, name)
	if err != nil {
		return domain.Brand{}, err
	}
	return r.toDomain(brand), nil
}

func (r *CacheBrandRepo) List(ctx context.Context, offset, limit int) ([]domain.Brand, error) {
	brands, err := r.brandDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.Brand
	for _, brand := range brands {
		result = append(result, r.toDomain(brand))
	}
	return result, nil
}

func (r *CacheBrandRepo) Count(ctx context.Context) (int64, error) {
	return r.brandDao.Count(ctx)
}

func (r *CacheBrandRepo) FindByFuzzyName(ctx context.Context, name string) ([]domain.Brand, error) {
	brands, err := r.brandDao.FindByFuzzyName(ctx, name)
	if err != nil {
		return nil, err
	}
	var result []domain.Brand
	for _, brand := range brands {
		result = append(result, r.toDomain(brand))
	}
	return result, nil
}

func (r *CacheBrandRepo) toModel(brand domain.Brand) model.Brand {
	return model.Brand{
		ID:               brand.ID,
		CreatedAt:        brand.CreatedAt,
		UpdatedAt:        brand.UpdatedAt,
		BrandName:        brand.BrandName,
		BrandLogo:        brand.BrandLogo,
		DetailImg:        brand.DetailImg,
		Desc:             brand.Desc,
		DetailDesc:       brand.DetailDesc,
		Status:           brand.Status,
		Weight:           brand.Weight,
		Policy:           brand.Policy,
		StoreNum:         brand.StoreNum,
		ContactName:      brand.ContactName,
		ContactPhone:     brand.ContactPhone,
		Company:          brand.Company,
		Average:          brand.Average,
		CategoryID:       brand.CategoryID,
		InStoreNum:       brand.InStoreNum,
		City:             brand.City,
		Province:         brand.Province,
		CreateType:       brand.CreateType,
		CrawlTime:        brand.CrawlTime,
		LockStatus:       brand.LockStatus,
		OperationMode:    brand.OperationMode,
		BrandCode:        brand.BrandCode,
		AreaMin:          brand.AreaMin,
		AreaMax:          brand.AreaMax,
		ShowType:         brand.ShowType,
		PlatformStoreNum: brand.PlatformStoreNum,
		MinMonthSale:     brand.MinMonthSale,
		MaxMonthSale:     brand.MaxMonthSale,
	}
}

func (r *CacheBrandRepo) toDomain(brand model.Brand) domain.Brand {
	return domain.Brand{
		ID:               brand.ID,
		CreatedAt:        brand.CreatedAt,
		UpdatedAt:        brand.UpdatedAt,
		BrandName:        brand.BrandName,
		BrandLogo:        brand.BrandLogo,
		DetailImg:        brand.DetailImg,
		Desc:             brand.Desc,
		DetailDesc:       brand.DetailDesc,
		Status:           brand.Status,
		Weight:           brand.Weight,
		Policy:           brand.Policy,
		StoreNum:         brand.StoreNum,
		ContactName:      brand.ContactName,
		ContactPhone:     brand.ContactPhone,
		Company:          brand.Company,
		Average:          brand.Average,
		CategoryID:       brand.CategoryID,
		InStoreNum:       brand.InStoreNum,
		City:             brand.City,
		Province:         brand.Province,
		CreateType:       brand.CreateType,
		CrawlTime:        brand.CrawlTime,
		LockStatus:       brand.LockStatus,
		OperationMode:    brand.OperationMode,
		BrandCode:        brand.BrandCode,
		AreaMin:          brand.AreaMin,
		AreaMax:          brand.AreaMax,
		ShowType:         brand.ShowType,
		PlatformStoreNum: brand.PlatformStoreNum,
		MinMonthSale:     brand.MinMonthSale,
		MaxMonthSale:     brand.MaxMonthSale,
	}
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueTaskRepo 线索任务仓储接口
type ClueTaskRepo interface {
	// Create 创建线索任务
	Create(ctx context.Context, task domain.ClueTask) (int64, error)
	// Update 更新线索任务
	Update(ctx context.Context, task domain.ClueTask, fields []string) error
	// Delete 删除线索任务
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索任务
	FindByID(ctx context.Context, id int64) (domain.ClueTask, error)
	// FindByClueID 根据线索ID查询线索任务列表
	FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueTask, error)
	// FindByAssigneeID 根据负责人ID查询线索任务列表
	FindByAssigneeID(ctx context.Context, assigneeID int64) ([]domain.ClueTask, error)
	// FindByStatus 根据状态查询线索任务列表
	FindByStatus(ctx context.Context, status int64) ([]domain.ClueTask, error)
	// List 分页查询线索任务列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueTask, error)
	// Count 统计线索任务总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueTaskRepo struct {
	clueTaskDao dao.ClueTaskDao
}

// NewCacheClueTaskRepo 创建线索任务仓储实现
func NewCacheClueTaskRepo(clueTaskDao dao.ClueTaskDao) *CacheClueTaskRepo {
	return &CacheClueTaskRepo{
		clueTaskDao: clueTaskDao,
	}
}

func (r *CacheClueTaskRepo) Create(ctx context.Context, task domain.ClueTask) (int64, error) {
	return r.clueTaskDao.Create(ctx, r.toModel(task))
}

func (r *CacheClueTaskRepo) Update(ctx context.Context, task domain.ClueTask, fields []string) error {
	return r.clueTaskDao.Update(ctx, r.toModel(task), fields)
}

func (r *CacheClueTaskRepo) Delete(ctx context.Context, id int64) error {
	return r.clueTaskDao.Delete(ctx, id)
}

func (r *CacheClueTaskRepo) FindByID(ctx context.Context, id int64) (domain.ClueTask, error) {
	model, err := r.clueTaskDao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueTask{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueTaskRepo) FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueTask, error) {
	models, err := r.clueTaskDao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTask, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskRepo) FindByAssigneeID(ctx context.Context, assigneeID int64) ([]domain.ClueTask, error) {
	models, err := r.clueTaskDao.FindByAssigneeID(ctx, assigneeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTask, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskRepo) FindByStatus(ctx context.Context, status int64) ([]domain.ClueTask, error) {
	models, err := r.clueTaskDao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTask, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueTask, error) {
	models, err := r.clueTaskDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTask, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskRepo) Count(ctx context.Context) (int64, error) {
	return r.clueTaskDao.Count(ctx)
}

func (r *CacheClueTaskRepo) toModel(task domain.ClueTask) model.ClueTask {
	return model.ClueTask{
		ID:           task.ID,
		ClueID:       task.ClueID,
		Title:        task.Title,
		Content:      task.Content,
		Priority:     int64(task.Priority),
		Deadline:     task.Deadline,
		AssigneeID:   task.AssigneeID,
		AssigneeName: task.AssigneeName,
		CreatorID:    task.CreatorID,
		CreatorName:  task.CreatorName,
		Status:       int64(task.Status),
		CreatedAt:    task.CreatedAt,
		UpdatedAt:    task.UpdatedAt,
		DeletedAt:    task.DeletedAt,
	}
}

func (r *CacheClueTaskRepo) toDomain(task model.ClueTask) domain.ClueTask {
	return domain.ClueTask{
		ID:           task.ID,
		ClueID:       task.ClueID,
		Title:        task.Title,
		Content:      task.Content,
		Priority:     int(task.Priority),
		Deadline:     task.Deadline,
		AssigneeID:   task.AssigneeID,
		AssigneeName: task.AssigneeName,
		CreatorID:    task.CreatorID,
		CreatorName:  task.CreatorName,
		Status:       int(task.Status),
		CreatedAt:    task.CreatedAt,
		UpdatedAt:    task.UpdatedAt,
		DeletedAt:    task.DeletedAt,
	}
}

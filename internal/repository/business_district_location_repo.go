package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type BusinessDistrictLocationRepo interface {
	Create(ctx context.Context, location domain.BusinessDistrictLocation) (int64, error)
	Update(ctx context.Context, location domain.BusinessDistrictLocation, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.BusinessDistrictLocation, error)
	FindByName(ctx context.Context, name string) (domain.BusinessDistrictLocation, error)
	List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictLocation, error)
	ListByCity(ctx context.Context, city string) ([]domain.BusinessDistrictLocation, error)
	Count(ctx context.Context) (int64, error)
}

type CacheBusinessDistrictLocationRepo struct {
	locationDao dao.BusinessDistrictLocationDao
}

func NewCacheBusinessDistrictLocationRepo(locationDao dao.BusinessDistrictLocationDao) *CacheBusinessDistrictLocationRepo {
	return &CacheBusinessDistrictLocationRepo{
		locationDao: locationDao,
	}
}

func (r *CacheBusinessDistrictLocationRepo) Create(ctx context.Context, location domain.BusinessDistrictLocation) (int64, error) {
	return r.locationDao.Create(ctx, r.toModel(location))
}

func (r *CacheBusinessDistrictLocationRepo) Update(ctx context.Context, location domain.BusinessDistrictLocation, fields []string) error {
	return r.locationDao.Update(ctx, r.toModel(location), fields)
}

func (r *CacheBusinessDistrictLocationRepo) Delete(ctx context.Context, id int64) error {
	return r.locationDao.Delete(ctx, id)
}

func (r *CacheBusinessDistrictLocationRepo) FindByID(ctx context.Context, id int64) (domain.BusinessDistrictLocation, error) {
	location, err := r.locationDao.FindByID(ctx, id)
	if err != nil {
		return domain.BusinessDistrictLocation{}, err
	}
	return r.toDomain(location), nil
}

func (r *CacheBusinessDistrictLocationRepo) FindByName(ctx context.Context, name string) (domain.BusinessDistrictLocation, error) {
	location, err := r.locationDao.FindByName(ctx, name)
	if err != nil {
		return domain.BusinessDistrictLocation{}, err
	}
	return r.toDomain(location), nil
}

func (r *CacheBusinessDistrictLocationRepo) List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictLocation, error) {
	locations, err := r.locationDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.BusinessDistrictLocation
	for _, location := range locations {
		result = append(result, r.toDomain(location))
	}
	return result, nil
}

func (r *CacheBusinessDistrictLocationRepo) ListByCity(ctx context.Context, city string) ([]domain.BusinessDistrictLocation, error) {
	locations, err := r.locationDao.ListByCity(ctx, city)
	if err != nil {
		return nil, err
	}
	var result []domain.BusinessDistrictLocation
	for _, location := range locations {
		result = append(result, r.toDomain(location))
	}
	return result, nil
}

func (r *CacheBusinessDistrictLocationRepo) Count(ctx context.Context) (int64, error) {
	return r.locationDao.Count(ctx)
}

func (r *CacheBusinessDistrictLocationRepo) toModel(location domain.BusinessDistrictLocation) model.BusinessDistrictLocation {
	return model.BusinessDistrictLocation{
		ID:               location.ID,
		CreatedAt:        location.CreatedAt,
		UpdatedAt:        location.UpdatedAt,
		BusinessDistrict: location.BusinessDistrict,
		Province:         location.Province,
		City:             location.City,
		District:         location.District,
		Location:         location.Location,
		FormattedAddress: location.FormattedAddress,
		CrawlTime:        location.CrawlTime,
		Level:            location.Level,
		Type:             location.Type,
		CrawlNum:         location.CrawlNum,
	}
}

func (r *CacheBusinessDistrictLocationRepo) toDomain(location model.BusinessDistrictLocation) domain.BusinessDistrictLocation {
	return domain.BusinessDistrictLocation{
		ID:               location.ID,
		CreatedAt:        location.CreatedAt,
		UpdatedAt:        location.UpdatedAt,
		BusinessDistrict: location.BusinessDistrict,
		Province:         location.Province,
		City:             location.City,
		District:         location.District,
		Location:         location.Location,
		FormattedAddress: location.FormattedAddress,
		CrawlTime:        location.CrawlTime,
		Level:            location.Level,
		Type:             location.Type,
		CrawlNum:         location.CrawlNum,
	}
}

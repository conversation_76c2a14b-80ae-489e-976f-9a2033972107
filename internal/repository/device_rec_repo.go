package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// DeviceRecRepo 设备记录仓储接口
type DeviceRecRepo interface {
	// Create 创建设备记录
	Create(ctx context.Context, rec domain.DeviceRec) (int64, error)
	// Update 更新设备记录
	Update(ctx context.Context, rec domain.DeviceRec, fields []string) error
	// Delete 删除设备记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询设备记录
	FindByID(ctx context.Context, id int64) (domain.DeviceRec, error)
	// FindByDeviceID 根据设备ID查询设备记录
	FindByDeviceID(ctx context.Context, deviceID string) (domain.DeviceRec, error)
	// FindByEmployeeID 根据员工ID查询设备记录列表
	FindByEmployeeID(ctx context.Context, employeeID int64) ([]domain.DeviceRec, error)
	// FindByStatus 根据状态查询设备记录列表
	FindByStatus(ctx context.Context, status int) ([]domain.DeviceRec, error)
	// FindByDeviceType 根据设备类型查询设备记录列表
	FindByDeviceType(ctx context.Context, deviceType int) ([]domain.DeviceRec, error)
	// FindByLastActiveTime 根据最后活跃时间范围查询设备记录列表
	FindByLastActiveTime(ctx context.Context, startTime, endTime time.Time) ([]domain.DeviceRec, error)
	// List 查询设备记录列表
	List(ctx context.Context, offset, limit int) ([]domain.DeviceRec, error)
	// Count 统计设备记录总数
	Count(ctx context.Context) (int64, error)
}

type CacheDeviceRecRepo struct {
	deviceRecDao dao.DeviceRecDao
}

// NewCacheDeviceRecRepo 创建设备记录仓储实现
func NewCacheDeviceRecRepo(deviceRecDao dao.DeviceRecDao) *CacheDeviceRecRepo {
	return &CacheDeviceRecRepo{
		deviceRecDao: deviceRecDao,
	}
}

func (r *CacheDeviceRecRepo) Create(ctx context.Context, rec domain.DeviceRec) (int64, error) {
	return r.deviceRecDao.Create(ctx, r.toModel(rec))
}

func (r *CacheDeviceRecRepo) Update(ctx context.Context, rec domain.DeviceRec, fields []string) error {
	return r.deviceRecDao.Update(ctx, r.toModel(rec), fields)
}

func (r *CacheDeviceRecRepo) Delete(ctx context.Context, id int64) error {
	return r.deviceRecDao.Delete(ctx, id)
}

func (r *CacheDeviceRecRepo) FindByID(ctx context.Context, id int64) (domain.DeviceRec, error) {
	model, err := r.deviceRecDao.FindByID(ctx, id)
	if err != nil {
		return domain.DeviceRec{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDeviceRecRepo) FindByDeviceID(ctx context.Context, deviceID string) (domain.DeviceRec, error) {
	model, err := r.deviceRecDao.FindByDeviceID(ctx, deviceID)
	if err != nil {
		return domain.DeviceRec{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDeviceRecRepo) FindByEmployeeID(ctx context.Context, employeeID int64) ([]domain.DeviceRec, error) {
	models, err := r.deviceRecDao.FindByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DeviceRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDeviceRecRepo) FindByStatus(ctx context.Context, status int) ([]domain.DeviceRec, error) {
	models, err := r.deviceRecDao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DeviceRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDeviceRecRepo) FindByDeviceType(ctx context.Context, deviceType int) ([]domain.DeviceRec, error) {
	models, err := r.deviceRecDao.FindByDeviceType(ctx, deviceType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DeviceRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDeviceRecRepo) FindByLastActiveTime(ctx context.Context, startTime, endTime time.Time) ([]domain.DeviceRec, error) {
	models, err := r.deviceRecDao.FindByLastActiveTime(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DeviceRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDeviceRecRepo) List(ctx context.Context, offset, limit int) ([]domain.DeviceRec, error) {
	models, err := r.deviceRecDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DeviceRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDeviceRecRepo) Count(ctx context.Context) (int64, error) {
	return r.deviceRecDao.Count(ctx)
}

func (r *CacheDeviceRecRepo) toModel(rec domain.DeviceRec) model.DeviceRec {
	return model.DeviceRec{
		ID:             rec.ID,
		DeviceID:       rec.DeviceID,
		DeviceType:     rec.DeviceType,
		DeviceName:     rec.DeviceName,
		DeviceModel:    rec.DeviceModel,
		DeviceVersion:  rec.DeviceVersion,
		EmployeeID:     rec.EmployeeID,
		EmployeeName:   rec.EmployeeName,
		Status:         rec.Status,
		LastActiveTime: rec.LastActiveTime,
		CreatedAt:      rec.CreatedAt,
		UpdatedAt:      rec.UpdatedAt,
		DeletedAt:      rec.DeletedAt,
	}
}

func (r *CacheDeviceRecRepo) toDomain(rec model.DeviceRec) domain.DeviceRec {
	return domain.DeviceRec{
		ID:             rec.ID,
		DeviceID:       rec.DeviceID,
		DeviceType:     rec.DeviceType,
		DeviceName:     rec.DeviceName,
		DeviceModel:    rec.DeviceModel,
		DeviceVersion:  rec.DeviceVersion,
		EmployeeID:     rec.EmployeeID,
		EmployeeName:   rec.EmployeeName,
		Status:         rec.Status,
		LastActiveTime: rec.LastActiveTime,
		CreatedAt:      rec.CreatedAt,
		UpdatedAt:      rec.UpdatedAt,
		DeletedAt:      rec.DeletedAt,
	}
}

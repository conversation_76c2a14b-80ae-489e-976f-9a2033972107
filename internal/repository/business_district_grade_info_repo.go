package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type BusinessDistrictGradeInfoRepo interface {
	Create(ctx context.Context, info domain.BusinessDistrictGradeInfo) (int64, error)
	Update(ctx context.Context, info domain.BusinessDistrictGradeInfo, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.BusinessDistrictGradeInfo, error)
	List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictGradeInfo, error)
	Count(ctx context.Context) (int64, error)
}

type CacheBusinessDistrictGradeInfoRepo struct {
	gradeInfoDao dao.BusinessDistrictGradeInfoDao
}

func NewCacheBusinessDistrictGradeInfoRepo(gradeInfoDao dao.BusinessDistrictGradeInfoDao) *CacheBusinessDistrictGradeInfoRepo {
	return &CacheBusinessDistrictGradeInfoRepo{
		gradeInfoDao: gradeInfoDao,
	}
}

func (r *CacheBusinessDistrictGradeInfoRepo) Create(ctx context.Context, info domain.BusinessDistrictGradeInfo) (int64, error) {
	return r.gradeInfoDao.Create(ctx, r.toModel(info))
}

func (r *CacheBusinessDistrictGradeInfoRepo) Update(ctx context.Context, info domain.BusinessDistrictGradeInfo, fields []string) error {
	return r.gradeInfoDao.Update(ctx, r.toModel(info), fields)
}

func (r *CacheBusinessDistrictGradeInfoRepo) Delete(ctx context.Context, id int64) error {
	return r.gradeInfoDao.Delete(ctx, id)
}

func (r *CacheBusinessDistrictGradeInfoRepo) FindByID(ctx context.Context, id int64) (domain.BusinessDistrictGradeInfo, error) {
	info, err := r.gradeInfoDao.FindByID(ctx, id)
	if err != nil {
		return domain.BusinessDistrictGradeInfo{}, err
	}
	return r.toDomain(info), nil
}

func (r *CacheBusinessDistrictGradeInfoRepo) List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictGradeInfo, error) {
	infos, err := r.gradeInfoDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.BusinessDistrictGradeInfo
	for _, info := range infos {
		result = append(result, r.toDomain(info))
	}
	return result, nil
}

func (r *CacheBusinessDistrictGradeInfoRepo) Count(ctx context.Context) (int64, error) {
	return r.gradeInfoDao.Count(ctx)
}

func (r *CacheBusinessDistrictGradeInfoRepo) toModel(info domain.BusinessDistrictGradeInfo) model.BusinessDistrictGradeInfo {
	return model.BusinessDistrictGradeInfo{
		ID:               info.ID,
		GradeID:          info.GradeID,
		BusinessDistrict: info.BusinessDistrict,
		City:             info.City,
		Province:         info.Province,
		Area:             info.Area,
		Address:          info.Address,
		Longitude:        info.Longitude,
		Latitude:         info.Latitude,
		Status:           info.Status,
		CreatedAt:        info.CreatedAt,
		UpdatedAt:        info.UpdatedAt,
		DeletedAt:        info.DeletedAt,
	}
}

func (r *CacheBusinessDistrictGradeInfoRepo) toDomain(info model.BusinessDistrictGradeInfo) domain.BusinessDistrictGradeInfo {
	return domain.BusinessDistrictGradeInfo{
		ID:               info.ID,
		GradeID:          info.GradeID,
		BusinessDistrict: info.BusinessDistrict,
		City:             info.City,
		Province:         info.Province,
		Area:             info.Area,
		Address:          info.Address,
		Longitude:        info.Longitude,
		Latitude:         info.Latitude,
		Status:           info.Status,
		CreatedAt:        info.CreatedAt,
		UpdatedAt:        info.UpdatedAt,
		DeletedAt:        info.DeletedAt,
	}
}

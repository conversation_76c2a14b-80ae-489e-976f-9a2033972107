package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueTaskInfoRepo 线索任务详情仓储接口
type ClueTaskInfoRepo interface {
	// Create 创建线索任务详情
	Create(ctx context.Context, info domain.ClueTaskInfo) (int64, error)
	// Update 更新线索任务详情
	Update(ctx context.Context, info domain.ClueTaskInfo, fields []string) error
	// Delete 删除线索任务详情
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索任务详情
	FindByID(ctx context.Context, id int64) (domain.ClueTaskInfo, error)
	// FindByTaskID 根据任务ID查询线索任务详情列表
	FindByTaskID(ctx context.Context, taskID int64) ([]domain.ClueTaskInfo, error)
	// FindByType 根据详情类型查询线索任务详情列表
	FindByType(ctx context.Context, infoType int) ([]domain.ClueTaskInfo, error)
	// List 分页查询线索任务详情列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueTaskInfo, error)
	// Count 统计线索任务详情总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueTaskInfoRepo struct {
	clueTaskInfoDao dao.ClueTaskInfoDao
}

// NewCacheClueTaskInfoRepo 创建线索任务详情仓储实现
func NewCacheClueTaskInfoRepo(clueTaskInfoDao dao.ClueTaskInfoDao) *CacheClueTaskInfoRepo {
	return &CacheClueTaskInfoRepo{
		clueTaskInfoDao: clueTaskInfoDao,
	}
}

func (r *CacheClueTaskInfoRepo) Create(ctx context.Context, info domain.ClueTaskInfo) (int64, error) {
	return r.clueTaskInfoDao.Create(ctx, r.toModel(info))
}

func (r *CacheClueTaskInfoRepo) Update(ctx context.Context, info domain.ClueTaskInfo, fields []string) error {
	return r.clueTaskInfoDao.Update(ctx, r.toModel(info), fields)
}

func (r *CacheClueTaskInfoRepo) Delete(ctx context.Context, id int64) error {
	return r.clueTaskInfoDao.Delete(ctx, id)
}

func (r *CacheClueTaskInfoRepo) FindByID(ctx context.Context, id int64) (domain.ClueTaskInfo, error) {
	model, err := r.clueTaskInfoDao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueTaskInfo{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueTaskInfoRepo) FindByTaskID(ctx context.Context, taskID int64) ([]domain.ClueTaskInfo, error) {
	models, err := r.clueTaskInfoDao.FindByTaskID(ctx, taskID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTaskInfo, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskInfoRepo) FindByType(ctx context.Context, infoType int) ([]domain.ClueTaskInfo, error) {
	models, err := r.clueTaskInfoDao.FindByType(ctx, infoType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTaskInfo, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskInfoRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueTaskInfo, error) {
	models, err := r.clueTaskInfoDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueTaskInfo, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueTaskInfoRepo) Count(ctx context.Context) (int64, error) {
	return r.clueTaskInfoDao.Count(ctx)
}

func (r *CacheClueTaskInfoRepo) toModel(info domain.ClueTaskInfo) model.ClueTaskInfo {
	return model.ClueTaskInfo{
		ID:          info.ID,
		TaskID:      info.TaskID,
		Type:        info.Type,
		Content:     info.Content,
		CreatorID:   info.CreatorID,
		CreatorName: info.CreatorName,
		Status:      info.Status,
		CreatedAt:   info.CreatedAt,
		UpdatedAt:   info.UpdatedAt,
		DeletedAt:   info.DeletedAt,
	}
}

func (r *CacheClueTaskInfoRepo) toDomain(info model.ClueTaskInfo) domain.ClueTaskInfo {
	return domain.ClueTaskInfo{
		ID:          info.ID,
		TaskID:      info.TaskID,
		Type:        info.Type,
		Content:     info.Content,
		CreatorID:   info.CreatorID,
		CreatorName: info.CreatorName,
		Status:      info.Status,
		CreatedAt:   info.CreatedAt,
		UpdatedAt:   info.UpdatedAt,
		DeletedAt:   info.DeletedAt,
	}
}

package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// CallStatisRepo 通话统计仓储接口
type CallStatisRepo interface {
	// Create 创建通话统计
	Create(ctx context.Context, statis domain.CallStatis) (int64, error)
	// Update 更新通话统计
	Update(ctx context.Context, statis domain.CallStatis, fields []string) error
	// Delete 删除通话统计
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询通话统计
	FindByID(ctx context.Context, id int64) (domain.CallStatis, error)
	// FindByEmployeeID 根据员工ID查询通话统计列表
	FindByEmployeeID(ctx context.Context, employeeID int64) ([]domain.CallStatis, error)
	// FindByDate 根据日期查询通话统计列表
	FindByDate(ctx context.Context, date time.Time) ([]domain.CallStatis, error)
	// FindByDateRange 根据日期范围查询通话统计列表
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]domain.CallStatis, error)
	// List 查询通话统计列表
	List(ctx context.Context, offset, limit int) ([]domain.CallStatis, error)
	// Count 统计通话统计总数
	Count(ctx context.Context) (int64, error)
}

type CacheCallStatisRepo struct {
	dao dao.CallStatisDao
}

// NewCacheCallStatisRepo 创建通话统计仓储实现
func NewCacheCallStatisRepo(dao dao.CallStatisDao) *CacheCallStatisRepo {
	return &CacheCallStatisRepo{
		dao: dao,
	}
}

func (r *CacheCallStatisRepo) Create(ctx context.Context, statis domain.CallStatis) (int64, error) {
	return r.dao.Create(ctx, r.toModel(statis))
}

func (r *CacheCallStatisRepo) Update(ctx context.Context, statis domain.CallStatis, fields []string) error {
	return r.dao.Update(ctx, r.toModel(statis), fields)
}

func (r *CacheCallStatisRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheCallStatisRepo) FindByID(ctx context.Context, id int64) (domain.CallStatis, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.CallStatis{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheCallStatisRepo) FindByEmployeeID(ctx context.Context, employeeID int64) ([]domain.CallStatis, error) {
	models, err := r.dao.FindByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	var result []domain.CallStatis
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheCallStatisRepo) FindByDate(ctx context.Context, date time.Time) ([]domain.CallStatis, error) {
	models, err := r.dao.FindByDate(ctx, date)
	if err != nil {
		return nil, err
	}
	var result []domain.CallStatis
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheCallStatisRepo) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]domain.CallStatis, error) {
	models, err := r.dao.FindByDateRange(ctx, startDate, endDate)
	if err != nil {
		return nil, err
	}
	var result []domain.CallStatis
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheCallStatisRepo) List(ctx context.Context, offset, limit int) ([]domain.CallStatis, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.CallStatis
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheCallStatisRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

func (r *CacheCallStatisRepo) toModel(statis domain.CallStatis) model.CallStatis {
	return model.CallStatis{
		ID:             statis.ID,
		CreatedAt:      statis.CreatedAt,
		UpdatedAt:      statis.UpdatedAt,
		DeletedAt:      statis.DeletedAt,
		EmployeeID:     statis.EmployeeID,
		EmployeeName:   statis.EmployeeName,
		Date:           statis.Date,
		TotalCalls:     statis.TotalCalls,
		ConnectedCalls: statis.ConnectedCalls,
		TotalDuration:  statis.TotalDuration,
		AvgDuration:    statis.AvgDuration,
	}
}

func (r *CacheCallStatisRepo) toDomain(statis model.CallStatis) domain.CallStatis {
	return domain.CallStatis{
		ID:             statis.ID,
		CreatedAt:      statis.CreatedAt,
		UpdatedAt:      statis.UpdatedAt,
		DeletedAt:      statis.DeletedAt,
		EmployeeID:     statis.EmployeeID,
		EmployeeName:   statis.EmployeeName,
		Date:           statis.Date,
		TotalCalls:     statis.TotalCalls,
		ConnectedCalls: statis.ConnectedCalls,
		TotalDuration:  statis.TotalDuration,
		AvgDuration:    statis.AvgDuration,
	}
}

package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// OperateClickRepo 操作点击仓储接口
type OperateClickRepo interface {
	// Create 创建操作点击
	Create(ctx context.Context, operateClick domain.OperateClick) error
	// Update 更新操作点击
	Update(ctx context.Context, operateClick domain.OperateClick) error
	// Delete 删除操作点击
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询操作点击
	FindByID(ctx context.Context, id int64) (domain.OperateClick, error)
	// FindByShopID 根据店铺ID查询操作点击列表
	FindByShopID(ctx context.Context, shopID string) ([]domain.OperateClick, error)
	// FindByOperatorID 根据运营人员ID查询操作点击列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]domain.OperateClick, error)
	// FindByClickType 根据点击类型查询操作点击列表
	FindByClickType(ctx context.Context, clickType int) ([]domain.OperateClick, error)
	// FindByDateRange 根据时间范围查询操作点击列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.OperateClick, error)
	// List 查询操作点击列表
	List(ctx context.Context, offset, limit int) ([]domain.OperateClick, error)
	// Count 统计操作点击总数
	Count(ctx context.Context) (int64, error)
}

type CacheOperateClickRepo struct {
	dao dao.OperateClickDao
}

// NewCacheOperateClickRepo 创建操作点击仓储实现
func NewCacheOperateClickRepo(dao dao.OperateClickDao) *CacheOperateClickRepo {
	return &CacheOperateClickRepo{
		dao: dao,
	}
}

func (r *CacheOperateClickRepo) toModel(operateClick domain.OperateClick) model.OperateClick {
	return model.OperateClick{
		ID:           operateClick.ID,
		ShopID:       operateClick.ShopID,
		ShopName:     operateClick.ShopName,
		OperatorID:   operateClick.OperatorID,
		OperatorName: operateClick.OperatorName,
		ClickType:    operateClick.ClickType,
		ClickTarget:  operateClick.ClickTarget,
		ClickTime:    operateClick.ClickTime,
		ClickIP:      operateClick.ClickIP,
		ClickUA:      operateClick.ClickUA,
		ClickReferer: operateClick.ClickReferer,
		CreatedAt:    operateClick.CreatedAt,
		UpdatedAt:    operateClick.UpdatedAt,
		DeletedAt:    operateClick.DeletedAt,
	}
}

func (r *CacheOperateClickRepo) toDomain(model model.OperateClick) domain.OperateClick {
	return domain.OperateClick{
		ID:           model.ID,
		ShopID:       model.ShopID,
		ShopName:     model.ShopName,
		OperatorID:   model.OperatorID,
		OperatorName: model.OperatorName,
		ClickType:    model.ClickType,
		ClickTarget:  model.ClickTarget,
		ClickTime:    model.ClickTime,
		ClickIP:      model.ClickIP,
		ClickUA:      model.ClickUA,
		ClickReferer: model.ClickReferer,
		CreatedAt:    model.CreatedAt,
		UpdatedAt:    model.UpdatedAt,
		DeletedAt:    model.DeletedAt,
	}
}

func (r *CacheOperateClickRepo) toDomains(models []model.OperateClick) []domain.OperateClick {
	domains := make([]domain.OperateClick, len(models))
	for i, m := range models {
		domains[i] = r.toDomain(m)
	}
	return domains
}

func (r *CacheOperateClickRepo) Create(ctx context.Context, operateClick domain.OperateClick) error {
	_, err := r.dao.Create(ctx, r.toModel(operateClick))
	return err
}

func (r *CacheOperateClickRepo) Update(ctx context.Context, operateClick domain.OperateClick) error {
	return r.dao.Update(ctx, r.toModel(operateClick), []string{"shop_id", "shop_name", "operator_id", "operator_name", "click_type", "click_target", "click_time", "click_ip", "click_ua", "click_referer", "updated_at"})
}

func (r *CacheOperateClickRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheOperateClickRepo) FindByID(ctx context.Context, id int64) (domain.OperateClick, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.OperateClick{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheOperateClickRepo) FindByShopID(ctx context.Context, shopID string) ([]domain.OperateClick, error) {
	models, err := r.dao.FindByShopID(ctx, shopID)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheOperateClickRepo) FindByOperatorID(ctx context.Context, operatorID int64) ([]domain.OperateClick, error) {
	models, err := r.dao.FindByOperatorID(ctx, operatorID)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheOperateClickRepo) FindByClickType(ctx context.Context, clickType int) ([]domain.OperateClick, error) {
	models, err := r.dao.FindByClickType(ctx, clickType)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheOperateClickRepo) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.OperateClick, error) {
	models, err := r.dao.FindByDateRange(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheOperateClickRepo) List(ctx context.Context, offset, limit int) ([]domain.OperateClick, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheOperateClickRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

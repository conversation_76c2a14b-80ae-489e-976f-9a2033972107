package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type OperateShopRepo interface {
	// Create 创建运营商店铺
	Create(ctx context.Context, shop domain.OperateShop) (int64, error)
	// Update 更新运营商店铺
	Update(ctx context.Context, shop domain.OperateShop, fields []string) error
	// Delete 删除运营商店铺
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询运营商店铺
	GetByID(ctx context.Context, id int64) (domain.OperateShop, error)
	// GetByName 根据店铺名称查询运营商店铺列表
	GetByName(ctx context.Context, name string) ([]domain.OperateShop, error)
	// GetByCity 根据店铺所在城市查询运营商店铺列表
	GetByCity(ctx context.Context, city string) ([]domain.OperateShop, error)
	// GetByBrandID 根据品牌ID查询运营商店铺列表
	GetByBrandID(ctx context.Context, brandID int64) ([]domain.OperateShop, error)
	// GetByStatus 根据店铺状态查询运营商店铺列表
	GetByStatus(ctx context.Context, status int) ([]domain.OperateShop, error)
	// GetByUserID 根据用户ID查询运营商店铺列表
	GetByUserID(ctx context.Context, userID int64) ([]domain.OperateShop, error)
	// GetByPlatform 根据平台查询运营商店铺列表
	GetByPlatform(ctx context.Context, platform int) ([]domain.OperateShop, error)
	// GetByClueID 根据线索ID查询运营商店铺列表
	GetByClueID(ctx context.Context, clueID int64) ([]domain.OperateShop, error)
	// List 查询运营商店铺列表
	List(ctx context.Context, offset, limit int) ([]domain.OperateShop, error)
	// Count 统计运营商店铺总数
	Count(ctx context.Context) (int64, error)
}

type CacheOperateShopRepo struct {
	dao dao.OperateShopDao
}

func NewCacheOperateShopRepo(dao dao.OperateShopDao) *CacheOperateShopRepo {
	return &CacheOperateShopRepo{
		dao: dao,
	}
}

func (r *CacheOperateShopRepo) toModel(shop domain.OperateShop) model.OperateShop {
	return model.OperateShop{
		ID:              shop.ID,
		Name:            shop.Name,
		CreateTime:      shop.CreateTime,
		UpdateTime:      shop.UpdateTime,
		CategoryID:      shop.CategoryID,
		Tel:             shop.Tel,
		Address:         shop.Address,
		IsDine:          shop.IsDine,
		IsNew:           shop.IsNew,
		StoreArea:       shop.StoreArea,
		BrandID:         shop.BrandID,
		Platform:        shop.Platform,
		ShopCode:        shop.ShopCode,
		IsCharge:        shop.IsCharge,
		Fee:             shop.Fee,
		CommissionRate:  shop.CommissionRate,
		Remark:          shop.Remark,
		ClueID:          shop.ClueID,
		City:            shop.City,
		StartTime:       shop.StartTime,
		EndTime:         shop.EndTime,
		UserID:          shop.UserID,
		Status:          shop.Status,
		LevelTier:       shop.LevelTier,
		HasPromotion:    shop.HasPromotion,
		HasBwc:          shop.HasBwc,
		MainIssues:      shop.MainIssues,
		TerminationTime: shop.TerminationTime,
		IsScaling:       shop.IsScaling,
		Reason:          shop.Reason,
		UserType:        shop.UserType,
		Mode:            shop.Mode,
		StoreID:         shop.StoreID,
	}
}

func (r *CacheOperateShopRepo) toDomain(model model.OperateShop) domain.OperateShop {
	return domain.OperateShop{
		ID:              model.ID,
		Name:            model.Name,
		CreateTime:      model.CreateTime,
		UpdateTime:      model.UpdateTime,
		CategoryID:      model.CategoryID,
		Tel:             model.Tel,
		Address:         model.Address,
		IsDine:          model.IsDine,
		IsNew:           model.IsNew,
		StoreArea:       model.StoreArea,
		BrandID:         model.BrandID,
		Platform:        model.Platform,
		ShopCode:        model.ShopCode,
		IsCharge:        model.IsCharge,
		Fee:             model.Fee,
		CommissionRate:  model.CommissionRate,
		Remark:          model.Remark,
		ClueID:          model.ClueID,
		City:            model.City,
		StartTime:       model.StartTime,
		EndTime:         model.EndTime,
		UserID:          model.UserID,
		Status:          model.Status,
		LevelTier:       model.LevelTier,
		HasPromotion:    model.HasPromotion,
		HasBwc:          model.HasBwc,
		MainIssues:      model.MainIssues,
		TerminationTime: model.TerminationTime,
		IsScaling:       model.IsScaling,
		Reason:          model.Reason,
		UserType:        model.UserType,
		Mode:            model.Mode,
		StoreID:         model.StoreID,
	}
}

func (r *CacheOperateShopRepo) Create(ctx context.Context, shop domain.OperateShop) (int64, error) {
	model := r.toModel(shop)
	return r.dao.Create(ctx, model)
}

func (r *CacheOperateShopRepo) Update(ctx context.Context, shop domain.OperateShop, fields []string) error {
	model := r.toModel(shop)
	return r.dao.Update(ctx, model, fields)
}

func (r *CacheOperateShopRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheOperateShopRepo) GetByID(ctx context.Context, id int64) (domain.OperateShop, error) {
	shop, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.OperateShop{}, err
	}
	return r.toDomain(shop), nil
}

func (r *CacheOperateShopRepo) GetByName(ctx context.Context, name string) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByName(ctx, name)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByCity(ctx context.Context, city string) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByCity(ctx, city)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByBrandID(ctx context.Context, brandID int64) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByBrandID(ctx, brandID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByStatus(ctx context.Context, status int) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByUserID(ctx context.Context, userID int64) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByPlatform(ctx context.Context, platform int) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByPlatform(ctx, platform)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) GetByClueID(ctx context.Context, clueID int64) ([]domain.OperateShop, error) {
	shops, err := r.dao.ListByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) List(ctx context.Context, offset, limit int) ([]domain.OperateShop, error) {
	shops, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateShop, len(shops))
	for i, shop := range shops {
		result[i] = r.toDomain(shop)
	}
	return result, nil
}

func (r *CacheOperateShopRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ShopActReportRepo 店铺活动报告仓储接口
type ShopActReportRepo interface {
	// Create 创建店铺活动报告
	Create(ctx context.Context, report domain.ShopActReport) (int64, error)
	// Update 更新店铺活动报告
	Update(ctx context.Context, report domain.ShopActReport, fields []string) error
	// Delete 删除店铺活动报告
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询店铺活动报告
	GetByID(ctx context.Context, id int64) (domain.ShopActReport, error)
	// GetByShopCode 根据店铺编码查询店铺活动报告
	GetByShopCode(ctx context.Context, shopCode string) (domain.ShopActReport, error)
	// List 分页查询店铺活动报告列表
	List(ctx context.Context, offset, limit int) ([]domain.ShopActReport, error)
	// ListByEmployeeID 根据员工ID查询店铺活动报告列表
	ListByEmployeeID(ctx context.Context, employeeID int) ([]domain.ShopActReport, error)
	// ListByBrandName 根据品牌名称查询店铺活动报告列表
	ListByBrandName(ctx context.Context, brandName string) ([]domain.ShopActReport, error)
	// ListByActID 根据活动ID查询店铺活动报告列表
	ListByActID(ctx context.Context, actID int64) ([]domain.ShopActReport, error)
	// Count 统计店铺活动报告总数
	Count(ctx context.Context) (int64, error)
}

type CacheShopActReportRepo struct {
	dao dao.ShopActReportDao
}

func NewCacheShopActReportRepo(dao dao.ShopActReportDao) *CacheShopActReportRepo {
	return &CacheShopActReportRepo{
		dao: dao,
	}
}

func (r *CacheShopActReportRepo) toModel(report domain.ShopActReport) model.ShopActReport {
	return model.ShopActReport{
		ID:                      report.ID,
		CreatedAt:               report.CreatedAt,
		UpdatedAt:               report.UpdatedAt,
		ShopID:                  report.ShopID,
		ShopCode:                report.ShopCode,
		ShopName:                report.ShopName,
		ActName:                 report.ActName,
		BrandName:               report.BrandName,
		UV:                      report.UV,
		PromoteTaokeNum:         report.PromoteTaokeNum,
		PayAmount:               report.PayAmount,
		PayNum:                  report.PayNum,
		OrderOriginalServiceFee: report.OrderOriginalServiceFee,
		OrderServicesFee:        report.OrderServicesFee,
		UserSettleAmount:        report.UserSettleAmount,
		ShopSettleAmount:        report.ShopSettleAmount,
		SettleNum:               report.SettleNum,
		OriginalServiceFee:      report.OriginalServiceFee,
		SettleFee:               report.SettleFee,
		CityServiceFee:          report.CityServiceFee,
		CitySettleFee:           report.CitySettleFee,
		OrderServiceFee:         report.OrderServiceFee,
		OrderSettleFee:          report.OrderSettleFee,
		City:                    report.City,
		District:                report.District,
		BusinessDistrict:        report.BusinessDistrict,
		CreateTime:              report.CreateTime,
		EmployeeID:              report.EmployeeID,
		IsNewShop:               report.IsNewShop,
		Bounty:                  report.Bounty,
		UserPayThreshold:        report.UserPayThreshold,
		ActID:                   report.ActID,
		Type:                    report.Type,
	}
}

func (r *CacheShopActReportRepo) toDomain(report model.ShopActReport) domain.ShopActReport {
	return domain.ShopActReport{
		ID:                      report.ID,
		CreatedAt:               report.CreatedAt,
		UpdatedAt:               report.UpdatedAt,
		ShopID:                  report.ShopID,
		ShopCode:                report.ShopCode,
		ShopName:                report.ShopName,
		ActName:                 report.ActName,
		BrandName:               report.BrandName,
		UV:                      report.UV,
		PromoteTaokeNum:         report.PromoteTaokeNum,
		PayAmount:               report.PayAmount,
		PayNum:                  report.PayNum,
		OrderOriginalServiceFee: report.OrderOriginalServiceFee,
		OrderServicesFee:        report.OrderServicesFee,
		UserSettleAmount:        report.UserSettleAmount,
		ShopSettleAmount:        report.ShopSettleAmount,
		SettleNum:               report.SettleNum,
		OriginalServiceFee:      report.OriginalServiceFee,
		SettleFee:               report.SettleFee,
		CityServiceFee:          report.CityServiceFee,
		CitySettleFee:           report.CitySettleFee,
		OrderServiceFee:         report.OrderServiceFee,
		OrderSettleFee:          report.OrderSettleFee,
		City:                    report.City,
		District:                report.District,
		BusinessDistrict:        report.BusinessDistrict,
		CreateTime:              report.CreateTime,
		EmployeeID:              report.EmployeeID,
		IsNewShop:               report.IsNewShop,
		Bounty:                  report.Bounty,
		UserPayThreshold:        report.UserPayThreshold,
		ActID:                   report.ActID,
		Type:                    report.Type,
	}
}

func (r *CacheShopActReportRepo) Create(ctx context.Context, report domain.ShopActReport) (int64, error) {
	return r.dao.Create(ctx, r.toModel(report))
}

func (r *CacheShopActReportRepo) Update(ctx context.Context, report domain.ShopActReport, fields []string) error {
	return r.dao.Update(ctx, r.toModel(report), fields)
}

func (r *CacheShopActReportRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheShopActReportRepo) GetByID(ctx context.Context, id int64) (domain.ShopActReport, error) {
	report, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ShopActReport{}, err
	}
	return r.toDomain(report), nil
}

func (r *CacheShopActReportRepo) GetByShopCode(ctx context.Context, shopCode string) (domain.ShopActReport, error) {
	report, err := r.dao.FindByShopCode(ctx, shopCode)
	if err != nil {
		return domain.ShopActReport{}, err
	}
	return r.toDomain(report), nil
}

func (r *CacheShopActReportRepo) List(ctx context.Context, offset, limit int) ([]domain.ShopActReport, error) {
	reports, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopActReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopActReportRepo) ListByEmployeeID(ctx context.Context, employeeID int) ([]domain.ShopActReport, error) {
	reports, err := r.dao.ListByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopActReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopActReportRepo) ListByBrandName(ctx context.Context, brandName string) ([]domain.ShopActReport, error) {
	reports, err := r.dao.ListByBrandName(ctx, brandName)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopActReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopActReportRepo) ListByActID(ctx context.Context, actID int64) ([]domain.ShopActReport, error) {
	reports, err := r.dao.ListByActID(ctx, actID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopActReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopActReportRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

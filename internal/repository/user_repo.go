package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type UserRepo interface {
	Create(ctx context.Context, user domain.User) (int64, error)
	Update(ctx context.Context, user domain.User, fields []string) error
	Delete(ctx context.Context, id int64) error
	GetByID(ctx context.Context, id int64) (domain.User, error)
	GetByAccount(ctx context.Context, account string) (domain.User, error)
	GetByMobile(ctx context.Context, mobile string) (domain.User, error)
	ListByCity(ctx context.Context, city string) ([]domain.User, error)
	ListByRole(ctx context.Context, roleID int64) ([]domain.User, error)
	ListByBDM(ctx context.Context, bdmID int64) ([]domain.User, error)
	List(ctx context.Context, offset, limit int) ([]domain.User, error)
	Count(ctx context.Context) (int64, error)
}

type CacheUserRepo struct {
	userDao dao.UserDao
}

func NewCacheUserRepo(userDao dao.UserDao) *CacheUserRepo {
	return &CacheUserRepo{
		userDao: userDao,
	}
}

func (r *CacheUserRepo) toModel(user domain.User) model.User {
	return model.User{
		ID:                  user.ID,
		SN:                  user.SN,
		Avatar:              user.Avatar,
		RealName:            user.RealName,
		Nickname:            user.Nickname,
		Account:             user.Account,
		Password:            user.Password,
		Mobile:              user.Mobile,
		Sex:                 user.Sex,
		Channel:             user.Channel,
		IsDisable:           user.IsDisable,
		LoginIP:             user.LoginIP,
		LoginTime:           user.LoginTime,
		IsNewUser:           user.IsNewUser,
		UserMoney:           user.UserMoney,
		TotalRechargeAmount: user.TotalRechargeAmount,
		EmployeeID:          user.EmployeeID,
		System:              user.System,
		BdmID:               user.BdmID,
		BusinessType:        user.BusinessType,
		CreateTime:          user.CreateTime,
		UpdateTime:          user.UpdateTime,
		DeleteTime:          user.DeleteTime,
		DingID:              user.DingID,
		City:                user.City,
		TelID:               user.TelID,
		IsCallUser:          user.IsCallUser,
		RoleID:              user.RoleID,
		CallType:            user.CallType,
		ParentID:            user.ParentID,
		Level:               user.Level,
		Version:             user.Version,
	}
}

func (r *CacheUserRepo) toDomain(user model.User) domain.User {
	return domain.User{
		ID:                  user.ID,
		SN:                  user.SN,
		Avatar:              user.Avatar,
		RealName:            user.RealName,
		Nickname:            user.Nickname,
		Account:             user.Account,
		Password:            user.Password,
		Mobile:              user.Mobile,
		Sex:                 user.Sex,
		Channel:             user.Channel,
		IsDisable:           user.IsDisable,
		LoginIP:             user.LoginIP,
		LoginTime:           user.LoginTime,
		IsNewUser:           user.IsNewUser,
		UserMoney:           user.UserMoney,
		TotalRechargeAmount: user.TotalRechargeAmount,
		EmployeeID:          user.EmployeeID,
		System:              user.System,
		BdmID:               user.BdmID,
		BusinessType:        user.BusinessType,
		CreateTime:          user.CreateTime,
		UpdateTime:          user.UpdateTime,
		DeleteTime:          user.DeleteTime,
		DingID:              user.DingID,
		City:                user.City,
		TelID:               user.TelID,
		IsCallUser:          user.IsCallUser,
		RoleID:              user.RoleID,
		CallType:            user.CallType,
		ParentID:            user.ParentID,
		Level:               user.Level,
		Version:             user.Version,
	}
}

func (r *CacheUserRepo) Create(ctx context.Context, user domain.User) (int64, error) {
	model := r.toModel(user)
	return r.userDao.Create(ctx, model)
}

func (r *CacheUserRepo) Update(ctx context.Context, user domain.User, fields []string) error {
	model := r.toModel(user)
	return r.userDao.Update(ctx, model, fields)
}

func (r *CacheUserRepo) Delete(ctx context.Context, id int64) error {
	return r.userDao.Delete(ctx, id)
}

func (r *CacheUserRepo) GetByID(ctx context.Context, id int64) (domain.User, error) {
	user, err := r.userDao.FindByID(ctx, id)
	if err != nil {
		return domain.User{}, err
	}
	return r.toDomain(user), nil
}

func (r *CacheUserRepo) GetByAccount(ctx context.Context, account string) (domain.User, error) {
	user, err := r.userDao.FindByAccount(ctx, account)
	if err != nil {
		return domain.User{}, err
	}
	return r.toDomain(user), nil
}

func (r *CacheUserRepo) GetByMobile(ctx context.Context, mobile string) (domain.User, error) {
	user, err := r.userDao.FindByMobile(ctx, mobile)
	if err != nil {
		return domain.User{}, err
	}
	return r.toDomain(user), nil
}

func (r *CacheUserRepo) ListByCity(ctx context.Context, city string) ([]domain.User, error) {
	users, err := r.userDao.ListByCity(ctx, city)
	if err != nil {
		return nil, err
	}
	result := make([]domain.User, 0, len(users))
	for _, user := range users {
		result = append(result, r.toDomain(user))
	}
	return result, nil
}

func (r *CacheUserRepo) ListByRole(ctx context.Context, roleID int64) ([]domain.User, error) {
	users, err := r.userDao.ListByRole(ctx, roleID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.User, 0, len(users))
	for _, user := range users {
		result = append(result, r.toDomain(user))
	}
	return result, nil
}

func (r *CacheUserRepo) ListByBDM(ctx context.Context, bdmID int64) ([]domain.User, error) {
	users, err := r.userDao.ListByBDM(ctx, bdmID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.User, 0, len(users))
	for _, user := range users {
		result = append(result, r.toDomain(user))
	}
	return result, nil
}

func (r *CacheUserRepo) List(ctx context.Context, offset, limit int) ([]domain.User, error) {
	users, err := r.userDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.User, 0, len(users))
	for _, user := range users {
		result = append(result, r.toDomain(user))
	}
	return result, nil
}

func (r *CacheUserRepo) Count(ctx context.Context) (int64, error) {
	return r.userDao.Count(ctx)
}

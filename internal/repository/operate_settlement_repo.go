package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// OperateSettlementRepo 运营结算仓储接口
type OperateSettlementRepo interface {
	// Create 创建运营结算
	Create(ctx context.Context, settlement domain.OperateSettlement) (int64, error)
	// Update 更新运营结算
	Update(ctx context.Context, settlement domain.OperateSettlement, fields []string) error
	// Delete 删除运营结算
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询运营结算
	GetByID(ctx context.Context, id int64) (domain.OperateSettlement, error)
	// GetByShopID 根据店铺ID查询运营结算列表
	GetByShopID(ctx context.Context, shopID string) ([]domain.OperateSettlement, error)
	// GetByOperatorID 根据运营人员ID查询运营结算列表
	GetByOperatorID(ctx context.Context, operatorID int64) ([]domain.OperateSettlement, error)
	// GetByStatus 根据结算状态查询运营结算列表
	GetByStatus(ctx context.Context, status int) ([]domain.OperateSettlement, error)
	// GetByType 根据结算类型查询运营结算列表
	GetByType(ctx context.Context, settlementType int) ([]domain.OperateSettlement, error)
	// GetByDateRange 根据时间范围查询运营结算列表
	GetByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.OperateSettlement, error)
	// List 查询运营结算列表
	List(ctx context.Context, offset, limit int) ([]domain.OperateSettlement, error)
	// Count 统计运营结算总数
	Count(ctx context.Context) (int64, error)
}

type CacheOperateSettlementRepo struct {
	dao dao.OperateSettlementDao
}

// NewCacheOperateSettlementRepo 创建运营结算仓储实现
func NewCacheOperateSettlementRepo(dao dao.OperateSettlementDao) *CacheOperateSettlementRepo {
	return &CacheOperateSettlementRepo{
		dao: dao,
	}
}

func (r *CacheOperateSettlementRepo) toModel(settlement domain.OperateSettlement) model.OperateSettlement {
	return model.OperateSettlement{
		ID:               settlement.ID,
		ShopID:           settlement.ShopID,
		ShopName:         settlement.ShopName,
		OperatorID:       settlement.OperatorID,
		OperatorName:     settlement.OperatorName,
		SettlementAmount: settlement.SettlementAmount,
		SettlementType:   settlement.SettlementType,
		SettlementStatus: settlement.SettlementStatus,
		SettlementTime:   settlement.SettlementTime,
		StartTime:        settlement.StartTime,
		EndTime:          settlement.EndTime,
		Remark:           settlement.Remark,
		CreatedAt:        settlement.CreatedAt,
		UpdatedAt:        settlement.UpdatedAt,
		DeletedAt:        settlement.DeletedAt,
	}
}

func (r *CacheOperateSettlementRepo) toDomain(settlement model.OperateSettlement) domain.OperateSettlement {
	return domain.OperateSettlement{
		ID:               settlement.ID,
		ShopID:           settlement.ShopID,
		ShopName:         settlement.ShopName,
		OperatorID:       settlement.OperatorID,
		OperatorName:     settlement.OperatorName,
		SettlementAmount: settlement.SettlementAmount,
		SettlementType:   settlement.SettlementType,
		SettlementStatus: settlement.SettlementStatus,
		SettlementTime:   settlement.SettlementTime,
		StartTime:        settlement.StartTime,
		EndTime:          settlement.EndTime,
		Remark:           settlement.Remark,
		CreatedAt:        settlement.CreatedAt,
		UpdatedAt:        settlement.UpdatedAt,
		DeletedAt:        settlement.DeletedAt,
	}
}

func (r *CacheOperateSettlementRepo) Create(ctx context.Context, settlement domain.OperateSettlement) (int64, error) {
	model := r.toModel(settlement)
	return r.dao.Create(ctx, model)
}

func (r *CacheOperateSettlementRepo) Update(ctx context.Context, settlement domain.OperateSettlement, fields []string) error {
	model := r.toModel(settlement)
	return r.dao.Update(ctx, model, fields)
}

func (r *CacheOperateSettlementRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheOperateSettlementRepo) GetByID(ctx context.Context, id int64) (domain.OperateSettlement, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.OperateSettlement{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheOperateSettlementRepo) GetByShopID(ctx context.Context, shopID string) ([]domain.OperateSettlement, error) {
	models, err := r.dao.FindByShopID(ctx, shopID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) GetByOperatorID(ctx context.Context, operatorID int64) ([]domain.OperateSettlement, error) {
	models, err := r.dao.FindByOperatorID(ctx, operatorID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) GetByStatus(ctx context.Context, status int) ([]domain.OperateSettlement, error) {
	models, err := r.dao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) GetByType(ctx context.Context, settlementType int) ([]domain.OperateSettlement, error) {
	models, err := r.dao.FindByType(ctx, settlementType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) GetByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.OperateSettlement, error) {
	models, err := r.dao.FindByDateRange(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) List(ctx context.Context, offset, limit int) ([]domain.OperateSettlement, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperateSettlement, len(models))
	for i, model := range models {
		result[i] = r.toDomain(model)
	}
	return result, nil
}

func (r *CacheOperateSettlementRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

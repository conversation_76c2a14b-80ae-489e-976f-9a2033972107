package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ElmShopCodeEmployeeRelRepo 饿了么店铺编码员工关系仓储接口
type ElmShopCodeEmployeeRelRepo interface {
	// Create 创建店铺编码员工关系
	Create(ctx context.Context, rel domain.ElmShopCodeEmployeeRel) error
	// Update 更新店铺编码员工关系
	Update(ctx context.Context, rel domain.ElmShopCodeEmployeeRel) error
	// Delete 删除店铺编码员工关系
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询店铺编码员工关系
	GetByID(ctx context.Context, id int64) (domain.ElmShopCodeEmployeeRel, error)
	// ListByEmployeeID 根据员工ID查询店铺编码员工关系列表
	ListByEmployeeID(ctx context.Context, employeeID int64) ([]domain.ElmShopCodeEmployeeRel, error)
	// ListByShopCode 根据店铺编码查询店铺编码员工关系列表
	ListByShopCode(ctx context.Context, shopCode string) ([]domain.ElmShopCodeEmployeeRel, error)
	// List 分页查询店铺编码员工关系列表
	List(ctx context.Context, offset, limit int) ([]domain.ElmShopCodeEmployeeRel, error)
	// Count 统计店铺编码员工关系总数
	Count(ctx context.Context) (int64, error)
}

type CacheElmShopCodeEmployeeRelRepo struct {
	dao dao.ElmShopCodeEmployeeRelDao
}

// NewCacheElmShopCodeEmployeeRelRepo 创建店铺编码员工关系仓储实现
func NewCacheElmShopCodeEmployeeRelRepo(dao dao.ElmShopCodeEmployeeRelDao) *CacheElmShopCodeEmployeeRelRepo {
	return &CacheElmShopCodeEmployeeRelRepo{dao: dao}
}

func (r *CacheElmShopCodeEmployeeRelRepo) toModel(rel domain.ElmShopCodeEmployeeRel) model.ElmShopCodeEmployeeRel {
	return model.ElmShopCodeEmployeeRel{
		ID:         int64(rel.ID),
		EmployeeID: int64(rel.EmployeeID),
		StartTime:  rel.StartTime,
		EndTime:    rel.EndTime,
		TagID:      int64(rel.TagID),
		Remark:     rel.Remark,
		CreatedAt:  rel.CreatedAt,
		UpdatedAt:  rel.UpdatedAt,
		DeletedAt:  rel.DeletedAt,
		City:       rel.City,
		ShopCode:   rel.ShopCode,
		ShopName:   rel.ShopName,
		Type:       rel.Type,
		BrandName:  rel.BrandName,
		ExpireTime: int64(rel.ExpireTime),
	}
}

func (r *CacheElmShopCodeEmployeeRelRepo) toDomain(rel model.ElmShopCodeEmployeeRel) domain.ElmShopCodeEmployeeRel {
	return domain.ElmShopCodeEmployeeRel{
		ID:         int(rel.ID),
		EmployeeID: int(rel.EmployeeID),
		StartTime:  rel.StartTime,
		EndTime:    rel.EndTime,
		TagID:      int(rel.TagID),
		Remark:     rel.Remark,
		CreatedAt:  rel.CreatedAt,
		UpdatedAt:  rel.UpdatedAt,
		DeletedAt:  rel.DeletedAt,
		City:       rel.City,
		ShopCode:   rel.ShopCode,
		ShopName:   rel.ShopName,
		Type:       rel.Type,
		BrandName:  rel.BrandName,
		ExpireTime: int(rel.ExpireTime),
	}
}

func (r *CacheElmShopCodeEmployeeRelRepo) Create(ctx context.Context, rel domain.ElmShopCodeEmployeeRel) error {
	_, err := r.dao.Create(ctx, r.toModel(rel))
	return err
}

func (r *CacheElmShopCodeEmployeeRelRepo) Update(ctx context.Context, rel domain.ElmShopCodeEmployeeRel) error {
	return r.dao.Update(ctx, r.toModel(rel), []string{"employee_id", "start_time", "end_time", "tag_id", "remark",
		"city", "shop_code", "shop_name", "type", "brand_name", "expire_time", "updated_at"})
}

func (r *CacheElmShopCodeEmployeeRelRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheElmShopCodeEmployeeRelRepo) GetByID(ctx context.Context, id int64) (domain.ElmShopCodeEmployeeRel, error) {
	rel, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ElmShopCodeEmployeeRel{}, err
	}
	return r.toDomain(rel), nil
}

func (r *CacheElmShopCodeEmployeeRelRepo) ListByEmployeeID(ctx context.Context, employeeID int64) ([]domain.ElmShopCodeEmployeeRel, error) {
	rels, err := r.dao.FindByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopCodeEmployeeRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

func (r *CacheElmShopCodeEmployeeRelRepo) ListByShopCode(ctx context.Context, shopCode string) ([]domain.ElmShopCodeEmployeeRel, error) {
	rels, err := r.dao.FindByShopCode(ctx, shopCode)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopCodeEmployeeRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

func (r *CacheElmShopCodeEmployeeRelRepo) List(ctx context.Context, offset, limit int) ([]domain.ElmShopCodeEmployeeRel, error) {
	rels, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopCodeEmployeeRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

func (r *CacheElmShopCodeEmployeeRelRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"
	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ElmAccountRepo 饿了么账号仓储接口
type ElmAccountRepo interface {
	// Create 创建账号
	Create(ctx context.Context, account domain.ElmAccount) (int64, error)
	// Update 更新账号
	Update(ctx context.Context, account domain.ElmAccount, fields []string) error
	// Get 获取账号
	Get(ctx context.Context, id int64) (domain.ElmAccount, error)
	// GetByUsername 根据用户名获取账号
	GetByUsername(ctx context.Context, username string) (domain.ElmAccount, error)
	// List 获取账号列表
	List(ctx context.Context, offset, limit int) ([]domain.ElmAccount, error)
	// Delete 删除账号
	Delete(ctx context.Context, id int64) error
}

// CacheElmAccountRepo 饿了么账号仓储实现
type CacheElmAccountRepo struct {
	elmAccountDao dao.ElmAccountDao
}

// NewCacheElmAccountRepo 创建ElmAccountRepo实例
func NewCacheElmAccountRepo(dao dao.ElmAccountDao) *CacheElmAccountRepo {
	return &CacheElmAccountRepo{
		elmAccountDao: dao,
	}
}

func (r *CacheElmAccountRepo) toModel(account domain.ElmAccount) model.ElmAccount {
	return model.ElmAccount{
		ID:        account.ID,
		Username:  account.Username,
		Password:  account.Password,
		City:      account.City,
		Cookie:    account.Cookie,
		AppID:     account.AppID,
		OpenCrawl: account.OpenCrawl,
		CreatedAt: account.CreatedAt,
		UpdatedAt: account.UpdatedAt,
	}
}

func (r *CacheElmAccountRepo) toDomain(account model.ElmAccount) domain.ElmAccount {
	return domain.ElmAccount{
		ID:        account.ID,
		Username:  account.Username,
		Password:  account.Password,
		City:      account.City,
		Cookie:    account.Cookie,
		AppID:     account.AppID,
		OpenCrawl: account.OpenCrawl,
		CreatedAt: account.CreatedAt,
		UpdatedAt: account.UpdatedAt,
	}
}

func (r *CacheElmAccountRepo) Create(ctx context.Context, account domain.ElmAccount) (int64, error) {
	return r.elmAccountDao.Create(ctx, r.toModel(account))
}

func (r *CacheElmAccountRepo) Update(ctx context.Context, account domain.ElmAccount, fields []string) error {
	return r.elmAccountDao.Update(ctx, r.toModel(account), fields)
}

func (r *CacheElmAccountRepo) Get(ctx context.Context, id int64) (domain.ElmAccount, error) {
	account, err := r.elmAccountDao.Get(ctx, id)
	if err != nil {
		return domain.ElmAccount{}, err
	}
	return r.toDomain(account), nil
}

func (r *CacheElmAccountRepo) GetByUsername(ctx context.Context, username string) (domain.ElmAccount, error) {
	account, err := r.elmAccountDao.GetByUsername(ctx, username)
	if err != nil {
		return domain.ElmAccount{}, err
	}
	return r.toDomain(account), nil
}

func (r *CacheElmAccountRepo) List(ctx context.Context, offset, limit int) ([]domain.ElmAccount, error) {
	accounts, err := r.elmAccountDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmAccount, len(accounts))
	for i, account := range accounts {
		result[i] = r.toDomain(account)
	}
	return result, nil
}

func (r *CacheElmAccountRepo) Delete(ctx context.Context, id int64) error {
	return r.elmAccountDao.Delete(ctx, id)
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type BusinessDistrictGradeRepo interface {
	Create(ctx context.Context, grade domain.BusinessDistrictGrade) (int64, error)
	Update(ctx context.Context, grade domain.BusinessDistrictGrade, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.BusinessDistrictGrade, error)
	List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictGrade, error)
	Count(ctx context.Context) (int64, error)
}

type CacheBusinessDistrictGradeRepo struct {
	gradeDao dao.BusinessDistrictGradeDao
}

func NewCacheBusinessDistrictGradeRepo(gradeDao dao.BusinessDistrictGradeDao) *CacheBusinessDistrictGradeRepo {
	return &CacheBusinessDistrictGradeRepo{
		gradeDao: gradeDao,
	}
}

func (r *CacheBusinessDistrictGradeRepo) Create(ctx context.Context, grade domain.BusinessDistrictGrade) (int64, error) {
	return r.gradeDao.Create(ctx, r.toModel(grade))
}

func (r *CacheBusinessDistrictGradeRepo) Update(ctx context.Context, grade domain.BusinessDistrictGrade, fields []string) error {
	return r.gradeDao.Update(ctx, r.toModel(grade), fields)
}

func (r *CacheBusinessDistrictGradeRepo) Delete(ctx context.Context, id int64) error {
	return r.gradeDao.Delete(ctx, id)
}

func (r *CacheBusinessDistrictGradeRepo) FindByID(ctx context.Context, id int64) (domain.BusinessDistrictGrade, error) {
	grade, err := r.gradeDao.FindByID(ctx, id)
	if err != nil {
		return domain.BusinessDistrictGrade{}, err
	}
	return r.toDomain(grade), nil
}

func (r *CacheBusinessDistrictGradeRepo) List(ctx context.Context, offset, limit int) ([]domain.BusinessDistrictGrade, error) {
	grades, err := r.gradeDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.BusinessDistrictGrade
	for _, grade := range grades {
		result = append(result, r.toDomain(grade))
	}
	return result, nil
}

func (r *CacheBusinessDistrictGradeRepo) Count(ctx context.Context) (int64, error) {
	return r.gradeDao.Count(ctx)
}

func (r *CacheBusinessDistrictGradeRepo) toModel(grade domain.BusinessDistrictGrade) model.BusinessDistrictGrade {
	return model.BusinessDistrictGrade{
		ID:          grade.ID,
		Name:        grade.Name,
		Description: grade.Description,
		Level:       grade.Level,
		Status:      grade.Status,
		CreatedAt:   grade.CreatedAt,
		UpdatedAt:   grade.UpdatedAt,
		DeletedAt:   grade.DeletedAt,
	}
}

func (r *CacheBusinessDistrictGradeRepo) toDomain(grade model.BusinessDistrictGrade) domain.BusinessDistrictGrade {
	return domain.BusinessDistrictGrade{
		ID:          grade.ID,
		Name:        grade.Name,
		Description: grade.Description,
		Level:       grade.Level,
		Status:      grade.Status,
		CreatedAt:   grade.CreatedAt,
		UpdatedAt:   grade.UpdatedAt,
		DeletedAt:   grade.DeletedAt,
	}
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueNotesRepo 线索笔记仓储接口
type ClueNotesRepo interface {
	// Create 创建线索笔记
	Create(ctx context.Context, note domain.ClueNotes) (int64, error)
	// Update 更新线索笔记
	Update(ctx context.Context, note domain.ClueNotes, fields []string) error
	// Delete 删除线索笔记
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索笔记
	FindByID(ctx context.Context, id int64) (domain.ClueNotes, error)
	// FindByClueID 根据线索ID查询线索笔记列表
	FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueNotes, error)
	// List 分页查询线索笔记列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueNotes, error)
	// Count 统计线索笔记总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueNotesRepo struct {
	clueNotesDao dao.ClueNotesDao
}

// NewCacheClueNotesRepo 创建线索笔记仓储实现
func NewCacheClueNotesRepo(clueNotesDao dao.ClueNotesDao) *CacheClueNotesRepo {
	return &CacheClueNotesRepo{
		clueNotesDao: clueNotesDao,
	}
}

func (r *CacheClueNotesRepo) Create(ctx context.Context, note domain.ClueNotes) (int64, error) {
	return r.clueNotesDao.Create(ctx, r.toModel(note))
}

func (r *CacheClueNotesRepo) Update(ctx context.Context, note domain.ClueNotes, fields []string) error {
	return r.clueNotesDao.Update(ctx, r.toModel(note), fields)
}

func (r *CacheClueNotesRepo) Delete(ctx context.Context, id int64) error {
	return r.clueNotesDao.Delete(ctx, id)
}

func (r *CacheClueNotesRepo) FindByID(ctx context.Context, id int64) (domain.ClueNotes, error) {
	model, err := r.clueNotesDao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueNotes{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueNotesRepo) FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueNotes, error) {
	models, err := r.clueNotesDao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueNotes
	for _, m := range models {
		result = append(result, r.toDomain(m))
	}
	return result, nil
}

func (r *CacheClueNotesRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueNotes, error) {
	models, err := r.clueNotesDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueNotes
	for _, m := range models {
		result = append(result, r.toDomain(m))
	}
	return result, nil
}

func (r *CacheClueNotesRepo) Count(ctx context.Context) (int64, error) {
	return r.clueNotesDao.Count(ctx)
}

func (r *CacheClueNotesRepo) toModel(note domain.ClueNotes) model.ClueNotes {
	return model.ClueNotes{
		ID:          note.ID,
		ClueID:      note.ClueID,
		Content:     note.Content,
		Type:        note.Type,
		CreatorID:   note.CreatorID,
		CreatorName: note.CreatorName,
		Status:      note.Status,
		CreatedAt:   note.CreatedAt,
		UpdatedAt:   note.UpdatedAt,
		DeletedAt:   note.DeletedAt,
	}
}

func (r *CacheClueNotesRepo) toDomain(note model.ClueNotes) domain.ClueNotes {
	return domain.ClueNotes{
		ID:          note.ID,
		ClueID:      note.ClueID,
		Content:     note.Content,
		Type:        note.Type,
		CreatorID:   note.CreatorID,
		CreatorName: note.CreatorName,
		Status:      note.Status,
		CreatedAt:   note.CreatedAt,
		UpdatedAt:   note.UpdatedAt,
		DeletedAt:   note.DeletedAt,
	}
}

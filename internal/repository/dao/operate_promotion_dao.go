package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// OperatePromotionDao 运营推广数据访问接口
type OperatePromotionDao interface {
	// Create 创建运营推广
	Create(ctx context.Context, promotion model.OperatePromotion) (int64, error)
	// Update 更新运营推广
	Update(ctx context.Context, promotion model.OperatePromotion, fields []string) error
	// Delete 删除运营推广
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询运营推广
	FindByID(ctx context.Context, id int64) (model.OperatePromotion, error)
	// List 分页查询运营推广列表
	List(ctx context.Context, page, size int) ([]model.OperatePromotion, error)
	// ListByType 根据类型查询运营推广列表
	ListByType(ctx context.Context, promotionType int8) ([]model.OperatePromotion, error)
	// Count 统计运营推广总数
	Count(ctx context.Context) (int64, error)
}

// GORMOperatePromotionDao 运营推广数据访问对象
type GORMOperatePromotionDao struct {
	db *gorm.DB
}

// NewGORMOperatePromotionDao 创建运营推广数据访问实现
func NewGORMOperatePromotionDao(db *gorm.DB) GORMOperatePromotionDao {
	return GORMOperatePromotionDao{
		db: db,
	}
}

func (d GORMOperatePromotionDao) Create(ctx context.Context, promotion model.OperatePromotion) (int64, error) {
	err := d.db.WithContext(ctx).Create(&promotion).Error
	if err != nil {
		return 0, err
	}
	return promotion.ID, nil
}

func (d GORMOperatePromotionDao) Update(ctx context.Context, promotion model.OperatePromotion, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.OperatePromotion{}).Where("id = ?", promotion.ID).Select(fields).Updates(&promotion).Error
}

func (d GORMOperatePromotionDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.OperatePromotion{}, id).Error
}

func (d GORMOperatePromotionDao) FindByID(ctx context.Context, id int64) (model.OperatePromotion, error) {
	var promotion model.OperatePromotion
	err := d.db.WithContext(ctx).First(&promotion, id).Error
	if err != nil {
		return model.OperatePromotion{}, err
	}
	return promotion, nil
}

func (d GORMOperatePromotionDao) List(ctx context.Context, page, size int) ([]model.OperatePromotion, error) {
	var promotions []model.OperatePromotion
	err := d.db.WithContext(ctx).Offset((page - 1) * size).Limit(size).Find(&promotions).Error
	if err != nil {
		return nil, err
	}
	return promotions, nil
}

func (d GORMOperatePromotionDao) ListByType(ctx context.Context, promotionType int8) ([]model.OperatePromotion, error) {
	var promotions []model.OperatePromotion
	err := d.db.WithContext(ctx).Where("type = ?", promotionType).Find(&promotions).Error
	if err != nil {
		return nil, err
	}
	return promotions, nil
}

func (d GORMOperatePromotionDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.OperatePromotion{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

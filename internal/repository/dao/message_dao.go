package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// MessageDao 消息数据访问接口
type MessageDao interface {
	// Create 创建消息
	Create(ctx context.Context, message model.Message) (int64, error)
	// Update 更新消息
	Update(ctx context.Context, message model.Message, fields []string) error
	// Delete 删除消息
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询消息
	FindByID(ctx context.Context, id int64) (model.Message, error)
	// FindByReceiverID 根据接收人ID查询消息列表
	FindByReceiverID(ctx context.Context, receiverID int64) ([]model.Message, error)
	// FindBySenderID 根据发送人ID查询消息列表
	FindBySenderID(ctx context.Context, senderID int64) ([]model.Message, error)
	// FindByStatus 根据状态查询消息列表
	FindByStatus(ctx context.Context, status int64) ([]model.Message, error)
	// FindByType 根据消息类型查询消息列表
	FindByType(ctx context.Context, messageType int64) ([]model.Message, error)
	// FindByPriority 根据优先级查询消息列表
	FindByPriority(ctx context.Context, priority int64) ([]model.Message, error)
	// FindByDateRange 根据时间范围查询消息列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.Message, error)
	// List 查询消息列表
	List(ctx context.Context, offset, limit int) ([]model.Message, error)
	// Count 统计消息总数
	Count(ctx context.Context) (int64, error)
}

// GORMMessageDao 消息数据访问对象
type GORMMessageDao struct {
	db *gorm.DB
}

// NewGORMMessageDao 创建消息数据访问实现
func NewGORMMessageDao(db *gorm.DB) GORMMessageDao {
	return GORMMessageDao{
		db: db,
	}
}

func (d GORMMessageDao) Create(ctx context.Context, message model.Message) (int64, error) {
	err := d.db.WithContext(ctx).Create(&message).Error
	if err != nil {
		return 0, err
	}
	return message.ID, nil
}

func (d GORMMessageDao) Update(ctx context.Context, message model.Message, fields []string) error {
	return d.db.WithContext(ctx).Model(model.Message{}).Where("id = ?", message.ID).Select(fields).Updates(&message).Error
}

func (d GORMMessageDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.Message{}, id).Error
}

func (d GORMMessageDao) FindByID(ctx context.Context, id int64) (model.Message, error) {
	var message model.Message
	err := d.db.WithContext(ctx).First(&message, id).Error
	if err != nil {
		return model.Message{}, err
	}
	return message, nil
}

func (d GORMMessageDao) FindByReceiverID(ctx context.Context, receiverID int64) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("receiver_id = ?", receiverID).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) FindBySenderID(ctx context.Context, senderID int64) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("sender_id = ?", senderID).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) FindByStatus(ctx context.Context, status int64) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) FindByType(ctx context.Context, messageType int64) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("message_type = ?", messageType).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) FindByPriority(ctx context.Context, priority int64) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("priority = ?", priority).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startTime, endTime).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) List(ctx context.Context, offset, limit int) ([]model.Message, error) {
	var messages []model.Message
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&messages).Error
	if err != nil {
		return nil, err
	}
	return messages, nil
}

func (d GORMMessageDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(model.Message{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// CategoryDao 定义分类数据访问接口
type CategoryDao interface {
	Create(ctx context.Context, category model.Category) (int64, error)
	Update(ctx context.Context, category model.Category, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.Category, error)
	FindByName(ctx context.Context, name string) (model.Category, error)
	List(ctx context.Context, offset, limit int) ([]model.Category, error)
	ListByParentID(ctx context.Context, parentID int64) ([]model.Category, error)
	Count(ctx context.Context) (int64, error)
}

// GORMCategoryDao 实现CategoryDao接口
type GORMCategoryDao struct {
	db *gorm.DB
}

// NewGORMCategoryDao 创建一个新的GORMCategoryDao实例
func NewGORMCategoryDao(db *gorm.DB) *GORMCategoryDao {
	return &GORMCategoryDao{
		db: db,
	}
}

func (d *GORMCategoryDao) Create(ctx context.Context, category model.Category) (int64, error) {
	err := d.db.WithContext(ctx).Create(&category).Error
	if err != nil {
		return 0, err
	}
	return category.ID, nil
}

func (d *GORMCategoryDao) Update(ctx context.Context, category model.Category, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.Category{}).Where("id = ?", category.ID).Select(fields).Updates(&category).Error
}

func (d *GORMCategoryDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.Category{}, id).Error
}

func (d *GORMCategoryDao) FindByID(ctx context.Context, id int64) (model.Category, error) {
	var category model.Category
	err := d.db.WithContext(ctx).First(&category, id).Error
	if err != nil {
		return model.Category{}, err
	}
	return category, nil
}

func (d *GORMCategoryDao) FindByName(ctx context.Context, name string) (model.Category, error) {
	var category model.Category
	err := d.db.WithContext(ctx).Where("name = ?", name).First(&category).Error
	if err != nil {
		return model.Category{}, err
	}
	return category, nil
}

func (d *GORMCategoryDao) List(ctx context.Context, offset, limit int) ([]model.Category, error) {
	var categories []model.Category
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&categories).Error
	if err != nil {
		return nil, err
	}
	return categories, nil
}

func (d *GORMCategoryDao) ListByParentID(ctx context.Context, parentID int64) ([]model.Category, error) {
	var categories []model.Category
	err := d.db.WithContext(ctx).Where("parent_id = ?", parentID).Find(&categories).Error
	if err != nil {
		return nil, err
	}
	return categories, nil
}

func (d *GORMCategoryDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(model.Category{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

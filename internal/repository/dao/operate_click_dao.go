package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// OperateClickDao 运营点击数据访问接口
type OperateClickDao interface {
	// Create 创建运营点击
	Create(ctx context.Context, click model.OperateClick) (int64, error)
	// Update 更新运营点击
	Update(ctx context.Context, click model.OperateClick, fields []string) error
	// Delete 删除运营点击
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询运营点击
	FindByID(ctx context.Context, id int64) (model.OperateClick, error)
	// FindByShopID 根据店铺ID查询运营点击列表
	FindByShopID(ctx context.Context, shopID string) ([]model.OperateClick, error)
	// FindByOperatorID 根据运营人员ID查询运营点击列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateClick, error)
	// FindByClickType 根据点击类型查询运营点击列表
	FindByClickType(ctx context.Context, clickType int) ([]model.OperateClick, error)
	// FindByDateRange 根据时间范围查询运营点击列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateClick, error)
	// List 查询运营点击列表
	List(ctx context.Context, offset, limit int) ([]model.OperateClick, error)
	// Count 统计运营点击总数
	Count(ctx context.Context) (int64, error)
}

type GORMOperateClickDao struct {
	db *gorm.DB
}

// NewGORMOperateClickDao 创建运营点击数据访问实现
func NewGORMOperateClickDao(db *gorm.DB) *GORMOperateClickDao {
	return &GORMOperateClickDao{
		db: db,
	}
}

func (d *GORMOperateClickDao) Create(ctx context.Context, click model.OperateClick) (int64, error) {
	err := d.db.WithContext(ctx).Create(click).Error
	if err != nil {
		return 0, err
	}
	return click.ID, nil
}

func (d *GORMOperateClickDao) Update(ctx context.Context, click model.OperateClick, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.OperateClick{}).Where("id = ?", click.ID).Select(fields).Updates(&click).Error
}

func (d *GORMOperateClickDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.OperateClick{}, id).Error
}

func (d *GORMOperateClickDao) FindByID(ctx context.Context, id int64) (model.OperateClick, error) {
	var click model.OperateClick
	err := d.db.WithContext(ctx).First(&click, id).Error
	if err != nil {
		return model.OperateClick{}, err
	}
	return click, nil
}

func (d *GORMOperateClickDao) FindByShopID(ctx context.Context, shopID string) ([]model.OperateClick, error) {
	var clicks []model.OperateClick
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).Find(&clicks).Error
	if err != nil {
		return nil, err
	}
	return clicks, nil
}

func (d *GORMOperateClickDao) FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateClick, error) {
	var clicks []model.OperateClick
	err := d.db.WithContext(ctx).Where("operator_id = ?", operatorID).Find(&clicks).Error
	if err != nil {
		return nil, err
	}
	return clicks, nil
}

func (d *GORMOperateClickDao) FindByClickType(ctx context.Context, clickType int) ([]model.OperateClick, error) {
	var clicks []model.OperateClick
	err := d.db.WithContext(ctx).Where("click_type = ?", clickType).Find(&clicks).Error
	if err != nil {
		return nil, err
	}
	return clicks, nil
}

func (d *GORMOperateClickDao) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateClick, error) {
	var clicks []model.OperateClick
	err := d.db.WithContext(ctx).Where("click_time BETWEEN ? AND ?", startTime, endTime).Find(&clicks).Error
	if err != nil {
		return nil, err
	}
	return clicks, nil
}

func (d *GORMOperateClickDao) List(ctx context.Context, offset, limit int) ([]model.OperateClick, error) {
	var clicks []model.OperateClick
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&clicks).Error
	if err != nil {
		return nil, err
	}
	return clicks, nil
}

func (d *GORMOperateClickDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.OperateClick{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type BrandDao interface {
	Create(ctx context.Context, brand model.Brand) (int64, error)
	Update(ctx context.Context, brand model.Brand, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.Brand, error)
	FindByName(ctx context.Context, name string) (model.Brand, error)
	List(ctx context.Context, offset, limit int) ([]model.Brand, error)
	Count(ctx context.Context) (int64, error)
	FindByFuzzyName(ctx context.Context, name string) ([]model.Brand, error)
}

type GORMBrandDao struct {
	db *gorm.DB
}

func NewGORMBrandDao(db *gorm.DB) *GORMBrandDao {
	return &GORMBrandDao{
		db: db,
	}
}

func (d *GORMBrandDao) Create(ctx context.Context, brand model.Brand) (int64, error) {
	err := d.db.WithContext(ctx).Create(&brand).Error
	if err != nil {
		return 0, err
	}
	return brand.ID, nil
}

func (d *GORMBrandDao) Update(ctx context.Context, brand model.Brand, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.Brand{}).Where("id = ?", brand.ID).Select(fields).Updates(&brand).Error
}

func (d *GORMBrandDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Brand{}, id).Error
}

func (d *GORMBrandDao) FindByID(ctx context.Context, id int64) (model.Brand, error) {
	var brand model.Brand
	err := d.db.WithContext(ctx).First(&brand, id).Error
	if err != nil {
		return model.Brand{}, err
	}
	return brand, nil
}

func (d *GORMBrandDao) FindByName(ctx context.Context, name string) (model.Brand, error) {
	var brand model.Brand
	err := d.db.WithContext(ctx).Where("brand_name = ?", name).First(&brand).Error
	if err != nil {
		return model.Brand{}, err
	}
	return brand, nil
}

func (d *GORMBrandDao) List(ctx context.Context, offset, limit int) ([]model.Brand, error) {
	var brands []model.Brand
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&brands).Error
	if err != nil {
		return nil, err
	}
	return brands, nil
}

func (d *GORMBrandDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Brand{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (d *GORMBrandDao) FindByFuzzyName(ctx context.Context, name string) ([]model.Brand, error) {
	var brands []model.Brand
	err := d.db.WithContext(ctx).Where("brand_name LIKE ?", "%"+name+"%").Find(&brands).Error
	if err != nil {
		return nil, err
	}
	return brands, nil
}

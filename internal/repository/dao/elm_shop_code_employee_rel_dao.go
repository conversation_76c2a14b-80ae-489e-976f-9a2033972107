package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// ElmShopCodeEmployeeRelDao 饿了么店铺编码员工关系数据访问接口
type ElmShopCodeEmployeeRelDao interface {
	// Create 创建店铺编码员工关系
	Create(ctx context.Context, rel model.ElmShopCodeEmployeeRel) (int64, error)
	// Update 更新店铺编码员工关系
	Update(ctx context.Context, rel model.ElmShopCodeEmployeeRel, fields []string) error
	// Delete 删除店铺编码员工关系
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询店铺编码员工关系
	FindByID(ctx context.Context, id int64) (model.ElmShopCodeEmployeeRel, error)
	// FindByShopID 根据店铺ID查询店铺编码员工关系
	FindByShopID(ctx context.Context, shopID int64) (model.ElmShopCodeEmployeeRel, error)
	// FindByEmployeeID 根据员工ID查询店铺编码员工关系列表
	FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShopCodeEmployeeRel, error)
	// FindByShopCode 根据店铺编码查询店铺编码员工关系列表
	FindByShopCode(ctx context.Context, shopCode string) ([]model.ElmShopCodeEmployeeRel, error)
	// List 查询店铺编码员工关系列表
	List(ctx context.Context, offset, limit int) ([]model.ElmShopCodeEmployeeRel, error)
	// Count 统计店铺编码员工关系总数
	Count(ctx context.Context) (int64, error)
}

type GORMElmShopCodeEmployeeRelDao struct {
	db *gorm.DB
}

// NewGORMElmShopCodeEmployeeRelDao 创建店铺编码员工关系数据访问实例
func NewGORMElmShopCodeEmployeeRelDao(db *gorm.DB) *GORMElmShopCodeEmployeeRelDao {
	return &GORMElmShopCodeEmployeeRelDao{
		db: db,
	}
}

func (d *GORMElmShopCodeEmployeeRelDao) Create(ctx context.Context, rel model.ElmShopCodeEmployeeRel) (int64, error) {
	err := d.db.WithContext(ctx).Create(rel).Error
	if err != nil {
		return 0, err
	}
	return rel.ID, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) Update(ctx context.Context, rel model.ElmShopCodeEmployeeRel, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ElmShopCodeEmployeeRel{}).Where("id = ?", rel.ID).Select(fields).Updates(&rel).Error
}

func (d *GORMElmShopCodeEmployeeRelDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ElmShopCodeEmployeeRel{}, id).Error
}

func (d *GORMElmShopCodeEmployeeRelDao) FindByID(ctx context.Context, id int64) (model.ElmShopCodeEmployeeRel, error) {
	var rel model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).First(&rel, id).Error
	if err != nil {
		return model.ElmShopCodeEmployeeRel{}, err
	}
	return rel, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) FindByShopID(ctx context.Context, shopID int64) (model.ElmShopCodeEmployeeRel, error) {
	var rel model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).First(&rel).Error
	if err != nil {
		return model.ElmShopCodeEmployeeRel{}, err
	}
	return rel, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShopCodeEmployeeRel, error) {
	var rels []model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) FindByShopCode(ctx context.Context, shopCode string) ([]model.ElmShopCodeEmployeeRel, error) {
	var rels []model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Where("shop_code = ?", shopCode).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) List(ctx context.Context, offset, limit int) ([]model.ElmShopCodeEmployeeRel, error) {
	var rels []model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopCodeEmployeeRelDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ElmShopCodeEmployeeRel{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ClueTaskInfoDao 线索任务详情数据访问接口
type ClueTaskInfoDao interface {
	// Create 创建线索任务详情
	Create(ctx context.Context, info model.ClueTaskInfo) (int64, error)
	// Update 更新线索任务详情
	Update(ctx context.Context, info model.ClueTaskInfo, fields []string) error
	// Delete 删除线索任务详情
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索任务详情
	FindByID(ctx context.Context, id int64) (model.ClueTaskInfo, error)
	// FindByTaskID 根据任务ID查询线索任务详情列表
	FindByTaskID(ctx context.Context, taskID int64) ([]model.ClueTaskInfo, error)
	// FindByType 根据详情类型查询线索任务详情列表
	FindByType(ctx context.Context, infoType int) ([]model.ClueTaskInfo, error)
	// List 分页查询线索任务详情列表
	List(ctx context.Context, offset, limit int) ([]model.ClueTaskInfo, error)
	// Count 统计线索任务详情总数
	Count(ctx context.Context) (int64, error)
}

type GORMClueTaskInfoDao struct {
	db *gorm.DB
}

// NewGORMClueTaskInfoDao 创建线索任务详情数据访问实现
func NewGORMClueTaskInfoDao(db *gorm.DB) *GORMClueTaskInfoDao {
	return &GORMClueTaskInfoDao{
		db: db,
	}
}

func (d *GORMClueTaskInfoDao) Create(ctx context.Context, info model.ClueTaskInfo) (int64, error) {
	err := d.db.WithContext(ctx).Create(info).Error
	if err != nil {
		return 0, err
	}
	return info.ID, err
}

func (d *GORMClueTaskInfoDao) Update(ctx context.Context, info model.ClueTaskInfo, fields []string) error {
	return d.db.WithContext(ctx).Model(model.ClueTaskInfo{}).Where("id = ?", info.ID).Select(fields).Updates(&info).Error
}

func (d *GORMClueTaskInfoDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ClueTaskInfo{}, id).Error
}

func (d *GORMClueTaskInfoDao) FindByID(ctx context.Context, id int64) (model.ClueTaskInfo, error) {
	var info model.ClueTaskInfo
	err := d.db.WithContext(ctx).First(&info, id).Error
	if err != nil {
		return model.ClueTaskInfo{}, err
	}
	return info, nil
}

func (d *GORMClueTaskInfoDao) FindByTaskID(ctx context.Context, taskID int64) ([]model.ClueTaskInfo, error) {
	var infos []model.ClueTaskInfo
	err := d.db.WithContext(ctx).Where("task_id = ?", taskID).Find(&infos).Error
	if err != nil {
		return nil, err
	}
	return infos, nil
}

func (d *GORMClueTaskInfoDao) FindByType(ctx context.Context, infoType int) ([]model.ClueTaskInfo, error) {
	var infos []model.ClueTaskInfo
	err := d.db.WithContext(ctx).Where("type = ?", infoType).Find(&infos).Error
	if err != nil {
		return nil, err
	}
	return infos, nil
}

func (d *GORMClueTaskInfoDao) List(ctx context.Context, offset, limit int) ([]model.ClueTaskInfo, error) {
	var infos []model.ClueTaskInfo
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&infos).Error
	if err != nil {
		return nil, err
	}
	return infos, nil
}

func (d *GORMClueTaskInfoDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ClueTaskInfo{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// OperateShopDao 运营商店铺数据访问接口
type OperateShopDao interface {
	// Create 创建运营商店铺
	Create(ctx context.Context, shop model.OperateShop) (int64, error)
	// Update 更新运营商店铺
	Update(ctx context.Context, shop model.OperateShop, fields []string) error
	// Delete 删除运营商店铺
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询运营商店铺
	FindByID(ctx context.Context, id int64) (model.OperateShop, error)
	// FindByShopID 根据店铺ID查询运营商店铺
	FindByShopID(ctx context.Context, shopID string) (model.OperateShop, error)
	// FindByOperatorID 根据运营人员ID查询运营商店铺列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateShop, error)
	// FindByStatus 根据状态查询运营商店铺列表
	FindByStatus(ctx context.Context, status int) ([]model.OperateShop, error)
	// FindByShopType 根据店铺类型查询运营商店铺列表
	FindByShopType(ctx context.Context, shopType int) ([]model.OperateShop, error)
	// FindByDateRange 根据时间范围查询运营商店铺列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateShop, error)
	// List 查询运营商店铺列表
	List(ctx context.Context, offset, limit int) ([]model.OperateShop, error)
	// Count 统计运营商店铺总数
	Count(ctx context.Context) (int64, error)
	// ListByName 根据店铺名称查询运营商店铺列表
	ListByName(ctx context.Context, name string) ([]model.OperateShop, error)
	// ListByCity 根据店铺所在城市查询运营商店铺列表
	ListByCity(ctx context.Context, city string) ([]model.OperateShop, error)
	// ListByBrandID 根据品牌ID查询运营商店铺列表
	ListByBrandID(ctx context.Context, brandID int64) ([]model.OperateShop, error)
	// ListByStatus 根据店铺状态查询运营商店铺列表
	ListByStatus(ctx context.Context, status int) ([]model.OperateShop, error)
	// ListByUserID 根据用户ID查询运营商店铺列表
	ListByUserID(ctx context.Context, userID int64) ([]model.OperateShop, error)
	// ListByPlatform 根据平台查询运营商店铺列表
	ListByPlatform(ctx context.Context, platform int) ([]model.OperateShop, error)
	// ListByClueID 根据线索ID查询运营商店铺列表
	ListByClueID(ctx context.Context, clueID int64) ([]model.OperateShop, error)
}

// GORMOperateShopDao 运营商店铺数据访问对象
type GORMOperateShopDao struct {
	db *gorm.DB
}

// NewGORMOperateShopDao 创建运营商店铺数据访问实现
func NewGORMOperateShopDao(db *gorm.DB) GORMOperateShopDao {
	return GORMOperateShopDao{
		db: db,
	}
}

func (d GORMOperateShopDao) Create(ctx context.Context, shop model.OperateShop) (int64, error) {
	err := d.db.WithContext(ctx).Create(&shop).Error
	if err != nil {
		return 0, err
	}
	return shop.ID, nil
}

func (d GORMOperateShopDao) Update(ctx context.Context, shop model.OperateShop, fields []string) error {
	return d.db.WithContext(ctx).Model(model.OperateShop{}).Where("id = ?", shop.ID).Select(fields).Updates(&shop).Error
}

func (d GORMOperateShopDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.OperateShop{}, id).Error
}

func (d GORMOperateShopDao) FindByID(ctx context.Context, id int64) (model.OperateShop, error) {
	var shop model.OperateShop
	err := d.db.WithContext(ctx).First(&shop, id).Error
	if err != nil {
		return model.OperateShop{}, err
	}
	return shop, nil
}

func (d GORMOperateShopDao) FindByShopID(ctx context.Context, shopID string) (model.OperateShop, error) {
	var shop model.OperateShop
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).First(&shop).Error
	if err != nil {
		return model.OperateShop{}, err
	}
	return shop, nil
}

func (d GORMOperateShopDao) FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("operator_id = ?", operatorID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) FindByStatus(ctx context.Context, status int) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) FindByShopType(ctx context.Context, shopType int) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("shop_type = ?", shopType).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startTime, endTime).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) List(ctx context.Context, offset, limit int) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(model.OperateShop{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (d GORMOperateShopDao) ListByName(ctx context.Context, name string) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("name = ?", name).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByCity(ctx context.Context, city string) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("city = ?", city).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByBrandID(ctx context.Context, brandID int64) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("brand_id = ?", brandID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByStatus(ctx context.Context, status int) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByUserID(ctx context.Context, userID int64) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("user_id = ?", userID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByPlatform(ctx context.Context, platform int) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("platform = ?", platform).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMOperateShopDao) ListByClueID(ctx context.Context, clueID int64) ([]model.OperateShop, error) {
	var shops []model.OperateShop
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// UserSessionDao 用户会话数据访问接口
type UserSessionDao interface {
	// Create 创建用户会话
	Create(ctx context.Context, session model.UserSession) (int64, error)
	// Update 更新用户会话
	Update(ctx context.Context, session model.UserSession, fields []string) error
	// Delete 删除用户会话
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询用户会话
	FindByID(ctx context.Context, id int64) (model.UserSession, error)
	// FindByToken 根据Token查询用户会话
	FindByToken(ctx context.Context, token string) (model.UserSession, error)
	// FindByUserIDAndTerminal 根据用户ID和终端类型查询用户会话
	FindByUserIDAndTerminal(ctx context.Context, userID int64, terminal int8) (model.UserSession, error)
	// ListByUserID 根据用户ID查询用户会话列表
	ListByUserID(ctx context.Context, userID int64) ([]model.UserSession, error)
	// List 分页查询用户会话列表
	List(ctx context.Context, offset, limit int) ([]model.UserSession, error)
	// Count 统计用户会话总数
	Count(ctx context.Context) (int64, error)
}

// GORMUserSessionDao 用户会话数据访问对象
type GORMUserSessionDao struct {
	db *gorm.DB
}

// NewGORMUserSessionDao 创建用户会话数据访问实现
func NewGORMUserSessionDao(db *gorm.DB) *GORMUserSessionDao {
	return &GORMUserSessionDao{
		db: db,
	}
}

func (d *GORMUserSessionDao) Create(ctx context.Context, session model.UserSession) (int64, error) {
	err := d.db.WithContext(ctx).Create(&session).Error
	if err != nil {
		return 0, err
	}
	return session.ID, nil
}

func (d *GORMUserSessionDao) Update(ctx context.Context, session model.UserSession, fields []string) error {
	return d.db.WithContext(ctx).Model(model.UserSession{}).Where("id = ?", session.ID).Select(fields).Updates(&session).Error
}

func (d *GORMUserSessionDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.UserSession{}, id).Error
}

func (d *GORMUserSessionDao) FindByID(ctx context.Context, id int64) (model.UserSession, error) {
	var session model.UserSession
	err := d.db.WithContext(ctx).First(&session, id).Error
	if err != nil {
		return model.UserSession{}, err
	}
	return session, nil
}

func (d *GORMUserSessionDao) FindByToken(ctx context.Context, token string) (model.UserSession, error) {
	var session model.UserSession
	err := d.db.WithContext(ctx).Where("token = ?", token).First(&session).Error
	if err != nil {
		return model.UserSession{}, err
	}
	return session, nil
}

func (d *GORMUserSessionDao) FindByUserIDAndTerminal(ctx context.Context, userID int64, terminal int8) (model.UserSession, error) {
	var session model.UserSession
	err := d.db.WithContext(ctx).Where("user_id = ? AND terminal = ?", userID, terminal).First(&session).Error
	if err != nil {
		return model.UserSession{}, err
	}
	return session, nil
}

func (d *GORMUserSessionDao) ListByUserID(ctx context.Context, userID int64) ([]model.UserSession, error) {
	var sessions []model.UserSession
	err := d.db.WithContext(ctx).Where("user_id = ?", userID).Find(&sessions).Error
	if err != nil {
		return nil, err
	}
	return sessions, nil
}

func (d *GORMUserSessionDao) List(ctx context.Context, offset, limit int) ([]model.UserSession, error) {
	var sessions []model.UserSession
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&sessions).Error
	if err != nil {
		return nil, err
	}
	return sessions, nil
}

func (d *GORMUserSessionDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.UserSession{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"
	"time"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// CallStatisDao 通话统计数据访问接口
type CallStatisDao interface {
	// Create 创建通话统计
	Create(ctx context.Context, statis model.CallStatis) (int64, error)
	// Update 更新通话统计
	Update(ctx context.Context, statis model.CallStatis, fields []string) error
	// Delete 删除通话统计
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询通话统计
	FindByID(ctx context.Context, id int64) (model.CallStatis, error)
	// FindByEmployeeID 根据员工ID查询通话统计列表
	FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.CallStatis, error)
	// FindByDate 根据日期查询通话统计列表
	FindByDate(ctx context.Context, date time.Time) ([]model.CallStatis, error)
	// FindByDateRange 根据日期范围查询通话统计列表
	FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]model.CallStatis, error)
	// List 查询通话统计列表
	List(ctx context.Context, offset, limit int) ([]model.CallStatis, error)
	// Count 统计通话统计总数
	Count(ctx context.Context) (int64, error)
}

// GORMCallStatisDao 通话统计数据访问对象
type GORMCallStatisDao struct {
	db *gorm.DB
}

// NewGORMCallStatisDao 创建通话统计数据访问实例
func NewGORMCallStatisDao(db *gorm.DB) *GORMCallStatisDao {
	return &GORMCallStatisDao{
		db: db,
	}
}

func (d *GORMCallStatisDao) Create(ctx context.Context, statis model.CallStatis) (int64, error) {
	err := d.db.WithContext(ctx).Create(&statis).Error
	if err != nil {
		return 0, err
	}
	return statis.ID, nil
}

func (d *GORMCallStatisDao) Update(ctx context.Context, statis model.CallStatis, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.CallStatis{}).Where("id = ?", statis.ID).Select(fields).Updates(&statis).Error
}

func (d *GORMCallStatisDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.CallStatis{}, id).Error
}

func (d *GORMCallStatisDao) FindByID(ctx context.Context, id int64) (model.CallStatis, error) {
	var statis model.CallStatis
	err := d.db.WithContext(ctx).First(&statis, id).Error
	if err != nil {
		return model.CallStatis{}, err
	}
	return statis, nil
}

func (d *GORMCallStatisDao) FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.CallStatis, error) {
	var statisList []model.CallStatis
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&statisList).Error
	if err != nil {
		return nil, err
	}
	return statisList, nil
}

func (d *GORMCallStatisDao) FindByDate(ctx context.Context, date time.Time) ([]model.CallStatis, error) {
	var statisList []model.CallStatis
	err := d.db.WithContext(ctx).Where("date = ?", date).Find(&statisList).Error
	if err != nil {
		return nil, err
	}
	return statisList, nil
}

func (d *GORMCallStatisDao) FindByDateRange(ctx context.Context, startDate, endDate time.Time) ([]model.CallStatis, error) {
	var statisList []model.CallStatis
	err := d.db.WithContext(ctx).Where("date BETWEEN ? AND ?", startDate, endDate).Find(&statisList).Error
	if err != nil {
		return nil, err
	}
	return statisList, nil
}

func (d *GORMCallStatisDao) List(ctx context.Context, offset, limit int) ([]model.CallStatis, error) {
	var statisList []model.CallStatis
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&statisList).Error
	if err != nil {
		return nil, err
	}
	return statisList, nil
}

func (d *GORMCallStatisDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(model.CallStatis{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

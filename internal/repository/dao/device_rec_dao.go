package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// DeviceRecDao 设备记录数据访问接口
type DeviceRecDao interface {
	// Create 创建设备记录
	Create(ctx context.Context, rec model.DeviceRec) (int64, error)
	// Update 更新设备记录
	Update(ctx context.Context, rec model.DeviceRec, fields []string) error
	// Delete 删除设备记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询设备记录
	FindByID(ctx context.Context, id int64) (model.DeviceRec, error)
	// FindByDeviceID 根据设备ID查询设备记录
	FindByDeviceID(ctx context.Context, deviceID string) (model.DeviceRec, error)
	// FindByEmployeeID 根据员工ID查询设备记录列表
	FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.DeviceRec, error)
	// FindByStatus 根据状态查询设备记录列表
	FindByStatus(ctx context.Context, status int) ([]model.DeviceRec, error)
	// FindByDeviceType 根据设备类型查询设备记录列表
	FindByDeviceType(ctx context.Context, deviceType int) ([]model.DeviceRec, error)
	// FindByLastActiveTime 根据最后活跃时间范围查询设备记录列表
	FindByLastActiveTime(ctx context.Context, startTime, endTime time.Time) ([]model.DeviceRec, error)
	// List 查询设备记录列表
	List(ctx context.Context, offset, limit int) ([]model.DeviceRec, error)
	// Count 统计设备记录总数
	Count(ctx context.Context) (int64, error)
}

type GORMDeviceRecDao struct {
	db *gorm.DB
}

// NewGORMDeviceRecDao 创建设备记录数据访问实现
func NewGORMDeviceRecDao(db *gorm.DB) *GORMDeviceRecDao {
	return &GORMDeviceRecDao{
		db: db,
	}
}

func (d *GORMDeviceRecDao) Create(ctx context.Context, rec model.DeviceRec) (int64, error) {
	err := d.db.WithContext(ctx).Create(rec).Error
	if err != nil {
		return 0, err
	}
	return rec.ID, nil
}

func (d *GORMDeviceRecDao) Update(ctx context.Context, rec model.DeviceRec, fields []string) error {
	return d.db.WithContext(ctx).Model(model.DeviceRec{}).Where("id = ?", rec.ID).Select(fields).Updates(&rec).Error
}

func (d *GORMDeviceRecDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.DeviceRec{}, id).Error
}

func (d *GORMDeviceRecDao) FindByID(ctx context.Context, id int64) (model.DeviceRec, error) {
	var rec model.DeviceRec
	err := d.db.WithContext(ctx).First(&rec, id).Error
	if err != nil {
		return model.DeviceRec{}, err
	}
	return rec, nil
}

func (d *GORMDeviceRecDao) FindByDeviceID(ctx context.Context, deviceID string) (model.DeviceRec, error) {
	var rec model.DeviceRec
	err := d.db.WithContext(ctx).Where("device_id = ?", deviceID).First(&rec).Error
	if err != nil {
		return model.DeviceRec{}, err
	}
	return rec, nil
}

func (d *GORMDeviceRecDao) FindByEmployeeID(ctx context.Context, employeeID int64) ([]model.DeviceRec, error) {
	var recs []model.DeviceRec
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMDeviceRecDao) FindByStatus(ctx context.Context, status int) ([]model.DeviceRec, error) {
	var recs []model.DeviceRec
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMDeviceRecDao) FindByDeviceType(ctx context.Context, deviceType int) ([]model.DeviceRec, error) {
	var recs []model.DeviceRec
	err := d.db.WithContext(ctx).Where("device_type = ?", deviceType).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMDeviceRecDao) FindByLastActiveTime(ctx context.Context, startTime, endTime time.Time) ([]model.DeviceRec, error) {
	var recs []model.DeviceRec
	err := d.db.WithContext(ctx).Where("last_active_time BETWEEN ? AND ?", startTime, endTime).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMDeviceRecDao) List(ctx context.Context, offset, limit int) ([]model.DeviceRec, error) {
	var recs []model.DeviceRec
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMDeviceRecDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.DeviceRec{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

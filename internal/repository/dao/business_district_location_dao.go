package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type BusinessDistrictLocationDao interface {
	Create(ctx context.Context, location model.BusinessDistrictLocation) (int64, error)
	Update(ctx context.Context, location model.BusinessDistrictLocation, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.BusinessDistrictLocation, error)
	FindByName(ctx context.Context, name string) (model.BusinessDistrictLocation, error)
	List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictLocation, error)
	ListByCity(ctx context.Context, city string) ([]model.BusinessDistrictLocation, error)
	Count(ctx context.Context) (int64, error)
}

type GORMBusinessDistrictLocationDao struct {
	db *gorm.DB
}

func NewBusinessDistrictLocationDao(db *gorm.DB) *GORMBusinessDistrictLocationDao {
	return &GORMBusinessDistrictLocationDao{
		db: db,
	}
}

func (d *GORMBusinessDistrictLocationDao) Create(ctx context.Context, location model.BusinessDistrictLocation) (int64, error) {
	err := d.db.WithContext(ctx).Create(location).Error
	if err != nil {
		return 0, err
	}
	return location.ID, nil
}

func (d *GORMBusinessDistrictLocationDao) Update(ctx context.Context, location model.BusinessDistrictLocation, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.BusinessDistrictLocation{}).Where("id = ?", location.ID).Select(fields).Updates(&location).Error
}

func (d *GORMBusinessDistrictLocationDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.BusinessDistrictLocation{}, id).Error
}

func (d *GORMBusinessDistrictLocationDao) FindByID(ctx context.Context, id int64) (model.BusinessDistrictLocation, error) {
	var location model.BusinessDistrictLocation
	err := d.db.WithContext(ctx).First(&location, id).Error
	if err != nil {
		return model.BusinessDistrictLocation{}, err
	}
	return location, nil
}

func (d *GORMBusinessDistrictLocationDao) FindByName(ctx context.Context, name string) (model.BusinessDistrictLocation, error) {
	var location model.BusinessDistrictLocation
	err := d.db.WithContext(ctx).Where("business_district = ?", name).First(&location).Error
	if err != nil {
		return model.BusinessDistrictLocation{}, err
	}
	return location, nil
}

func (d *GORMBusinessDistrictLocationDao) List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictLocation, error) {
	var locations []model.BusinessDistrictLocation
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&locations).Error
	if err != nil {
		return nil, err
	}
	return locations, nil
}

func (d *GORMBusinessDistrictLocationDao) ListByCity(ctx context.Context, city string) ([]model.BusinessDistrictLocation, error) {
	var locations []model.BusinessDistrictLocation
	err := d.db.WithContext(ctx).Where("city = ?", city).Find(&locations).Error
	if err != nil {
		return nil, err
	}
	return locations, nil
}

func (d *GORMBusinessDistrictLocationDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.BusinessDistrictLocation{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

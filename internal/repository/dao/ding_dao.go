package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// DingDao 钉钉配置数据访问接口
type DingDao interface {
	// Create 创建钉钉配置
	Create(ctx context.Context, ding model.Ding) (int64, error)
	// Update 更新钉钉配置
	Update(ctx context.Context, ding model.Ding, fields []string) error
	// Delete 删除钉钉配置
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询钉钉配置
	FindByID(ctx context.Context, id int64) (model.Ding, error)
	// FindByAppKey 根据AppKey查询钉钉配置
	FindByAppKey(ctx context.Context, appKey string) (model.Ding, error)
	// FindByCorpID 根据企业ID查询钉钉配置
	FindByCorpID(ctx context.Context, corpID string) (model.Ding, error)
	// FindByStatus 根据状态查询钉钉配置列表
	FindByStatus(ctx context.Context, status int) ([]model.Ding, error)
	// List 查询钉钉配置列表
	List(ctx context.Context, offset, limit int) ([]model.Ding, error)
	// Count 统计钉钉配置总数
	Count(ctx context.Context) (int64, error)
}

type GORMDingDao struct {
	db *gorm.DB
}

// NeGORMDingDao 创建钉钉配置数据访问实现
func NeGORMDingDao(db *gorm.DB) *GORMDingDao {
	return &GORMDingDao{
		db: db,
	}
}

func (d *GORMDingDao) Create(ctx context.Context, ding model.Ding) error {
	return d.db.WithContext(ctx).Create(ding).Error
}

func (d *GORMDingDao) Update(ctx context.Context, ding model.Ding, fields []string) error {
	return d.db.WithContext(ctx).Model(model.Ding{}).Where("id = ?", ding.ID).Select(fields).Updates(&ding).Error
}

func (d *GORMDingDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Ding{}, id).Error
}

func (d *GORMDingDao) FindByID(ctx context.Context, id int64) (model.Ding, error) {
	var ding model.Ding
	err := d.db.WithContext(ctx).First(&ding, id).Error
	if err != nil {
		return model.Ding{}, err
	}
	return ding, nil
}

func (d *GORMDingDao) FindByAppKey(ctx context.Context, appKey string) (model.Ding, error) {
	var ding model.Ding
	err := d.db.WithContext(ctx).Where("app_key = ?", appKey).First(&ding).Error
	if err != nil {
		return model.Ding{}, err
	}
	return ding, nil
}

func (d *GORMDingDao) FindByCorpID(ctx context.Context, corpID string) (model.Ding, error) {
	var ding model.Ding
	err := d.db.WithContext(ctx).Where("corp_id = ?", corpID).First(&ding).Error
	if err != nil {
		return model.Ding{}, err
	}
	return ding, nil
}

func (d *GORMDingDao) FindByStatus(ctx context.Context, status int) ([]model.Ding, error) {
	var dings []model.Ding
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&dings).Error
	if err != nil {
		return nil, err
	}
	return dings, nil
}

func (d *GORMDingDao) List(ctx context.Context, offset, limit int) ([]model.Ding, error) {
	var dings []model.Ding
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&dings).Error
	if err != nil {
		return nil, err
	}
	return dings, nil
}

func (d *GORMDingDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Ding{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

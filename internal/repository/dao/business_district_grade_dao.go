package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// BusinessDistrictGradeDao 商圈等级数据访问接口
type BusinessDistrictGradeDao interface {
	// Create 创建商圈等级
	Create(ctx context.Context, grade model.BusinessDistrictGrade) (int64, error)
	// Update 更新商圈等级
	Update(ctx context.Context, grade model.BusinessDistrictGrade, fields []string) error
	// Delete 删除商圈等级
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询商圈等级
	FindByID(ctx context.Context, id int64) (model.BusinessDistrictGrade, error)
	// FindByLevel 根据等级值查询商圈等级
	FindByLevel(ctx context.Context, level int) (model.BusinessDistrictGrade, error)
	// List 分页查询商圈等级列表
	List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictGrade, error)
	// Count 统计商圈等级总数
	Count(ctx context.Context) (int64, error)
}

type GORMBusinessDistrictGradeDao struct {
	db *gorm.DB
}

// NewBusinessDistrictGradeDao 创建商圈等级数据访问实现
func NewBusinessDistrictGradeDao(db *gorm.DB) BusinessDistrictGradeDao {
	return &GORMBusinessDistrictGradeDao{
		db: db,
	}
}

func (d *GORMBusinessDistrictGradeDao) Create(ctx context.Context, grade model.BusinessDistrictGrade) (int64, error) {
	err := d.db.WithContext(ctx).Create(grade).Error
	if err != nil {
		return 0, err
	}
	return grade.ID, nil
}

func (d *GORMBusinessDistrictGradeDao) Update(ctx context.Context, grade model.BusinessDistrictGrade, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.BusinessDistrictGrade{}).Where("id = ?", grade.ID).Select(fields).Updates(&grade).Error
}

func (d *GORMBusinessDistrictGradeDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.BusinessDistrictGrade{}, id).Error
}

func (d *GORMBusinessDistrictGradeDao) FindByID(ctx context.Context, id int64) (model.BusinessDistrictGrade, error) {
	var grade model.BusinessDistrictGrade
	err := d.db.WithContext(ctx).First(&grade, id).Error
	if err != nil {
		return model.BusinessDistrictGrade{}, err
	}
	return grade, nil
}

func (d *GORMBusinessDistrictGradeDao) FindByLevel(ctx context.Context, level int) (model.BusinessDistrictGrade, error) {
	var grade model.BusinessDistrictGrade
	err := d.db.WithContext(ctx).Where("level = ?", level).First(&grade).Error
	if err != nil {
		return model.BusinessDistrictGrade{}, err
	}
	return grade, nil
}

func (d *GORMBusinessDistrictGradeDao) List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictGrade, error) {
	var grades []model.BusinessDistrictGrade
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&grades).Error
	if err != nil {
		return nil, err
	}
	return grades, nil
}

func (d *GORMBusinessDistrictGradeDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.BusinessDistrictGrade{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

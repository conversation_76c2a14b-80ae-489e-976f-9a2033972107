package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// ConfigDao 系统配置数据访问接口
type ConfigDao interface {
	// Create 创建系统配置
	Create(ctx context.Context, config model.Config) (int64, error)
	// Update 更新系统配置
	Update(ctx context.Context, config model.Config, fields []string) error
	// Delete 删除系统配置
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询系统配置
	FindByID(ctx context.Context, id int64) (model.Config, error)
	// FindByKey 根据配置键查询系统配置
	FindByKey(ctx context.Context, key string) (model.Config, error)
	// FindByType 根据配置类型查询系统配置列表
	FindByType(ctx context.Context, configType string) ([]model.Config, error)
	// List 查询系统配置列表
	List(ctx context.Context, offset, limit int) ([]model.Config, error)
	// Count 统计系统配置总数
	Count(ctx context.Context) (int64, error)
}

type GORMConfigDao struct {
	db *gorm.DB
}

// NewGORMConfigDao 创建系统配置数据访问实例
func NewGORMConfigDao(db *gorm.DB) *GORMConfigDao {
	return &GORMConfigDao{
		db: db,
	}
}

func (d *GORMConfigDao) Create(ctx context.Context, config model.Config) (int64, error) {
	err := d.db.WithContext(ctx).Create(config).Error
	if err != nil {
		return 0, err
	}
	return config.ID, nil
}

func (d *GORMConfigDao) Update(ctx context.Context, config model.Config, fields []string) error {
	return d.db.WithContext(ctx).Model(model.Config{}).Where("id = ?", config.ID).Select(fields).Updates(&config).Error
}

func (d *GORMConfigDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.Config{}, id).Error
}

func (d *GORMConfigDao) FindByID(ctx context.Context, id int64) (model.Config, error) {
	var config model.Config
	err := d.db.WithContext(ctx).First(&config, id).Error
	if err != nil {
		return model.Config{}, err
	}
	return config, nil
}

func (d *GORMConfigDao) FindByKey(ctx context.Context, key string) (model.Config, error) {
	var config model.Config
	err := d.db.WithContext(ctx).Where("key = ?", key).First(&config).Error
	if err != nil {
		return model.Config{}, err
	}
	return config, nil
}

func (d *GORMConfigDao) FindByType(ctx context.Context, configType string) ([]model.Config, error) {
	var configs []model.Config
	err := d.db.WithContext(ctx).Where("type = ?", configType).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func (d *GORMConfigDao) List(ctx context.Context, offset, limit int) ([]model.Config, error) {
	var configs []model.Config
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&configs).Error
	if err != nil {
		return nil, err
	}
	return configs, nil
}

func (d *GORMConfigDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.Config{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

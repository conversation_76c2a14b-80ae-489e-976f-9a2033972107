package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ClueNotesDao 线索笔记数据访问接口
type ClueNotesDao interface {
	// Create 创建线索笔记
	Create(ctx context.Context, note model.ClueNotes) (int64, error)
	// Update 更新线索笔记
	Update(ctx context.Context, note model.ClueNotes, fields []string) error
	// Delete 删除线索笔记
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索笔记
	FindByID(ctx context.Context, id int64) (model.ClueNotes, error)
	// FindByClueID 根据线索ID查询线索笔记列表
	FindByClueID(ctx context.Context, clueID int64) ([]model.ClueNotes, error)
	// List 分页查询线索笔记列表
	List(ctx context.Context, offset, limit int) ([]model.ClueNotes, error)
	// Count 统计线索笔记总数
	Count(ctx context.Context) (int64, error)
}

type GORMClueNotesDao struct {
	db *gorm.DB
}

// NewGORMClueNotesDao 创建线索笔记数据访问实现
func NewGORMClueNotesDao(db *gorm.DB) *GORMClueNotesDao {
	return &GORMClueNotesDao{
		db: db,
	}
}

func (d *GORMClueNotesDao) Create(ctx context.Context, note model.ClueNotes) (int64, error) {
	err := d.db.WithContext(ctx).Create(note).Error
	if err != nil {
		return 0, err
	}
	return note.ID, nil
}

func (d *GORMClueNotesDao) Update(ctx context.Context, note model.ClueNotes, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ClueNotes{}).Where("id = ?", note.ID).Select(fields).Updates(&note).Error
}

func (d *GORMClueNotesDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ClueNotes{}, id).Error
}

func (d *GORMClueNotesDao) FindByID(ctx context.Context, id int64) (model.ClueNotes, error) {
	var note model.ClueNotes
	err := d.db.WithContext(ctx).First(&note, id).Error
	if err != nil {
		return model.ClueNotes{}, err
	}
	return note, nil
}

func (d *GORMClueNotesDao) FindByClueID(ctx context.Context, clueID int64) ([]model.ClueNotes, error) {
	var notes []model.ClueNotes
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&notes).Error
	if err != nil {
		return nil, err
	}
	return notes, nil
}

func (d *GORMClueNotesDao) List(ctx context.Context, offset, limit int) ([]model.ClueNotes, error) {
	var notes []model.ClueNotes
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&notes).Error
	if err != nil {
		return nil, err
	}
	return notes, nil
}

func (d *GORMClueNotesDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ClueNotes{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

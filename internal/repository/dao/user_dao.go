// Package dao 提供数据访问对象的实现
package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// UserDao 定义用户数据访问接口
type UserDao interface {
	Create(ctx context.Context, user model.User) (int64, error)
	Update(ctx context.Context, user model.User, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.User, error)
	FindByAccount(ctx context.Context, account string) (model.User, error)
	FindByMobile(ctx context.Context, mobile string) (model.User, error)
	ListByCity(ctx context.Context, city string) ([]model.User, error)
	ListByRole(ctx context.Context, roleID int64) ([]model.User, error)
	ListByBDM(ctx context.Context, bdmID int64) ([]model.User, error)
	List(ctx context.Context, offset, limit int) ([]model.User, error)
	Count(ctx context.Context) (int64, error)
}

// GORMUserDao 实现UserDao接口，使用GORM进行数据库操作
type GORMUserDao struct {
	db *gorm.DB
}

// NewGORMUserDao 创建一个新的GORMUserDao实例
func NewGORMUserDao(db *gorm.DB) *GORMUserDao {
	return &GORMUserDao{
		db: db,
	}
}

// Create 创建新用户
func (d GORMUserDao) Create(ctx context.Context, user model.User) (int64, error) {
	err := d.db.WithContext(ctx).Create(&user).Error
	if err != nil {
		return 0, err
	}
	return user.ID, nil
}

// Update 更新用户信息
func (d GORMUserDao) Update(ctx context.Context, user model.User, fields []string) error {
	return d.db.WithContext(ctx).Model(model.User{}).Where("id = ?", user.ID).Select(fields).Updates(&user).Error
}

// Delete 删除用户
func (d GORMUserDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.User{}, id).Error
}

// FindByID 根据ID查找用户
func (d GORMUserDao) FindByID(ctx context.Context, id int64) (model.User, error) {
	var user model.User
	err := d.db.WithContext(ctx).First(&user, id).Error
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

// FindByAccount 根据账号查找用户
func (d GORMUserDao) FindByAccount(ctx context.Context, account string) (model.User, error) {
	var user model.User
	err := d.db.WithContext(ctx).Where("account = ?", account).First(&user).Error
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

// FindByMobile 根据手机号查找用户
func (d GORMUserDao) FindByMobile(ctx context.Context, mobile string) (model.User, error) {
	var user model.User
	err := d.db.WithContext(ctx).Where("mobile = ?", mobile).First(&user).Error
	if err != nil {
		return model.User{}, err
	}
	return user, nil
}

// ListByCity 根据城市查找用户列表
func (d GORMUserDao) ListByCity(ctx context.Context, city string) ([]model.User, error) {
	var users []model.User
	err := d.db.WithContext(ctx).Where("city = ?", city).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// ListByRole 根据角色ID查找用户列表
func (d GORMUserDao) ListByRole(ctx context.Context, roleID int64) ([]model.User, error) {
	var users []model.User
	err := d.db.WithContext(ctx).Where("role_id = ?", roleID).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// ListByBDM 根据BDM ID查找用户列表
func (d GORMUserDao) ListByBDM(ctx context.Context, bdmID int64) ([]model.User, error) {
	var users []model.User
	err := d.db.WithContext(ctx).Where("bdm_id = ?", bdmID).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// List 分页查询用户列表
func (d GORMUserDao) List(ctx context.Context, offset, limit int) ([]model.User, error) {
	var users []model.User
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&users).Error
	if err != nil {
		return nil, err
	}
	return users, nil
}

// Count 统计用户总数
func (d GORMUserDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.User{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

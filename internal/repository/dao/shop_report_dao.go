package dao

import (
	"context"
	"time"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type ShopReportDao interface {
	Create(ctx context.Context, report model.ShopReport) (int64, error)
	Update(ctx context.Context, report model.ShopReport, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.ShopReport, error)
	ListByShopID(ctx context.Context, shopID int64) ([]model.ShopReport, error)
	ListByShopCode(ctx context.Context, shopCode string) ([]model.ShopReport, error)
	ListByEmployeeID(ctx context.Context, employeeID int) ([]model.ShopReport, error)
	ListByCreateTime(ctx context.Context, startTime, endTime time.Time) ([]model.ShopReport, error)
	ListByBrandName(ctx context.Context, brandName string) ([]model.ShopReport, error)
	List(ctx context.Context, offset, limit int) ([]model.ShopReport, error)
	Count(ctx context.Context) (int64, error)
}

type GORMShopReportDao struct {
	db *gorm.DB
}

func NewGORMShopReportDao(db *gorm.DB) ShopReportDao {
	return &GORMShopReportDao{
		db: db,
	}
}

func (d GORMShopReportDao) Create(ctx context.Context, report model.ShopReport) (int64, error) {
	err := d.db.WithContext(ctx).Create(&report).Error
	if err != nil {
		return 0, err
	}
	return report.ID, nil
}

func (d GORMShopReportDao) Update(ctx context.Context, report model.ShopReport, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ShopReport{}).Where("id = ?", report.ID).Select(fields).Updates(&report).Error
}

func (d GORMShopReportDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ShopReport{}, id).Error
}

func (d GORMShopReportDao) FindByID(ctx context.Context, id int64) (model.ShopReport, error) {
	var report model.ShopReport
	err := d.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return model.ShopReport{}, err
	}
	return report, nil
}

func (d GORMShopReportDao) ListByShopID(ctx context.Context, shopID int64) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) ListByShopCode(ctx context.Context, shopCode string) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Where("shop_code = ?", shopCode).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) ListByEmployeeID(ctx context.Context, employeeID int) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) ListByCreateTime(ctx context.Context, startTime, endTime time.Time) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startTime, endTime).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) ListByBrandName(ctx context.Context, brandName string) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Where("brand_name = ?", brandName).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) List(ctx context.Context, offset, limit int) ([]model.ShopReport, error) {
	var reports []model.ShopReport
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d GORMShopReportDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ShopReport{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

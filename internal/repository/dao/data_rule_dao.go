package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// DataRuleDao 数据规则数据访问接口
type DataRuleDao interface {
	// Create 创建数据规则
	Create(ctx context.Context, rule model.DataRule) (int64, error)
	// Update 更新数据规则
	Update(ctx context.Context, rule model.DataRule, fields []string) error
	// Delete 删除数据规则
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询数据规则
	FindByID(ctx context.Context, id int64) (model.DataRule, error)
	// FindByType 根据规则类型查询数据规则列表
	FindByType(ctx context.Context, ruleType int) ([]model.DataRule, error)
	// FindByStatus 根据状态查询数据规则列表
	FindByStatus(ctx context.Context, status int) ([]model.DataRule, error)
	// FindByCreator 根据创建人查询数据规则列表
	FindByCreator(ctx context.Context, creatorID int64) ([]model.DataRule, error)
	// List 查询数据规则列表
	List(ctx context.Context, offset, limit int) ([]model.DataRule, error)
	// Count 统计数据规则总数
	Count(ctx context.Context) (int64, error)
}

type GORMDataRuleDao struct {
	db *gorm.DB
}

// NewGORMDataRuleDao 创建数据规则数据访问实现
func NewGORMDataRuleDao(db *gorm.DB) *GORMDataRuleDao {
	return &GORMDataRuleDao{
		db: db,
	}
}

func (d *GORMDataRuleDao) Create(ctx context.Context, rule model.DataRule) (int64, error) {
	err := d.db.WithContext(ctx).Create(rule).Error
	if err != nil {
		return 0, err
	}
	return rule.ID, nil
}

func (d *GORMDataRuleDao) Update(ctx context.Context, rule model.DataRule, fields []string) error {
	return d.db.WithContext(ctx).Model(model.DataRule{}).Where("id = ?", rule.ID).Select(fields).Updates(&rule).Error
}

func (d *GORMDataRuleDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.DataRule{}, id).Error
}

func (d *GORMDataRuleDao) FindByID(ctx context.Context, id int64) (model.DataRule, error) {
	var rule model.DataRule
	err := d.db.WithContext(ctx).First(&rule, id).Error
	if err != nil {
		return model.DataRule{}, err
	}
	return rule, nil
}

func (d *GORMDataRuleDao) FindByType(ctx context.Context, ruleType int) ([]model.DataRule, error) {
	var rules []model.DataRule
	err := d.db.WithContext(ctx).Where("rule_type = ?", ruleType).Find(&rules).Error
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func (d *GORMDataRuleDao) FindByStatus(ctx context.Context, status int) ([]model.DataRule, error) {
	var rules []model.DataRule
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&rules).Error
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func (d *GORMDataRuleDao) FindByCreator(ctx context.Context, creatorID int64) ([]model.DataRule, error) {
	var rules []model.DataRule
	err := d.db.WithContext(ctx).Where("creator_id = ?", creatorID).Find(&rules).Error
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func (d *GORMDataRuleDao) List(ctx context.Context, offset, limit int) ([]model.DataRule, error) {
	var rules []model.DataRule
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&rules).Error
	if err != nil {
		return nil, err
	}
	return rules, nil
}

func (d *GORMDataRuleDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.DataRule{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

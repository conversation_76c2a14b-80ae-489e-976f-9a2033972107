package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ComplaintRecDao 投诉记录数据访问接口
type ComplaintRecDao interface {
	// Create 创建投诉记录
	Create(ctx context.Context, rec model.ComplaintRec) (int64, error)
	// Update 更新投诉记录
	Update(ctx context.Context, rec model.ComplaintRec, fields []string) error
	// Delete 删除投诉记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询投诉记录
	FindByID(ctx context.Context, id int64) (model.ComplaintRec, error)
	// FindByClueID 根据线索ID查询投诉记录
	FindByClueID(ctx context.Context, clueID int64) ([]model.ComplaintRec, error)
	// FindByProcessorID 根据处理人ID查询投诉记录
	FindByProcessorID(ctx context.Context, processorID int64) ([]model.ComplaintRec, error)
	// FindByStatus 根据处理状态查询投诉记录
	FindByStatus(ctx context.Context, status int) ([]model.ComplaintRec, error)
	// FindByDateRange 根据时间范围查询投诉记录
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.ComplaintRec, error)
	// List 查询投诉记录列表
	List(ctx context.Context, offset, limit int) ([]model.ComplaintRec, error)
	// Count 统计投诉记录总数
	Count(ctx context.Context) (int64, error)
}

type GORMComplaintRecDao struct {
	db *gorm.DB
}

// NewGORMComplaintRecDao 创建投诉记录数据访问实现
func NewGORMComplaintRecDao(db *gorm.DB) *GORMComplaintRecDao {
	return &GORMComplaintRecDao{
		db: db,
	}
}

func (d *GORMComplaintRecDao) Create(ctx context.Context, rec model.ComplaintRec) (int64, error) {
	err := d.db.WithContext(ctx).Create(rec).Error
	if err != nil {
		return 0, err
	}
	return rec.ID, nil
}

func (d *GORMComplaintRecDao) Update(ctx context.Context, rec model.ComplaintRec, fields []string) error {
	return d.db.WithContext(ctx).Model(model.ComplaintRec{}).Where("id = ?", rec.ID).Select(fields).Updates(&rec).Error
}

func (d *GORMComplaintRecDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ComplaintRec{}, id).Error
}

func (d *GORMComplaintRecDao) FindByID(ctx context.Context, id int64) (model.ComplaintRec, error) {
	var rec model.ComplaintRec
	err := d.db.WithContext(ctx).First(&rec, id).Error
	if err != nil {
		return model.ComplaintRec{}, err
	}
	return rec, nil
}

func (d *GORMComplaintRecDao) FindByClueID(ctx context.Context, clueID int64) ([]model.ComplaintRec, error) {
	var recs []model.ComplaintRec
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMComplaintRecDao) FindByProcessorID(ctx context.Context, processorID int64) ([]model.ComplaintRec, error) {
	var recs []model.ComplaintRec
	err := d.db.WithContext(ctx).Where("processor_id = ?", processorID).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMComplaintRecDao) FindByStatus(ctx context.Context, status int) ([]model.ComplaintRec, error) {
	var recs []model.ComplaintRec
	err := d.db.WithContext(ctx).Where("process_status = ?", status).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMComplaintRecDao) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.ComplaintRec, error) {
	var recs []model.ComplaintRec
	err := d.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startTime, endTime).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMComplaintRecDao) List(ctx context.Context, offset, limit int) ([]model.ComplaintRec, error) {
	var recs []model.ComplaintRec
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&recs).Error
	if err != nil {
		return nil, err
	}
	return recs, nil
}

func (d *GORMComplaintRecDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ComplaintRec{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

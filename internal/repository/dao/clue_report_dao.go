package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ClueReportDao 线索报告数据访问接口
type ClueReportDao interface {
	// Create 创建线索报告
	Create(ctx context.Context, report model.ClueReport) (int64, error)
	// Update 更新线索报告
	Update(ctx context.Context, report model.ClueReport, fields []string) error
	// Delete 删除线索报告
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索报告
	FindByID(ctx context.Context, id int64) (model.ClueReport, error)
	// FindByClueID 根据线索ID查询线索报告列表
	FindByClueID(ctx context.Context, clueID int64) ([]model.ClueReport, error)
	// FindByType 根据报告类型查询线索报告列表
	FindByType(ctx context.Context, reportType int) ([]model.ClueReport, error)
	// List 分页查询线索报告列表
	List(ctx context.Context, offset, limit int) ([]model.ClueReport, error)
	// Count 统计线索报告总数
	Count(ctx context.Context) (int64, error)
}

type GORMClueReportDao struct {
	db *gorm.DB
}

// NewGORMClueReportDao 创建线索报告数据访问实现
func NewGORMClueReportDao(db *gorm.DB) *GORMClueReportDao {
	return &GORMClueReportDao{
		db: db,
	}
}

func (d *GORMClueReportDao) Create(ctx context.Context, report model.ClueReport) (int64, error) {
	err := d.db.WithContext(ctx).Create(report).Error
	if err != nil {
		return 0, err
	}
	return report.ID, nil
}

func (d *GORMClueReportDao) Update(ctx context.Context, report model.ClueReport, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ClueReport{}).Where("id = ?", report.ID).Select(fields).Updates(&report).Error
}

func (d *GORMClueReportDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ClueReport{}, id).Error
}

func (d *GORMClueReportDao) FindByID(ctx context.Context, id int64) (model.ClueReport, error) {
	var report model.ClueReport
	err := d.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return model.ClueReport{}, err
	}
	return report, nil
}

func (d *GORMClueReportDao) FindByClueID(ctx context.Context, clueID int64) ([]model.ClueReport, error) {
	var reports []model.ClueReport
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueReportDao) FindByType(ctx context.Context, reportType int) ([]model.ClueReport, error) {
	var reports []model.ClueReport
	err := d.db.WithContext(ctx).Where("type = ?", reportType).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueReportDao) List(ctx context.Context, offset, limit int) ([]model.ClueReport, error) {
	var reports []model.ClueReport
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueReportDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ClueReport{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package model

import (
	"time"
)

// ClueFilterReport 线索过滤报表
type ClueFilterReport struct {
	ID           int64      // 主键ID
	ClueID       int64      // 线索ID
	FilterType   int        // 过滤类型 1:重复 2:黑名单 3:无效号码 4:其他
	FilterReason string     // 过滤原因
	OperatorID   int64      // 操作人ID
	OperatorName string     // 操作人姓名
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

// TableName 表名
func (m *ClueFilterReport) TableName() string {
	return "sq_clue_filter_report"
}

package model

import "time"

// CircleShop 爬虫商圈店铺名称
type CircleShop struct {
	ID             int64     // 主键ID
	CreatedAt      time.Time // 创建时间
	UpdatedAt      time.Time // 更新时间
	Name           string    // 店铺名称
	CircleID       int64     // 商圈ID
	CrawlTime      int       // 爬取时间
	Distance       int       // 距离，米
	Tags           string    // 标签
	MonthSale      int       // 月售
	Score          float64   // 评分
	CategoryName   string    // 分类名称
	LowerPrice     float64   // 低价均值
	TallPrice      float64   // 高价均值
	IsBrand        int8      // 是否是品牌
	IsNew          int8      // 是否是新店
	LastUpdateTime time.Time // 最近更新时间
	BrandID        int64     // 品牌id
}

func (m *CircleShop) TableName() string {
	return "shenquan.circle_shops"
}

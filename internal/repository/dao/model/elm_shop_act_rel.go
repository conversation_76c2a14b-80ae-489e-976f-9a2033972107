package model

import "time"

// ElmShopActRel 饿了么店铺活动关系表
type ElmShopActRel struct {
	ID                int64     // 自增ID
	ShopID            int64     // 店铺ID
	ActRecID          int64     // 报名ID
	ActivityID        int64     // 活动ID
	OriShopID         string    // 饿了么店铺ID
	OriShopName       string    // 店铺名称
	ShopScore         float64   // 店铺评分
	AuditStatus       int8      // 审核状态 0-待审核，1-人审通过，2-拒绝，3-免审规则通过
	BonusFee          int64     // 奖励金
	BounsOrderNum     int64     // 每日活动库存
	ChannelServiceFee float64   // 渠道推广费率
	CityServicesFee   float64   // 招商费率
	CommissionRate    float64   // 佣金比率
	PromotionState    int64     // 推广状态 0- --，1-未开始，2-进行中，3-已暂停，4-已结束，5-退出中，6-已退出
	SalesNum          int64     // 销量
	ServicesFee       int64     // 招商服务费
	OrderAmtLimit     int64     // 满XX元
	CityName          string    // 城市
	DistrictName      string    // 区
	EnrollDate        time.Time // 报名时间
	StartDate         time.Time // 开始时间
	EndDate           time.Time // 结束时间
	CreatedAt         time.Time // 创建时间
	UpdatedAt         time.Time // 更新时间
	ShopCode          string    // 店铺的code
	TerminateTime     time.Time // 退出时间
	IsDeleted         int8      // 是否删除
	MatchName         string    // 去除所有特殊字符的店铺名
	BusinessDistrict  string    // 商圈
	RefuseDate        time.Time // 拒绝时间
	PassDate          time.Time // 通过时间
	BrandID           int64     // 品牌id
}

// TableName 表名
func (e *ElmShopActRel) TableName() string {
	return "shenquan.elm_shop_act_rels"
}

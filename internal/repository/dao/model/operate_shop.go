package model

// OperateShop 运营店铺表
type OperateShop struct {
	ID              int64   `gorm:"column:id;primaryKey;autoIncrement"`
	Name            string  `gorm:"column:name;default:'';comment:店铺名"`
	CreateTime      int     `gorm:"column:create_time;default:0;comment:创建时间"`
	UpdateTime      int     `gorm:"column:update_time;default:0;comment:更新时间"`
	CategoryID      int     `gorm:"column:category_id;default:0;comment:分类id"`
	Tel             string  `gorm:"column:tel;default:'';comment:联系方式"`
	Address         string  `gorm:"column:address;default:'';comment:地址"`
	IsDine          int8    `gorm:"column:is_dine;default:0;comment:有无堂食"`
	IsNew           int8    `gorm:"column:is_new;default:0;comment:是否新店"`
	StoreArea       int     `gorm:"column:store_area;default:0;comment:门店面积"`
	BrandID         int64   `gorm:"column:brand_id;default:0;comment:品牌id"`
	Platform        int     `gorm:"column:platform;default:0;comment:平台"`
	ShopCode        string  `gorm:"column:shop_code;default:'';comment:店铺编码"`
	IsCharge        int8    `gorm:"column:is_charge;default:0;comment:前置收费"`
	Fee             float64 `gorm:"column:fee;default:0.00;comment:收费金额"`
	CommissionRate  float64 `gorm:"column:commission_rate;default:0.00;comment:佣金比例"`
	Remark          string  `gorm:"column:remark;default:'';comment:备注"`
	ClueID          int64   `gorm:"column:clue_id;default:0;comment:线索id"`
	City            string  `gorm:"column:city;default:'';comment:城市"`
	StartTime       int     `gorm:"column:start_time;default:0;comment:签约时间"`
	EndTime         int     `gorm:"column:end_time;default:0;comment:结束时间"`
	UserID          int64   `gorm:"column:user_id;default:0;comment:用户id"`
	Status          int8    `gorm:"column:status;default:1;comment:状态"`
	LevelTier       string  `gorm:"column:level_tier;default:'';comment:分层等级"`
	HasPromotion    int     `gorm:"column:has_promotion;default:-1;comment:有无推广"`
	HasBwc          int     `gorm:"column:has_bwc;default:-1;comment:推霸王餐"`
	MainIssues      string  `gorm:"column:main_issues;default:'';comment:主要问题"`
	TerminationTime int     `gorm:"column:termination_time;default:0;comment:解约时间"`
	IsScaling       int     `gorm:"column:is_scaling;default:-1;comment:是否起量"`
	Reason          string  `gorm:"column:reason;default:''"`
	UserType        int8    `gorm:"column:user_type;default:1;comment:用户类型"`
	Mode            int8    `gorm:"column:mode;default:0;comment:模式"`
	StoreID         string  `gorm:"column:store_id;default:'';comment:店铺id"`
}

// TableName 表名
func (m *OperateShop) TableName() string {
	return "sq_operate_shops"
}

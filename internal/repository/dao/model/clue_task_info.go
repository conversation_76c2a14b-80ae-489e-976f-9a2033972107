package model

import "time"

// ClueTaskInfo 线索任务详情表
type ClueTaskInfo struct {
	ID          int64      // 主键ID
	TaskID      int64      // 任务ID
	Content     string     // 任务详情内容
	Type        int        // 详情类型 1:进度更新 2:问题反馈 3:其他
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *ClueTaskInfo) TableName() string {
	return "sq_clue_task_info"
}

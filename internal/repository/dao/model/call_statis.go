package model

import (
	"time"
)

// CallStatis 通话统计
type CallStatis struct {
	ID             int64      // 主键ID
	EmployeeID     int64      // 员工ID
	EmployeeName   string     // 员工姓名
	Date           time.Time  // 统计日期
	TotalCalls     int        // 总通话次数
	ConnectedCalls int        // 接通次数
	TotalDuration  int        // 总通话时长(秒)
	AvgDuration    float64    // 平均通话时长(秒)
	CreatedAt      time.Time  // 创建时间
	UpdatedAt      time.Time  // 更新时间
	DeletedAt      *time.Time // 删除时间
}

// TableName 表名
func (m *CallStatis) TableName() string {
	return "sq_call_statis"
}

package model

import "time"

// ElmShopCodeEmployeeRel 饿了么店铺员工关联表
type ElmShopCodeEmployeeRel struct {
	ID         int64     // 主键ID
	EmployeeID int64     // 员工ID
	StartTime  time.Time // 开始时间
	EndTime    time.Time // 结束时间
	TagID      int64     // 类型 1--新签 0--其他
	Remark     string    // 备注
	CreatedAt  time.Time // 创建时间
	UpdatedAt  time.Time // 更新时间
	DeletedAt  time.Time // 删除时间
	City       string    // 城市
	ShopCode   string    // 店铺的code
	ShopName   string    // 店铺名称
	Type       int8      // 1-非品牌 2-品牌
	BrandName  string    // 品牌名称
	ExpireTime int64     // 过期时间
}

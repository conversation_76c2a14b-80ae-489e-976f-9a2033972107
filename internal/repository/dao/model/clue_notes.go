package model

import "time"

// ClueNotes 线索笔记表
type ClueNotes struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Content     string     // 笔记内容
	Type        int        // 笔记类型 1:普通笔记 2:重要笔记
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *ClueNotes) TableName() string {
	return "sq_clue_notes"
}

package model

import "time"

// BusinessDistrictGradeInfo 商圈等级信息
type BusinessDistrictGradeInfo struct {
	ID               int64      // 主键ID
	GradeID          int64      // 等级ID
	BusinessDistrict string     // 商圈名称
	City             string     // 城市
	Province         string     // 省份
	Area             string     // 区域
	Address          string     // 详细地址
	Longitude        float64    // 经度
	Latitude         float64    // 纬度
	Status           int        // 状态 1:正常 2:禁用
	CreatedAt        time.Time  // 创建时间
	UpdatedAt        time.Time  // 更新时间
	DeletedAt        *time.Time // 删除时间
}

// TableName 表名
func (m *BusinessDistrictGradeInfo) TableName() string {
	return "business_district_grade_info"
}

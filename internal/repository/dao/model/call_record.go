package model

// CallRecord 通话记录
type CallRecord struct {
	ID          int64  // 主键ID
	Caller      string // 主叫号码
	Callee      string // 被叫号码
	UserID      int64  // 用户ID
	ClueID      int64  // 线索ID
	OrderSn     string // 订单编号
	TelID       string // 电话ID
	ChannelType int8   // 渠道类型
	StartTime   int64  // 开始时间
	EndTime     int64  // 结束时间
	HoldTime    int    // 通话时长
	Status      int8   // 状态
	ErrorMsg    string // 错误信息
	OriAudioURL string // 原始录音URL
	AudioURL    string // 处理后的录音URL
	ShowNo      string // 显示号码
	OrderID     string // 通话ID
	EventType   string // 事件类型
	BindId      string // 绑定信息
	CallType    string // 呼叫类型
	CreateTime  int64  // 创建时间
	UpdateTime  int64  // 更新时间
}

// TableName 表名
func (m *CallRecord) TableName() string {
	return "sq_call_record"
}

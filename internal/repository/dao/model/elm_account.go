package model

import "time"

// Elm<PERSON>ccount represents the elm account information table
type Elm<PERSON><PERSON><PERSON> struct {
	// ID is the auto-incrementing primary key
	ID int64
	// Username is the account username
	Username string
	// Password is the account password
	Password string
	// City is the account's associated city
	City string
	// <PERSON><PERSON> is the login cookie
	Cookie string
	// AppID is the application identifier
	AppID string
	// OpenCrawl indicates whether crawling is enabled
	OpenCrawl bool
	// CreatedAt is the creation timestamp
	CreatedAt time.Time
	// UpdatedAt is the last update timestamp
	UpdatedAt time.Time
}

// TableName returns the table name for the ElmAccount model
func (e *ElmAccount) TableName() string {
	return "rebete_new.elm_account_settings"
}

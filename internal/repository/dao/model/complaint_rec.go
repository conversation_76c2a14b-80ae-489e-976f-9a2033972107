package model

import "time"

// ComplaintRec 投诉记录模型
type ComplaintRec struct {
	ID int64 // 主键ID

	ClueID int64 // 线索ID

	ComplaintType int // 投诉类型：1-客户投诉 2-内部投诉

	ComplaintReason string // 投诉原因

	ComplaintContent string // 投诉内容

	ProcessStatus int // 处理状态：0-待处理 1-处理中 2-已处理

	ProcessResult string // 处理结果

	ProcessorID int64 // 处理人ID

	ProcessorName string // 处理人姓名

	ProcessTime *time.Time // 处理时间

	CreatedAt time.Time // 创建时间

	UpdatedAt time.Time // 更新时间

	DeletedAt *time.Time // 删除时间
}

// TableName 表名
func (m *ComplaintRec) TableName() string {
	return "sq_complaint_recs"
}

package model

import "time"

// OperateClick 运营点击模型
type OperateClick struct {
	ID           int64      // 主键ID
	ShopID       string     // 店铺ID
	ShopName     string     // 店铺名称
	OperatorID   int64      // 运营人员ID
	OperatorName string     // 运营人员姓名
	ClickType    int        // 点击类型：1-商品 2-活动 3-其他
	ClickTarget  string     // 点击目标
	ClickTime    time.Time  // 点击时间
	ClickIP      string     // 点击IP
	ClickUA      string     // 点击UA
	ClickReferer string     // 点击来源
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

// TableName 表名
func (m *OperateClick) TableName() string {
	return "sq_operate_clicks"
}

package model

import "time"

// Brand 品牌表
type Brand struct {
	ID               int64     // 主键ID
	CreatedAt        time.Time // 创建时间
	UpdatedAt        time.Time // 更新时间
	BrandName        string    // 品牌名称
	BrandLogo        string    // 品牌logo
	DetailImg        string    // 详情图
	Desc             string    // 描述
	DetailDesc       string    // 详细描述
	Status           int8      // 0 下架 1 上架
	Weight           int       // 权重
	Policy           string    // 招商政策
	StoreNum         int       // 总门店数
	ContactName      string    // 联系人
	ContactPhone     string    // 联系方式
	Company          string    // 所属公司名称
	Average          float64   // 人均客单价，元
	CategoryID       int64     // 分类ID
	InStoreNum       int64     // 谈成门店数
	City             string    // 城市
	Province         string    // 省份
	CreateType       int8      // 0手动创建 1爬虫
	CrawlTime        int       // 爬取时间
	LockStatus       int8      // 0未锁定 1已锁定
	OperationMode    string    // 经营类目
	BrandCode        string    // 品牌编号
	AreaMin          int64     // 最小面积
	AreaMax          int64     // 最大面积
	ShowType         int       // 展示类型
	PlatformStoreNum int       // 平台门店数
	MinMonthSale     int       // 最小月售
	MaxMonthSale     int       // 最大月售
}

func (m *Brand) TableName() string {
	return "shenquan.brands"
}

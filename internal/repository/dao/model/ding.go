package model

import "time"

// Ding 钉钉配置模型
type Ding struct {
	ID          int64      // 主键ID
	AppKey      string     // 应用的唯一标识key
	AppSecret   string     // 应用的密钥
	AgentID     string     // 应用的AgentID
	CorpID      string     // 企业ID
	Status      int        // 状态：0-禁用 1-启用
	Description string     // 配置描述
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *Ding) TableName() string {
	return "sq_ding"
}

package model

// Ding 钉钉配置模型 (匹配DBML中的sq_ding表结构)
type Ding struct {
	ID          int64  `gorm:"column:id;primaryKey;autoIncrement" json:"id"`                         // 主键ID
	Name        string `gorm:"column:name;type:varchar(200);default:''" json:"name"`                 // 钉钉名字
	AccessToken string `gorm:"column:access_token;type:varchar(255);default:''" json:"access_token"` // 钉钉token
	Secret      string `gorm:"column:secret;type:varchar(255);default:''" json:"secret"`             // 加密密钥
	CreateTime  int64  `gorm:"column:create_time;default:0" json:"create_time"`                      // 创建时间
	UpdateTime  int64  `gorm:"column:update_time;default:0" json:"update_time"`                      // 更新时间
}

// TableName 表名
func (m *Ding) TableName() string {
	return "sq_ding"
}

package model

import "time"

// DataRule 数据规则模型
type DataRule struct {
	ID int64 // 主键ID

	RuleName string // 规则名称

	RuleType int // 规则类型：1-过滤规则 2-分配规则 3-其他

	RuleContent string // 规则内容（JSON格式）

	Status int // 状态：0-禁用 1-启用

	Priority int // 优先级

	Description string // 规则描述

	CreatorID int64 // 创建人ID

	CreatorName string // 创建人姓名

	CreatedAt time.Time // 创建时间

	UpdatedAt time.Time // 更新时间

	DeletedAt *time.Time // 删除时间
}

// TableName 表名
func (m *DataRule) TableName() string {
	return "sq_data_rule"
}

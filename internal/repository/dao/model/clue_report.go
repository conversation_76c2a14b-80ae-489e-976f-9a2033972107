package model

import "time"

// ClueReport 线索报告表
type ClueReport struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Title       string     // 报告标题
	Content     string     // 报告内容
	Type        int        // 报告类型 1:日报 2:周报 3:月报
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *ClueReport) TableName() string {
	return "sq_clue_report"
}

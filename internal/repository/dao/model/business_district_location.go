package model

import "time"

// BusinessDistrictLocation 爬虫任务
type BusinessDistrictLocation struct {
	ID               int64     // 主键ID
	CreatedAt        time.Time // 创建时间
	UpdatedAt        time.Time // 更新时间
	BusinessDistrict string    // 商圈名称
	Province         string    // 省份
	City             string    // 城市
	District         string    // 区域
	Location         string    // 经纬度
	FormattedAddress string    // 格式化地址
	CrawlTime        int       // 爬取时间
	Level            int       // 等级
	Type             int8      // 类型
	CrawlNum         int       // 爬取次数
}

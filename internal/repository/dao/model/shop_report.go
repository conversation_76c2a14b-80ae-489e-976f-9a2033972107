package model

import "time"

// ShopReport 店铺报告表
type ShopReport struct {
	ID                      int64     `gorm:"column:id;primaryKey;autoIncrement"`
	ShopID                  int64     `gorm:"column:shop_id;default:0;comment:店铺ID"`
	ShopCode                string    `gorm:"column:shop_code"`
	ShopName                string    `gorm:"column:shop_name;default:'';comment:店铺名称"`
	BrandName               string    `gorm:"column:brand_name;default:'';comment:品牌名称"`
	UV                      int64     `gorm:"column:uv;default:0;comment:引流uv"`
	PromoteTaokeNum         int64     `gorm:"column:promote_taoke_num;default:0;comment:推广淘客数"`
	PayAmount               float64   `gorm:"column:pay_amount;default:0.00;comment:付款金额"`
	PayNum                  int64     `gorm:"column:pay_num;default:0;comment:付款笔数"`
	OrderOriginalServiceFee float64   `gorm:"column:order_original_service_fee;default:0.00;comment:预估订单原始服务费"`
	OrderServicesFee        float64   `gorm:"column:order_services_fee;default:0.00;comment:预估订单招商服务费"`
	UserSettleAmount        float64   `gorm:"column:user_settle_amount;default:0.00;comment:用户结算金额"`
	ShopSettleAmount        float64   `gorm:"column:shop_settle_amount;default:0.00;comment:商家结算金额"`
	SettleNum               int64     `gorm:"column:settle_num;default:0;comment:结算笔数"`
	OriginalServiceFee      float64   `gorm:"column:original_service_fee;default:0.00;comment:总预估结算原始服务费"`
	SettleFee               float64   `gorm:"column:settle_fee;default:0.00;comment:总预估结算招商服务费"`
	CityServiceFee          float64   `gorm:"column:city_service_fee;default:0.00;comment:总预估订单城市服务商服务费"`
	CitySettleFee           float64   `gorm:"column:city_settle_fee;default:0.00;comment:总预估结算城市服务商服务费"`
	OrderServiceFee         float64   `gorm:"column:order_service_fee;default:0.00;comment:总预估订单招商服务商服务费"`
	OrderSettleFee          float64   `gorm:"column:order_settle_fee;default:0.00;comment:总预估结算招商服务商服务费"`
	City                    string    `gorm:"column:city;default:'';comment:城市"`
	District                string    `gorm:"column:district;default:'';comment:区域"`
	BusinessDistrict        string    `gorm:"column:business_district;default:'';comment:商圈"`
	CreateTime              time.Time `gorm:"column:create_time;comment:创建时间"`
	EmployeeID              int       `gorm:"column:employee_id;default:0;comment:员工表id"`
	IsNewShop               int8      `gorm:"column:is_new_shop;default:0;comment:是否新店"`
	Timeline                int8      `gorm:"column:timeline;default:0;comment:时间线"`
	NewSignature            int8      `gorm:"column:new_signature;default:0;comment:新签"`
	Type                    int8      `gorm:"column:type;default:1;comment:1-非品牌 2-品牌"`
	OfficialUV              int64     `gorm:"column:official_uv;default:0;comment:官方引流uv"`
	OfficialSettleNum       int64     `gorm:"column:official_settle_num;default:0;comment:官方结算笔数"`
	OfficialSettleFee       float64   `gorm:"column:official_settle_fee;default:0.00;comment:官方总预估结算招商服务费"`
}

// TableName 表名
func (m *ShopReport) TableName() string {
	return "shop_reports"
}

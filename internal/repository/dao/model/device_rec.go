package model

import "time"

// DeviceRec 设备记录模型
type DeviceRec struct {
	ID int64 // 主键ID

	DeviceID string // 设备ID

	DeviceType int // 设备类型：1-Android 2-iOS 3-Web 4-其他

	DeviceName string // 设备名称

	DeviceModel string // 设备型号

	DeviceVersion string // 设备版本

	EmployeeID int64 // 员工ID

	EmployeeName string // 员工姓名

	Status int // 状态：0-离线 1-在线

	LastActiveTime *time.Time // 最后活跃时间

	CreatedAt time.Time // 创建时间

	UpdatedAt time.Time // 更新时间

	DeletedAt *time.Time // 删除时间
}

// TableName 表名
func (m *DeviceRec) TableName() string {
	return "sq_device_rec"
}

package model

import "time"

// OperateSettlement 运营结算模型
type OperateSettlement struct {
	ID               int64      // 主键ID
	ShopID           string     // 店铺ID
	ShopName         string     // 店铺名称
	OperatorID       int64      // 运营人员ID
	OperatorName     string     // 运营人员姓名
	SettlementAmount float64    // 结算金额
	SettlementType   int        // 结算类型：1-日结 2-周结 3-月结
	SettlementStatus int        // 结算状态：0-待结算 1-已结算 2-已取消
	SettlementTime   *time.Time // 结算时间
	StartTime        time.Time  // 结算开始时间
	EndTime          time.Time  // 结算结束时间
	Remark           string     // 备注
	CreatedAt        time.Time  // 创建时间
	UpdatedAt        time.Time  // 更新时间
	DeletedAt        *time.Time // 删除时间
}

// TableName 表名
func (m *OperateSettlement) TableName() string {
	return "sq_operate_settlements"
}

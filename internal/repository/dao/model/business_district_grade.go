package model

import "time"

// BusinessDistrictGrade 商圈等级表
type BusinessDistrictGrade struct {
	ID          int64      // 主键ID
	Name        string     // 等级名称
	Description string     // 等级描述
	Level       int        // 等级值
	Status      int        // 状态 1:正常 2:禁用
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *BusinessDistrictGrade) TableName() string {
	return "sq_business_district_grade"
}

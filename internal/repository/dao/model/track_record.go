package model

// TrackRecord 跟进记录表
type TrackRecord struct {
	ID              int64   // 主键
	ClueID          int64   // 线索id
	ClueType        int8    // 1-品牌跟进 2代运营
	UserID          int64   // 用户id
	CreateTime      int64   // 创建时间
	UpdateTime      int64   // 更新时间
	Status          int     // 状态
	CancelTime      int64   // 取消时间
	SubmitTime      int64   // 提报时间
	StoreNum        int     // 谈店数量
	ActivityName    string  // 活动id
	ActivityCity    string  // 活动区域
	Bounty          float64 // 奖励金
	BountyLimitCent float64 // 奖励金门槛
	DailyQuantity   int     // 活动日库存
	IfCustomer      int8    // 是否添加客服
	Platform        int     // 平台
	StoreID         string  // 店铺id
	BelongID        int64   // 归属id
}

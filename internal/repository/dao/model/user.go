package model

// User 用户表
type User struct {
	ID                  int64   // 主键
	SN                  int64   // 编号
	Avatar              string  // 头像
	RealName            string  // 真实姓名
	Nickname            string  // 用户昵称
	Account             string  // 用户账号
	Password            string  // 用户密码
	Mobile              string  // 用户电话
	Sex                 int8    // 用户性别: [1=男, 2=女]
	Channel             int8    // 注册渠道
	IsDisable           int8    // 是否禁用
	LoginIP             string  // 最后登录IP
	LoginTime           int64   // 最后登录时间
	IsNewUser           int8    // 是否是新注册用户
	UserMoney           float64 // 用户余额
	TotalRechargeAmount float64 // 累计充值
	EmployeeID          int64   // 员工Id
	System              int8    // 系统账号
	BdmID               int64   // 所属BDM
	BusinessType        int8    // 业务类型
	CreateTime          int64   // 创建时间
	UpdateTime          int64   // 更新时间
	DeleteTime          int64   // 删除时间
	DingID              int64   // 钉钉通知群id
	City                string  // 城市
	TelID               string  // 外呼id
	IsCallUser          int64   // 是否创建赢客云用户
	RoleID              int64   // 角色id
	CallType            int8    // 呼叫类型
	ParentID            int64   // 上级id
	Level               int64   // 等级
	Version             string  // 版本
}

func (u *User) TableName() string {
	return "sq_user"
}

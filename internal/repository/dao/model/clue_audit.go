package model

import "time"

// ClueAudit 线索审核表
type ClueAudit struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Type        int        // 审核类型 1:线索分配 2:线索转移 3:线索关闭
	Status      int        // 审核状态 1:待审核 2:审核通过 3:审核拒绝
	Remark      string     // 审核备注
	AuditorID   int64      // 审核人ID
	AuditorName string     // 审核人姓名
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// TableName 表名
func (m *ClueAudit) TableName() string {
	return "sq_clue_audit"
}

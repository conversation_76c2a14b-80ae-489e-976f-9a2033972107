package model

// PointReport 点位报告表
type PointReport struct {
	ID                   int64  // 主键
	City                 string // 城市
	Number               int    // 轮次
	WaitCount            int64  // 等待点位
	ExecCount            int64  // 执行点位
	FinishCount          int64  // 完成点位
	ListGatherCount      int64  // 列表采集数量
	DetailGatherCount    int64  // 详情采集数量
	NewShopGatherCount   int64  // 新店采集数量
	BrandGatherCount     int64  // 品牌采集数量
	BrandShopGatherCount int64  // 品牌店铺采集数量
	CreateTime           int64  // 创建时间
	UpdateTime           int64  // 更新时间
}

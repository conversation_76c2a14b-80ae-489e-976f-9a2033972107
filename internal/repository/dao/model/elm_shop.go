package model

import "time"

// ElmShop 饿了么店铺表
type ElmShop struct {
	ID                 int64     // 主键ID
	CreatedAt          time.Time // 创建时间
	UpdatedAt          time.Time // 更新时间
	Name               string    // 店铺名称
	Logo               string    // 店铺LOGO
	EmployeeID         int64     // 员工ID
	OriShopID          string    // 原始店铺ID
	ShopCode           string    // 店铺编码
	Longitude          float64   // 经度
	Latitude           float64   // 纬度
	Province           string    // 店铺所在省
	City               string    // 店铺所在城市
	District           string    // 店铺所在区或县
	Address            string    // 详细地址
	BusinessDistrict   string    // 商圈
	Type               int64     // 1elm 2美团
	Status             int64     // 0失效，1-在线，2-未报名，3-不在线
	YesterdayOrderNum  int64     // 昨日订单
	BeforeDayOrderNum  int64     // 前日订单
	MonthOrderNum      int64     // 本月订单
	LastMonthOrderNum  int64     // 上月订单
	WeekOrderNum       int64     // 本周订单
	TotalOrderNum      int64     // 总订单
	ShopLink           string    // 门店链接
	IsOpen             bool      // 是否营业
	IsValid            bool      // 是否有效
	JobPriority        bool      // 优先级
	Tags               string    // 标签
	Tel                string    // 联系电话
	MonthSale          int64     // 月售
	Score              float64   // 评分
	Evaluate           int64     // 评价数
	CrawlTime          int64     // 爬取时间
	SourceType         bool      // 来源类型
	ClaimID            int64     // 认领id
	UsedName           string    // 曾用名
	DistrictCode       string    // 行政区ID
	IntentLevel        int64     // 意向等级
	CallTime           int64     // 外呼时间
	Priority           int64     // 意向优先级
	BrandLeaderID      int64     // 品牌负责人
	BrandType          int64     // 品牌类型
	BrandID            int64     // 品牌ID
	CategoryName       string    // 分类名称
	LowerPrice         float64   // 低价均值
	TallPrice          float64   // 高价均值
	IsBrand            bool      // 是否是品牌
	IsNew              bool      // 是否是新店
	OrderSnapshot      string    // 点餐快照
	EvaluateSnapshot   string    // 评价快照
	ShopSnapshot       string    // 商家快照
	IsCpsOpen          int64     // 是否开启cps
	CategoryID         int64     // 分类id
	IsDpEffect         int64     // 是否开启大牌
	BusinessUpdateTime int64     // 商机更新时间
	BusinessType       string    // 业务类型
	CpsClaimID         int64     // cps认领员工id
	CpsEmployeeID      int64     // cps归属员工id
	ShopType           int64     // 店铺类型
	BonusFee           string    // 红包推荐
	ServiceFee         string    // 服务费推荐
	MatchName          string    // 匹配名称
	SourceID           int64     // 来源id
	LastTakeTime       time.Time // 上次爬取时间
}

package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ClueTaskDao 线索任务数据访问接口
type ClueTaskDao interface {
	// Create 创建线索任务
	Create(ctx context.Context, task model.ClueTask) (int64, error)
	// Update 更新线索任务
	Update(ctx context.Context, task model.ClueTask, fields []string) error
	// Delete 删除线索任务
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索任务
	FindByID(ctx context.Context, id int64) (model.ClueTask, error)
	// FindByClueID 根据线索ID查询线索任务列表
	FindByClueID(ctx context.Context, clueID int64) ([]model.ClueTask, error)
	// FindByAssigneeID 根据负责人ID查询线索任务列表
	FindByAssigneeID(ctx context.Context, assigneeID int64) ([]model.ClueTask, error)
	// FindByStatus 根据状态查询线索任务列表
	FindByStatus(ctx context.Context, status int64) ([]model.ClueTask, error)
	// List 分页查询线索任务列表
	List(ctx context.Context, offset, limit int) ([]model.ClueTask, error)
	// Count 统计线索任务总数
	Count(ctx context.Context) (int64, error)
}

// GORMClueTaskDao 线索任务数据访问对象
type GORMClueTaskDao struct {
	db *gorm.DB
}

// NewGORMClueTaskDao 创建线索任务数据访问实现
func NewGORMClueTaskDao(db *gorm.DB) *GORMClueTaskDao {
	return &GORMClueTaskDao{
		db: db,
	}
}

func (d *GORMClueTaskDao) Create(ctx context.Context, task model.ClueTask) (int64, error) {
	err := d.db.WithContext(ctx).Create(&task).Error
	if err != nil {
		return 0, err
	}
	return task.ID, nil
}

func (d *GORMClueTaskDao) Update(ctx context.Context, task model.ClueTask, fields []string) error {
	return d.db.WithContext(ctx).Model(model.ClueTask{}).Where("id = ?", task.ID).Select(fields).Updates(&task).Error
}

func (d *GORMClueTaskDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.ClueTask{}, id).Error
}

func (d *GORMClueTaskDao) FindByID(ctx context.Context, id int64) (model.ClueTask, error) {
	var task model.ClueTask
	err := d.db.WithContext(ctx).First(&task, id).Error
	if err != nil {
		return model.ClueTask{}, err
	}
	return task, nil
}

func (d *GORMClueTaskDao) FindByClueID(ctx context.Context, clueID int64) ([]model.ClueTask, error) {
	var tasks []model.ClueTask
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (d *GORMClueTaskDao) FindByAssigneeID(ctx context.Context, assigneeID int64) ([]model.ClueTask, error) {
	var tasks []model.ClueTask
	err := d.db.WithContext(ctx).Where("assignee_id = ?", assigneeID).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (d *GORMClueTaskDao) FindByStatus(ctx context.Context, status int64) ([]model.ClueTask, error) {
	var tasks []model.ClueTask
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (d *GORMClueTaskDao) List(ctx context.Context, offset, limit int) ([]model.ClueTask, error) {
	var tasks []model.ClueTask
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&tasks).Error
	if err != nil {
		return nil, err
	}
	return tasks, nil
}

func (d *GORMClueTaskDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(model.ClueTask{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

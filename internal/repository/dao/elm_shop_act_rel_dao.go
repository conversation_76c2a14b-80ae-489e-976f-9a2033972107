package dao

import (
	"context"
	"enter/internal/repository/dao/model"
	"time"

	"gorm.io/gorm"
)

// ElmShopActRelDao 饿了么店铺活动关系数据访问接口
type ElmShopActRelDao interface {
	// Create 创建店铺活动关系
	Create(ctx context.Context, rel model.ElmShopActRel) (int64, error)
	// Update 更新店铺活动关系
	Update(ctx context.Context, rel model.ElmShopActRel, fields []string) error
	// Get 获取店铺活动关系
	Get(ctx context.Context, id int64) (model.ElmShopActRel, error)
	// GetByShopID 根据店铺ID获取活动关系
	GetByShopID(ctx context.Context, shopID int64) ([]model.ElmShopActRel, error)
	// GetByActivityID 根据活动ID获取店铺关系列表
	GetByActivityID(ctx context.Context, activityID int64) ([]model.ElmShopActRel, error)
	// List 获取店铺活动关系列表
	List(ctx context.Context, offset, limit int) ([]model.ElmShopActRel, error)
	// Delete 删除店铺活动关系
	Delete(ctx context.Context, id int64) error
	// UpdateAuditStatus 更新审核状态
	UpdateAuditStatus(ctx context.Context, id int64, status int8) error
	// UpdatePromotionState 更新推广状态
	UpdatePromotionState(ctx context.Context, id int64, state int64) error
}

// GORMElmShopActRelDao 饿了么店铺活动关系数据访问对象
type GORMElmShopActRelDao struct {
	db *gorm.DB
}

// NewGORMElmShopActRelDao 创建ElmShopActRelDao实例
func NewGORMElmShopActRelDao(db *gorm.DB) *GORMElmShopActRelDao {
	return &GORMElmShopActRelDao{db: db}
}

func (d *GORMElmShopActRelDao) Create(ctx context.Context, rel model.ElmShopActRel) (int64, error) {
	err := d.db.WithContext(ctx).Create(rel).Error
	if err != nil {
		return 0, err
	}
	return rel.ID, nil
}

func (d *GORMElmShopActRelDao) Update(ctx context.Context, rel model.ElmShopActRel, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ElmShopActRel{}).Where("id = ?", rel.ID).Select(fields).Updates(&rel).Error
}

func (d *GORMElmShopActRelDao) Get(ctx context.Context, id int64) (model.ElmShopActRel, error) {
	var rel model.ElmShopActRel
	err := d.db.WithContext(ctx).First(&rel, id).Error
	if err != nil {
		return model.ElmShopActRel{}, err
	}
	return rel, nil
}

func (d *GORMElmShopActRelDao) GetByShopID(ctx context.Context, shopID int64) ([]model.ElmShopActRel, error) {
	var rels []model.ElmShopActRel
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopActRelDao) GetByActivityID(ctx context.Context, activityID int64) ([]model.ElmShopActRel, error) {
	var rels []model.ElmShopActRel
	err := d.db.WithContext(ctx).Where("activity_id = ?", activityID).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopActRelDao) List(ctx context.Context, offset, limit int) ([]model.ElmShopActRel, error) {
	var rels []model.ElmShopActRel
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d *GORMElmShopActRelDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ElmShopActRel{}, id).Error
}

func (d *GORMElmShopActRelDao) UpdateAuditStatus(ctx context.Context, id int64, status int8) error {
	return d.db.WithContext(ctx).Model(&model.ElmShopActRel{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"audit_status": status,
			"updated_at":   time.Now(),
		}).Error
}

func (d *GORMElmShopActRelDao) UpdatePromotionState(ctx context.Context, id int64, state int64) error {
	return d.db.WithContext(ctx).Model(&model.ElmShopActRel{}).Where("id = ?", id).
		Updates(map[string]interface{}{
			"promotion_state": state,
			"updated_at":      time.Now(),
		}).Error
}

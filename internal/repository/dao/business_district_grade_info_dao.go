package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type BusinessDistrictGradeInfoDao interface {
	Create(ctx context.Context, info model.BusinessDistrictGradeInfo) (int64, error)
	Update(ctx context.Context, info model.BusinessDistrictGradeInfo, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.BusinessDistrictGradeInfo, error)
	FindByGradeID(ctx context.Context, gradeID int64) (model.BusinessDistrictGradeInfo, error)
	FindByBusinessDistrict(ctx context.Context, businessDistrict string) (model.BusinessDistrictGradeInfo, error)
	List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictGradeInfo, error)
	Count(ctx context.Context) (int64, error)
}

type GORMBusinessDistrictGradeInfoDao struct {
	db *gorm.DB
}

func NewGORMBusinessDistrictGradeInfoDao(db *gorm.DB) *GORMBusinessDistrictGradeInfoDao {
	return &GORMBusinessDistrictGradeInfoDao{
		db: db,
	}
}

func (d *GORMBusinessDistrictGradeInfoDao) Create(ctx context.Context, info model.BusinessDistrictGradeInfo) (int64, error) {
	err := d.db.WithContext(ctx).Create(info).Error
	if err != nil {
		return 0, err
	}
	return info.ID, nil
}

func (d *GORMBusinessDistrictGradeInfoDao) Update(ctx context.Context, info model.BusinessDistrictGradeInfo, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.BusinessDistrictGradeInfo{}).Where("id = ?", info.ID).Select(fields).Updates(&info).Error
}

func (d *GORMBusinessDistrictGradeInfoDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.BusinessDistrictGradeInfo{}, id).Error
}

func (d *GORMBusinessDistrictGradeInfoDao) FindByID(ctx context.Context, id int64) (model.BusinessDistrictGradeInfo, error) {
	var info model.BusinessDistrictGradeInfo
	err := d.db.WithContext(ctx).First(&info, id).Error
	if err != nil {
		return model.BusinessDistrictGradeInfo{}, err
	}
	return info, nil
}

func (d *GORMBusinessDistrictGradeInfoDao) FindByGradeID(ctx context.Context, gradeID int64) (model.BusinessDistrictGradeInfo, error) {
	var info model.BusinessDistrictGradeInfo
	err := d.db.WithContext(ctx).Where("grade_id = ?", gradeID).First(&info).Error
	if err != nil {
		return model.BusinessDistrictGradeInfo{}, err
	}
	return info, nil
}

func (d *GORMBusinessDistrictGradeInfoDao) FindByBusinessDistrict(ctx context.Context, businessDistrict string) (model.BusinessDistrictGradeInfo, error) {
	var info model.BusinessDistrictGradeInfo
	err := d.db.WithContext(ctx).Where("business_district = ?", businessDistrict).First(&info).Error
	if err != nil {
		return model.BusinessDistrictGradeInfo{}, err
	}
	return info, nil
}

func (d *GORMBusinessDistrictGradeInfoDao) List(ctx context.Context, offset, limit int) ([]model.BusinessDistrictGradeInfo, error) {
	var infos []model.BusinessDistrictGradeInfo
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&infos).Error
	if err != nil {
		return nil, err
	}
	return infos, nil
}

func (d *GORMBusinessDistrictGradeInfoDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.BusinessDistrictGradeInfo{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"
	"time"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// OperateSettlementDao 运营结算数据访问接口
type OperateSettlementDao interface {
	// Create 创建运营结算
	Create(ctx context.Context, settlement model.OperateSettlement) (int64, error)
	// Update 更新运营结算
	Update(ctx context.Context, settlement model.OperateSettlement, fields []string) error
	// Delete 删除运营结算
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询运营结算
	FindByID(ctx context.Context, id int64) (model.OperateSettlement, error)
	// FindByShopID 根据店铺ID查询运营结算列表
	FindByShopID(ctx context.Context, shopID string) ([]model.OperateSettlement, error)
	// FindByOperatorID 根据运营人员ID查询运营结算列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateSettlement, error)
	// FindByStatus 根据结算状态查询运营结算列表
	FindByStatus(ctx context.Context, status int) ([]model.OperateSettlement, error)
	// FindByType 根据结算类型查询运营结算列表
	FindByType(ctx context.Context, settlementType int) ([]model.OperateSettlement, error)
	// FindByDateRange 根据时间范围查询运营结算列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateSettlement, error)
	// List 查询运营结算列表
	List(ctx context.Context, offset, limit int) ([]model.OperateSettlement, error)
	// Count 统计运营结算总数
	Count(ctx context.Context) (int64, error)
}

type GORMOperateSettlementDao struct {
	db *gorm.DB
}

// NewGORMOperateSettlementDao 创建运营结算数据访问实现
func NewGORMOperateSettlementDao(db *gorm.DB) *GORMOperateSettlementDao {
	return &GORMOperateSettlementDao{
		db: db,
	}
}

func (d *GORMOperateSettlementDao) Create(ctx context.Context, settlement model.OperateSettlement) (int64, error) {
	err := d.db.WithContext(ctx).Create(settlement).Error
	if err != nil {
		return 0, err
	}
	return settlement.ID, nil
}

func (d *GORMOperateSettlementDao) Update(ctx context.Context, settlement model.OperateSettlement, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.OperateSettlement{}).Where("id = ?", settlement.ID).Select(fields).Updates(&settlement).Error
}

func (d *GORMOperateSettlementDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.OperateSettlement{}, id).Error
}

func (d *GORMOperateSettlementDao) FindByID(ctx context.Context, id int64) (model.OperateSettlement, error) {
	var settlement model.OperateSettlement
	err := d.db.WithContext(ctx).First(&settlement, id).Error
	if err != nil {
		return model.OperateSettlement{}, err
	}
	return settlement, nil
}

func (d *GORMOperateSettlementDao) FindByShopID(ctx context.Context, shopID string) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) FindByOperatorID(ctx context.Context, operatorID int64) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Where("operator_id = ?", operatorID).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) FindByStatus(ctx context.Context, status int) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Where("settlement_status = ?", status).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) FindByType(ctx context.Context, settlementType int) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Where("settlement_type = ?", settlementType).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Where("created_at BETWEEN ? AND ?", startTime, endTime).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) List(ctx context.Context, offset, limit int) ([]model.OperateSettlement, error) {
	var settlements []model.OperateSettlement
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&settlements).Error
	if err != nil {
		return nil, err
	}
	return settlements, nil
}

func (d *GORMOperateSettlementDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.OperateSettlement{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

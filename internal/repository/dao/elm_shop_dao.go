package dao

import (
	"context"
	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ElmShopDao 饿了么店铺数据访问接口
type ElmShopDao interface {
	// 店铺变更记录相关
	CreateShopChange(ctx context.Context, change model.ElmShopChange) (int64, error)
	GetShopChangeByID(ctx context.Context, id int64) (model.ElmShopChange, error)
	ListShopChangesByShopID(ctx context.Context, shopID int64) ([]model.ElmShopChange, error)

	// 店铺员工关联相关
	CreateShopEmployeeRel(ctx context.Context, rel *model.ElmShopCodeEmployeeRel) (int64, error)
	GetShopEmployeeRelByID(ctx context.Context, id int64) (model.ElmShopCodeEmployeeRel, error)
	ListShopEmployeeRelsByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShopCodeEmployeeRel, error)
	ListShopEmployeeRelsByShopCode(ctx context.Context, shopCode string) ([]model.ElmShopCodeEmployeeRel, error)

	// 店铺相关
	CreateShop(ctx context.Context, shop *model.ElmShop) (int64, error)
	GetShopByID(ctx context.Context, id int64) (model.ElmShop, error)
	GetShopByCode(ctx context.Context, code string) (model.ElmShop, error)
	ListShopsByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShop, error)
	UpdateShop(ctx context.Context, shop model.ElmShop, fields []string) error
	DeleteShop(ctx context.Context, id int64) error
	// List 分页查询店铺列表
	List(ctx context.Context, offset, limit int) ([]model.ElmShop, error)
	// Count 统计店铺总数
	Count(ctx context.Context) (int64, error)
}

// GORMElmShopDao 饿了么店铺数据访问对象
type GORMElmShopDao struct {
	db *gorm.DB
}

// NewGORMElmShopDao 创建饿了么店铺数据访问对象
func NewGORMElmShopDao(db *gorm.DB) *GORMElmShopDao {
	return &GORMElmShopDao{db: db}
}

// CreateShopChange 店铺变更记录实现
func (d GORMElmShopDao) CreateShopChange(ctx context.Context, change model.ElmShopChange) (int64, error) {
	err := d.db.WithContext(ctx).Create(change).Error
	if err != nil {
		return 0, err
	}
	return change.ID, nil
}

func (d GORMElmShopDao) GetShopChangeByID(ctx context.Context, id int64) (model.ElmShopChange, error) {
	var change model.ElmShopChange
	err := d.db.WithContext(ctx).First(&change, id).Error
	if err != nil {
		return model.ElmShopChange{}, err
	}
	return change, nil
}

func (d GORMElmShopDao) ListShopChangesByShopID(ctx context.Context, shopID int64) ([]model.ElmShopChange, error) {
	var changes []model.ElmShopChange
	err := d.db.WithContext(ctx).Where("elm_shop_id = ?", shopID).Find(&changes).Error
	if err != nil {
		return nil, err
	}
	return changes, nil
}

// 店铺员工关联实现
func (d GORMElmShopDao) CreateShopEmployeeRel(ctx context.Context, rel *model.ElmShopCodeEmployeeRel) (int64, error) {
	err := d.db.WithContext(ctx).Create(rel).Error
	if err != nil {
		return 0, err
	}
	return rel.ID, nil
}

func (d GORMElmShopDao) GetShopEmployeeRelByID(ctx context.Context, id int64) (model.ElmShopCodeEmployeeRel, error) {
	var rel model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).First(&rel, id).Error
	if err != nil {
		return model.ElmShopCodeEmployeeRel{}, err
	}
	return rel, nil
}

func (d GORMElmShopDao) ListShopEmployeeRelsByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShopCodeEmployeeRel, error) {
	var rels []model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

func (d GORMElmShopDao) ListShopEmployeeRelsByShopCode(ctx context.Context, shopCode string) ([]model.ElmShopCodeEmployeeRel, error) {
	var rels []model.ElmShopCodeEmployeeRel
	err := d.db.WithContext(ctx).Where("shop_code = ?", shopCode).Find(&rels).Error
	if err != nil {
		return nil, err
	}
	return rels, nil
}

// 店铺实现
func (d GORMElmShopDao) CreateShop(ctx context.Context, shop *model.ElmShop) (int64, error) {
	err := d.db.WithContext(ctx).Create(shop).Error
	if err != nil {
		return 0, err
	}
	return shop.ID, nil
}

func (d GORMElmShopDao) GetShopByID(ctx context.Context, id int64) (model.ElmShop, error) {
	var shop model.ElmShop
	err := d.db.WithContext(ctx).First(&shop, id).Error
	if err != nil {
		return model.ElmShop{}, err
	}
	return shop, nil
}

func (d GORMElmShopDao) GetShopByCode(ctx context.Context, code string) (model.ElmShop, error) {
	var shop model.ElmShop
	err := d.db.WithContext(ctx).Where("shop_code = ?", code).First(&shop).Error
	if err != nil {
		return model.ElmShop{}, err
	}
	return shop, nil
}

func (d GORMElmShopDao) ListShopsByEmployeeID(ctx context.Context, employeeID int64) ([]model.ElmShop, error) {
	var shops []model.ElmShop
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d GORMElmShopDao) UpdateShop(ctx context.Context, shop model.ElmShop, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ElmShop{}).Where("id = ?", shop.ID).Select(fields).Updates(&shop).Error
}

func (d GORMElmShopDao) DeleteShop(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ElmShop{}, id).Error
}

func (d *GORMElmShopDao) List(ctx context.Context, offset, limit int) ([]model.ElmShop, error) {
	var shops []model.ElmShop
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d *GORMElmShopDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ElmShop{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ClueAuditDao 线索审核数据访问接口
type ClueAuditDao interface {
	// Create 创建线索审核
	Create(ctx context.Context, audit model.ClueAudit) (int64, error)
	// Update 更新线索审核
	Update(ctx context.Context, audit model.ClueAudit, fields []string) error
	// Delete 删除线索审核
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索审核
	FindByID(ctx context.Context, id int64) (model.ClueAudit, error)
	// FindByClueID 根据线索ID查询线索审核列表
	FindByClueID(ctx context.Context, clueID int64) ([]model.ClueAudit, error)
	// FindByType 根据审核类型查询线索审核列表
	FindByType(ctx context.Context, auditType int) ([]model.ClueAudit, error)
	// FindByStatus 根据审核状态查询线索审核列表
	FindByStatus(ctx context.Context, status int) ([]model.ClueAudit, error)
	// List 分页查询线索审核列表
	List(ctx context.Context, offset, limit int) ([]model.ClueAudit, error)
	// Count 统计线索审核总数
	Count(ctx context.Context) (int64, error)
}

type GORMClueAuditDao struct {
	db *gorm.DB
}

// NewGORMClueAuditDao 创建线索审核数据访问实现
func NewGORMClueAuditDao(db *gorm.DB) *GORMClueAuditDao {
	return &GORMClueAuditDao{
		db: db,
	}
}

func (d *GORMClueAuditDao) Create(ctx context.Context, audit model.ClueAudit) (int64, error) {
	err := d.db.WithContext(ctx).Create(audit).Error
	if err != nil {
		return 0, err
	}
	return audit.ID, nil
}

func (d *GORMClueAuditDao) Update(ctx context.Context, audit model.ClueAudit, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ClueAudit{}).Where("id = ?", audit.ID).Select(fields).Updates(&audit).Error
}

func (d *GORMClueAuditDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ClueAudit{}, id).Error
}

func (d *GORMClueAuditDao) FindByID(ctx context.Context, id int64) (model.ClueAudit, error) {
	var audit model.ClueAudit
	err := d.db.WithContext(ctx).First(&audit, id).Error
	if err != nil {
		return model.ClueAudit{}, err
	}
	return audit, nil
}

func (d *GORMClueAuditDao) FindByClueID(ctx context.Context, clueID int64) ([]model.ClueAudit, error) {
	var audits []model.ClueAudit
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&audits).Error
	if err != nil {
		return nil, err
	}
	return audits, nil
}

func (d *GORMClueAuditDao) FindByType(ctx context.Context, auditType int) ([]model.ClueAudit, error) {
	var audits []model.ClueAudit
	err := d.db.WithContext(ctx).Where("type = ?", auditType).Find(&audits).Error
	if err != nil {
		return nil, err
	}
	return audits, nil
}

func (d *GORMClueAuditDao) FindByStatus(ctx context.Context, status int) ([]model.ClueAudit, error) {
	var audits []model.ClueAudit
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&audits).Error
	if err != nil {
		return nil, err
	}
	return audits, nil
}

func (d *GORMClueAuditDao) List(ctx context.Context, offset, limit int) ([]model.ClueAudit, error) {
	var audits []model.ClueAudit
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&audits).Error
	if err != nil {
		return nil, err
	}
	return audits, nil
}

func (d *GORMClueAuditDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ClueAudit{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

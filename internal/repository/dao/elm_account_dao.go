package dao

import (
	"context"
	"enter/internal/repository/dao/model"

	"gorm.io/gorm"
)

// ElmAccountDao defines the interface for elm account data access
type ElmAccountDao interface {
	Create(ctx context.Context, account model.ElmAccount) (int64, error)
	Update(ctx context.Context, account model.ElmAccount, fields []string) error
	Get(ctx context.Context, id int64) (model.ElmAccount, error)
	GetByUsername(ctx context.Context, username string) (model.ElmAccount, error)
	List(ctx context.Context, offset, limit int) ([]model.ElmAccount, error)
	Delete(ctx context.Context, id int64) error
}

// GORMElmAccountDao implements ElmAccountDao interface using GORM
type GORMElmAccountDao struct {
	db *gorm.DB
}

// NewGORMElmAccountDao creates a new instance of GORMElmAccountDao
func NewGORMElmAccountDao(db *gorm.DB) *GORMElmAccountDao {
	return &GORMElmAccountDao{db: db}
}

func (d *GORMElmAccountDao) Create(ctx context.Context, account model.ElmAccount) (int64, error) {
	err := d.db.WithContext(ctx).Create(&account).Error
	if err != nil {
		return 0, err
	}
	return account.ID, nil
}

func (d *GORMElmAccountDao) Update(ctx context.Context, account model.ElmAccount, fields []string) error {
	updates := make(map[string]interface{})
	for _, field := range fields {
		switch field {
		case "username":
			updates["username"] = account.Username
		case "password":
			updates["password"] = account.Password
		case "city":
			updates["city"] = account.City
		case "cookie":
			updates["cookie"] = account.Cookie
		case "app_id":
			updates["app_id"] = account.AppID
		case "open_crawl":
			updates["open_crawl"] = account.OpenCrawl
		case "updated_at":
			updates["updated_at"] = account.UpdatedAt
		}
	}
	if len(updates) == 0 {
		return nil
	}
	return d.db.WithContext(ctx).Model(&model.ElmAccount{}).Where("id = ?", account.ID).Updates(updates).Error
}

func (d *GORMElmAccountDao) Get(ctx context.Context, id int64) (model.ElmAccount, error) {
	var account model.ElmAccount
	err := d.db.WithContext(ctx).First(&account, id).Error
	if err != nil {
		return model.ElmAccount{}, err
	}
	return account, nil
}

func (d *GORMElmAccountDao) GetByUsername(ctx context.Context, username string) (model.ElmAccount, error) {
	var account model.ElmAccount
	err := d.db.WithContext(ctx).Where("username = ?", username).First(&account).Error
	if err != nil {
		return model.ElmAccount{}, err
	}
	return account, nil
}

func (d *GORMElmAccountDao) List(ctx context.Context, offset, limit int) ([]model.ElmAccount, error) {
	var accounts []model.ElmAccount
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&accounts).Error
	if err != nil {
		return nil, err
	}
	return accounts, nil
}

func (d *GORMElmAccountDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ElmAccount{}, id).Error
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type TrackRecordDao interface {
	Create(ctx context.Context, record model.TrackRecord) (int64, error)
	Update(ctx context.Context, record model.TrackRecord, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.TrackRecord, error)
	ListByUserID(ctx context.Context, userID int64) ([]model.TrackRecord, error)
	ListByClueID(ctx context.Context, clueID int64) ([]model.TrackRecord, error)
	ListByStatus(ctx context.Context, status int) ([]model.TrackRecord, error)
	ListByPlatform(ctx context.Context, platform int) ([]model.TrackRecord, error)
	List(ctx context.Context, offset, limit int) ([]model.TrackRecord, error)
	Count(ctx context.Context) (int64, error)
}

type GORMTrackRecordDao struct {
	db *gorm.DB
}

func NewGORMTrackRecordDao(db *gorm.DB) *GORMTrackRecordDao {
	return &GORMTrackRecordDao{
		db: db,
	}
}

func (d *GORMTrackRecordDao) Create(ctx context.Context, record model.TrackRecord) (int64, error) {
	err := d.db.WithContext(ctx).Create(record).Error
	if err != nil {
		return 0, err
	}
	return record.ID, nil
}

func (d *GORMTrackRecordDao) Update(ctx context.Context, record model.TrackRecord, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.TrackRecord{}).Where("id = ?", record.ID).Select(fields).Updates(&record).Error
}

func (d *GORMTrackRecordDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.TrackRecord{}, id).Error
}

func (d *GORMTrackRecordDao) FindByID(ctx context.Context, id int64) (model.TrackRecord, error) {
	var record model.TrackRecord
	err := d.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return model.TrackRecord{}, err
	}
	return record, nil
}

func (d *GORMTrackRecordDao) ListByUserID(ctx context.Context, userID int64) ([]model.TrackRecord, error) {
	var records []model.TrackRecord
	err := d.db.WithContext(ctx).Where("user_id = ?", userID).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (d *GORMTrackRecordDao) ListByClueID(ctx context.Context, clueID int64) ([]model.TrackRecord, error) {
	var records []model.TrackRecord
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (d *GORMTrackRecordDao) ListByStatus(ctx context.Context, status int) ([]model.TrackRecord, error) {
	var records []model.TrackRecord
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (d *GORMTrackRecordDao) ListByPlatform(ctx context.Context, platform int) ([]model.TrackRecord, error) {
	var records []model.TrackRecord
	err := d.db.WithContext(ctx).Where("platform = ?", platform).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (d *GORMTrackRecordDao) List(ctx context.Context, offset, limit int) ([]model.TrackRecord, error) {
	var records []model.TrackRecord
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

func (d *GORMTrackRecordDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.TrackRecord{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// PointReportDao 积分报告数据访问接口
type PointReportDao interface {
	// Create 创建积分报告
	Create(ctx context.Context, report model.PointReport) (int64, error)
	// Update 更新积分报告
	Update(ctx context.Context, report model.PointReport, fields []string) error
	// Delete 删除积分报告
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询积分报告
	FindByID(ctx context.Context, id int64) (model.PointReport, error)
	// ListByCity 根据城市查询积分报告列表
	ListByCity(ctx context.Context, city string) ([]model.PointReport, error)
	// ListByNumber 根据数量查询积分报告列表
	ListByNumber(ctx context.Context, number int) ([]model.PointReport, error)
	// List 查询积分报告列表
	List(ctx context.Context, offset, limit int) ([]model.PointReport, error)
	// Count 统计积分报告总数
	Count(ctx context.Context) (int64, error)
}

// GORMPointReportDao 积分报告数据访问对象
type GORMPointReportDao struct {
	db *gorm.DB
}

// NewGORMPointReportDao 创建积分报告数据访问实现
func NewGORMPointReportDao(db *gorm.DB) GORMPointReportDao {
	return GORMPointReportDao{
		db: db,
	}
}

func (d *GORMPointReportDao) Create(ctx context.Context, report model.PointReport) (int64, error) {
	err := d.db.WithContext(ctx).Create(&report).Error
	if err != nil {
		return 0, err
	}
	return report.ID, nil
}

func (d *GORMPointReportDao) Update(ctx context.Context, report model.PointReport, fields []string) error {
	return d.db.WithContext(ctx).Model(model.PointReport{}).Where("id = ?", report.ID).Select(fields).Updates(&report).Error
}

func (d *GORMPointReportDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(model.PointReport{}, id).Error
}

func (d *GORMPointReportDao) FindByID(ctx context.Context, id int64) (model.PointReport, error) {
	var report model.PointReport
	err := d.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return model.PointReport{}, err
	}
	return report, nil
}

func (d *GORMPointReportDao) ListByCity(ctx context.Context, city string) ([]model.PointReport, error) {
	var reports []model.PointReport
	err := d.db.WithContext(ctx).Where("city = ?", city).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMPointReportDao) ListByNumber(ctx context.Context, number int) ([]model.PointReport, error) {
	var reports []model.PointReport
	err := d.db.WithContext(ctx).Where("number = ?", number).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMPointReportDao) List(ctx context.Context, offset, limit int) ([]model.PointReport, error) {
	var reports []model.PointReport
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMPointReportDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.PointReport{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type CircleShopDao interface {
	Create(ctx context.Context, shop model.CircleShop) (int64, error)
	Update(ctx context.Context, shop model.CircleShop, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.CircleShop, error)
	FindByName(ctx context.Context, name string) (model.CircleShop, error)
	List(ctx context.Context, offset, limit int) ([]model.CircleShop, error)
	ListByCircleID(ctx context.Context, circleID int64) ([]model.CircleShop, error)
	ListByBrandID(ctx context.Context, brandID int64) ([]model.CircleShop, error)
	Count(ctx context.Context) (int64, error)
	ListUnmatchedShops(ctx context.Context, offset, limit int) ([]model.CircleShop, error)
	BatchUpdateBrandID(ctx context.Context, shopIDs []int64, brandID int64) error
}

type GORMCircleShopDao struct {
	db *gorm.DB
}

func NewGORMCircleShopDao(db *gorm.DB) *GORMCircleShopDao {
	return &GORMCircleShopDao{
		db: db,
	}
}

func (d *GORMCircleShopDao) Create(ctx context.Context, shop model.CircleShop) (int64, error) {
	err := d.db.WithContext(ctx).Create(shop).Error
	if err != nil {
		return 0, err
	}
	return shop.ID, nil
}

func (d *GORMCircleShopDao) Update(ctx context.Context, shop model.CircleShop, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.CircleShop{}).Where("id = ?", shop.ID).Select(fields).Updates(&shop).Error
}

func (d *GORMCircleShopDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.CircleShop{}, id).Error
}

func (d *GORMCircleShopDao) FindByID(ctx context.Context, id int64) (model.CircleShop, error) {
	var shop model.CircleShop
	err := d.db.WithContext(ctx).First(&shop, id).Error
	if err != nil {
		return model.CircleShop{}, err
	}
	return shop, nil
}

func (d *GORMCircleShopDao) FindByName(ctx context.Context, name string) (model.CircleShop, error) {
	var shop model.CircleShop
	err := d.db.WithContext(ctx).Where("name = ?", name).First(&shop).Error
	if err != nil {
		return model.CircleShop{}, err
	}
	return shop, nil
}

func (d *GORMCircleShopDao) List(ctx context.Context, offset, limit int) ([]model.CircleShop, error) {
	var shops []model.CircleShop
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d *GORMCircleShopDao) ListByCircleID(ctx context.Context, circleID int64) ([]model.CircleShop, error) {
	var shops []model.CircleShop
	err := d.db.WithContext(ctx).Where("circle_id = ?", circleID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d *GORMCircleShopDao) ListByBrandID(ctx context.Context, brandID int64) ([]model.CircleShop, error) {
	var shops []model.CircleShop
	err := d.db.WithContext(ctx).Where("brand_id = ?", brandID).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d *GORMCircleShopDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.CircleShop{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

func (d *GORMCircleShopDao) ListUnmatchedShops(ctx context.Context, offset, limit int) ([]model.CircleShop, error) {
	var shops []model.CircleShop
	err := d.db.WithContext(ctx).Where("is_brand = 1").Where("brand_id = 0").Order("id desc").Offset(offset).Limit(limit).Find(&shops).Error
	if err != nil {
		return nil, err
	}
	return shops, nil
}

func (d *GORMCircleShopDao) BatchUpdateBrandID(ctx context.Context, shopIDs []int64, brandID int64) error {
	return d.db.WithContext(ctx).Model(&model.CircleShop{}).Where("id IN ?", shopIDs).Update("brand_id", brandID).Error
}

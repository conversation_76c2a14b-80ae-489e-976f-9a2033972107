package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// ElmShopChangeDao 饿了么店铺变更记录数据访问接口
type ElmShopChangeDao interface {
	// Create 创建店铺变更记录
	Create(ctx context.Context, change model.ElmShopChange) (int64, error)
	// Update 更新店铺变更记录
	Update(ctx context.Context, change model.ElmShopChange, fields []string) error
	// Delete 删除店铺变更记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询店铺变更记录
	FindByID(ctx context.Context, id int64) (model.ElmShopChange, error)
	// FindByShopID 根据店铺ID查询店铺变更记录列表
	FindByShopID(ctx context.Context, shopID int64) ([]model.ElmShopChange, error)
	// List 查询店铺变更记录列表
	List(ctx context.Context, offset, limit int) ([]model.ElmShopChange, error)
	// Count 统计店铺变更记录总数
	Count(ctx context.Context) (int64, error)
}

type GORMElmShopChangeDao struct {
	db *gorm.DB
}

// NewGORMElmShopChangeDao 创建店铺变更记录数据访问实例
func NewGORMElmShopChangeDao(db *gorm.DB) *GORMElmShopChangeDao {
	return &GORMElmShopChangeDao{
		db: db,
	}
}

func (d *GORMElmShopChangeDao) Create(ctx context.Context, change model.ElmShopChange) (int64, error) {
	err := d.db.WithContext(ctx).Create(change).Error
	if err != nil {
		return 0, err
	}
	return change.ID, nil
}

func (d *GORMElmShopChangeDao) Update(ctx context.Context, change model.ElmShopChange, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ElmShopChange{}).Where("id = ?", change.ID).Select(fields).Updates(&change).Error
}

func (d *GORMElmShopChangeDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ElmShopChange{}, id).Error
}

func (d *GORMElmShopChangeDao) FindByID(ctx context.Context, id int64) (model.ElmShopChange, error) {
	var change model.ElmShopChange
	err := d.db.WithContext(ctx).First(&change, id).Error
	if err != nil {
		return model.ElmShopChange{}, err
	}
	return change, nil
}

func (d *GORMElmShopChangeDao) FindByShopID(ctx context.Context, shopID int64) ([]model.ElmShopChange, error) {
	var changes []model.ElmShopChange
	err := d.db.WithContext(ctx).Where("shop_id = ?", shopID).Find(&changes).Error
	if err != nil {
		return nil, err
	}
	return changes, nil
}

func (d *GORMElmShopChangeDao) List(ctx context.Context, offset, limit int) ([]model.ElmShopChange, error) {
	var changes []model.ElmShopChange
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&changes).Error
	if err != nil {
		return nil, err
	}
	return changes, nil
}

func (d *GORMElmShopChangeDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ElmShopChange{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

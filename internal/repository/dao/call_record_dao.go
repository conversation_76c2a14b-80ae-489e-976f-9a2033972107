package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// CallRecordDao 通话记录数据访问接口
type CallRecordDao interface {
	// Create 创建通话记录
	Create(ctx context.Context, record model.CallRecord) (int64, error)
	// Update 更新通话记录
	Update(ctx context.Context, record model.CallRecord, fields []string) error
	// Delete 删除通话记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询通话记录
	FindByID(ctx context.Context, id int64) (model.CallRecord, error)
	// FindByClueID 根据线索ID查询通话记录列表
	FindByClueID(ctx context.Context, clueID int64) ([]model.CallRecord, error)
	// FindByCallerID 根据主叫人ID查询通话记录列表
	FindByCallerID(ctx context.Context, callerID int64) ([]model.CallRecord, error)
	// FindByStatus 根据通话状态查询通话记录列表
	FindByStatus(ctx context.Context, status int) ([]model.CallRecord, error)
	// List 查询通话记录列表
	List(ctx context.Context, offset, limit int) ([]model.CallRecord, error)
	// Count 查询通话记录总数
	Count(ctx context.Context) (int64, error)
	// FindByOrderSn 根据订单编号获取通话记录
	FindByOrderSn(ctx context.Context, orderSn string) (model.CallRecord, error)
	// FindByOrderId 根据外部订单编号获取通话记录
	FindByOrderId(ctx context.Context, orderId string) (model.CallRecord, error)
}

// GORMCallRecordDao 通话记录数据访问实现
type GORMCallRecordDao struct {
	db *gorm.DB
}

// NewGORMCallRecordDao 创建通话记录数据访问实例
func NewGORMCallRecordDao(db *gorm.DB) *GORMCallRecordDao {
	return &GORMCallRecordDao{
		db: db,
	}
}

// Create 创建通话记录
func (d *GORMCallRecordDao) Create(ctx context.Context, record model.CallRecord) (int64, error) {
	err := d.db.WithContext(ctx).Create(&record).Error
	if err != nil {
		return 0, err
	}
	return record.ID, nil
}

// Update 更新通话记录
func (d *GORMCallRecordDao) Update(ctx context.Context, record model.CallRecord, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.CallRecord{}).Where("id = ?", record.ID).Select(fields).Updates(&record).Error
}

// Delete 删除通话记录
func (d *GORMCallRecordDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.CallRecord{}, id).Error
}

// FindByID 根据ID查询通话记录
func (d *GORMCallRecordDao) FindByID(ctx context.Context, id int64) (model.CallRecord, error) {
	var record model.CallRecord
	err := d.db.WithContext(ctx).First(&record, id).Error
	if err != nil {
		return model.CallRecord{}, err
	}
	return record, nil
}

// FindByClueID 根据线索ID查询通话记录列表
func (d *GORMCallRecordDao) FindByClueID(ctx context.Context, clueID int64) ([]model.CallRecord, error) {
	var records []model.CallRecord
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// FindByCallerID 根据主叫人ID查询通话记录列表
func (d *GORMCallRecordDao) FindByCallerID(ctx context.Context, callerID int64) ([]model.CallRecord, error) {
	var records []model.CallRecord
	err := d.db.WithContext(ctx).Where("caller_id = ?", callerID).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// FindByStatus 根据通话状态查询通话记录列表
func (d *GORMCallRecordDao) FindByStatus(ctx context.Context, status int) ([]model.CallRecord, error) {
	var records []model.CallRecord
	err := d.db.WithContext(ctx).Where("status = ?", status).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// List 查询通话记录列表
func (d *GORMCallRecordDao) List(ctx context.Context, offset, limit int) ([]model.CallRecord, error) {
	var records []model.CallRecord
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&records).Error
	if err != nil {
		return nil, err
	}
	return records, nil
}

// Count 查询通话记录总数
func (d *GORMCallRecordDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.CallRecord{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

// FindByOrderSn 根据订单编号获取通话记录
func (d *GORMCallRecordDao) FindByOrderSn(ctx context.Context, orderSn string) (model.CallRecord, error) {
	var record model.CallRecord
	err := d.db.WithContext(ctx).Where("order_sn = ?", orderSn).First(&record).Error
	if err != nil {
		return model.CallRecord{}, err
	}
	return record, nil
}

// FindByOrderId 根据外部订单编号获取通话记录
func (d *GORMCallRecordDao) FindByOrderId(ctx context.Context, orderId string) (model.CallRecord, error) {
	var record model.CallRecord
	err := d.db.WithContext(ctx).Where("order_id = ?", orderId).First(&record).Error
	if err != nil {
		return model.CallRecord{}, err
	}
	return record, nil
}

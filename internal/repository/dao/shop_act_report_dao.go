package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

type ShopActReportDao interface {
	Create(ctx context.Context, report model.ShopActReport) (int64, error)
	Update(ctx context.Context, report model.ShopActReport, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (model.ShopActReport, error)
	FindByShopCode(ctx context.Context, shopCode string) (model.ShopActReport, error)
	List(ctx context.Context, offset, limit int) ([]model.ShopActReport, error)
	ListByEmployeeID(ctx context.Context, employeeID int) ([]model.ShopActReport, error)
	ListByBrandName(ctx context.Context, brandName string) ([]model.ShopActReport, error)
	ListByActID(ctx context.Context, actID int64) ([]model.ShopActReport, error)
	Count(ctx context.Context) (int64, error)
}

type GORMShopActReportDao struct {
	db *gorm.DB
}

func NewGORMShopActReportDao(db *gorm.DB) *GORMShopActReportDao {
	return &GORMShopActReportDao{
		db: db,
	}
}

func (d *GORMShopActReportDao) Create(ctx context.Context, report model.ShopActReport) (int64, error) {
	err := d.db.WithContext(ctx).Create(report).Error
	if err != nil {
		return 0, err
	}
	return report.ID, nil
}

func (d *GORMShopActReportDao) Update(ctx context.Context, report model.ShopActReport, fields []string) error {
	return d.db.WithContext(ctx).Model(model.ShopActReport{}).Where("id = ?", report.ID).Select(fields).Updates(&report).Error
}

func (d *GORMShopActReportDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ShopActReport{}, id).Error
}

func (d *GORMShopActReportDao) FindByID(ctx context.Context, id int64) (model.ShopActReport, error) {
	var report model.ShopActReport
	err := d.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return model.ShopActReport{}, err
	}
	return report, nil
}

func (d *GORMShopActReportDao) FindByShopCode(ctx context.Context, shopCode string) (model.ShopActReport, error) {
	var report model.ShopActReport
	err := d.db.WithContext(ctx).Where("shop_code = ?", shopCode).First(&report).Error
	if err != nil {
		return model.ShopActReport{}, err
	}
	return report, nil
}

func (d *GORMShopActReportDao) List(ctx context.Context, offset, limit int) ([]model.ShopActReport, error) {
	var reports []model.ShopActReport
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMShopActReportDao) ListByEmployeeID(ctx context.Context, employeeID int) ([]model.ShopActReport, error) {
	var reports []model.ShopActReport
	err := d.db.WithContext(ctx).Where("employee_id = ?", employeeID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMShopActReportDao) ListByBrandName(ctx context.Context, brandName string) ([]model.ShopActReport, error) {
	var reports []model.ShopActReport
	err := d.db.WithContext(ctx).Where("brand_name = ?", brandName).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMShopActReportDao) ListByActID(ctx context.Context, actID int64) ([]model.ShopActReport, error) {
	var reports []model.ShopActReport
	err := d.db.WithContext(ctx).Where("act_id = ?", actID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMShopActReportDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ShopActReport{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

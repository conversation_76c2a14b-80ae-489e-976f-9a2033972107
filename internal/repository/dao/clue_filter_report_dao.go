package dao

import (
	"context"

	"gorm.io/gorm"

	"enter/internal/repository/dao/model"
)

// ClueFilterReportDao 线索过滤报表数据访问接口
type ClueFilterReportDao interface {
	// Create 创建线索过滤报表
	Create(ctx context.Context, report model.ClueFilterReport) (int64, error)
	// Update 更新线索过滤报表
	Update(ctx context.Context, report model.ClueFilterReport, fields []string) error
	// Delete 删除线索过滤报表
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索过滤报表
	FindByID(ctx context.Context, id int64) (model.ClueFilterReport, error)
	// FindByClueID 根据线索ID查询线索过滤报表
	FindByClueID(ctx context.Context, clueID int64) (model.ClueFilterReport, error)
	// FindByOperatorID 根据操作人ID查询线索过滤报表列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]model.ClueFilterReport, error)
	// FindByFilterType 根据过滤类型查询线索过滤报表列表
	FindByFilterType(ctx context.Context, filterType int) ([]model.ClueFilterReport, error)
	// List 查询线索过滤报表列表
	List(ctx context.Context, offset, limit int) ([]model.ClueFilterReport, error)
	// Count 统计线索过滤报表总数
	Count(ctx context.Context) (int64, error)
}

type GORMClueFilterReportDao struct {
	db *gorm.DB
}

// NewGORMClueFilterReportDao 创建线索过滤报表数据访问实例
func NewGORMClueFilterReportDao(db *gorm.DB) *GORMClueFilterReportDao {
	return &GORMClueFilterReportDao{
		db: db,
	}
}

func (d *GORMClueFilterReportDao) Create(ctx context.Context, report model.ClueFilterReport) (int64, error) {
	err := d.db.WithContext(ctx).Create(report).Error
	if err != nil {
		return 0, err
	}
	return report.ID, nil
}

func (d *GORMClueFilterReportDao) Update(ctx context.Context, report model.ClueFilterReport, fields []string) error {
	return d.db.WithContext(ctx).Model(&model.ClueFilterReport{}).Where("id = ?", report.ID).Select(fields).Updates(&report).Error
}

func (d *GORMClueFilterReportDao) Delete(ctx context.Context, id int64) error {
	return d.db.WithContext(ctx).Delete(&model.ClueFilterReport{}, id).Error
}

func (d *GORMClueFilterReportDao) FindByID(ctx context.Context, id int64) (model.ClueFilterReport, error) {
	var report model.ClueFilterReport
	err := d.db.WithContext(ctx).First(&report, id).Error
	if err != nil {
		return model.ClueFilterReport{}, err
	}
	return report, nil
}

func (d *GORMClueFilterReportDao) FindByClueID(ctx context.Context, clueID int64) (model.ClueFilterReport, error) {
	var report model.ClueFilterReport
	err := d.db.WithContext(ctx).Where("clue_id = ?", clueID).First(&report).Error
	if err != nil {
		return model.ClueFilterReport{}, err
	}
	return report, nil
}

func (d *GORMClueFilterReportDao) FindByOperatorID(ctx context.Context, operatorID int64) ([]model.ClueFilterReport, error) {
	var reports []model.ClueFilterReport
	err := d.db.WithContext(ctx).Where("operator_id = ?", operatorID).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueFilterReportDao) FindByFilterType(ctx context.Context, filterType int) ([]model.ClueFilterReport, error) {
	var reports []model.ClueFilterReport
	err := d.db.WithContext(ctx).Where("filter_type = ?", filterType).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueFilterReportDao) List(ctx context.Context, offset, limit int) ([]model.ClueFilterReport, error) {
	var reports []model.ClueFilterReport
	err := d.db.WithContext(ctx).Offset(offset).Limit(limit).Find(&reports).Error
	if err != nil {
		return nil, err
	}
	return reports, nil
}

func (d *GORMClueFilterReportDao) Count(ctx context.Context) (int64, error) {
	var count int64
	err := d.db.WithContext(ctx).Model(&model.ClueFilterReport{}).Count(&count).Error
	if err != nil {
		return 0, err
	}
	return count, nil
}

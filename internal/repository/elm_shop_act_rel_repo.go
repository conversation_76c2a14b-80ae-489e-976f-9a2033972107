package repository

import (
	"context"
	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ElmShopActRelRepo 饿了么店铺活动关系仓储接口
type ElmShopActRelRepo interface {
	// Create 创建店铺活动关系
	Create(ctx context.Context, rel domain.ElmShopActRel) error
	// Update 更新店铺活动关系
	Update(ctx context.Context, rel domain.ElmShopActRel) error
	// Get 获取店铺活动关系
	Get(ctx context.Context, id int64) (domain.ElmShopActRel, error)
	// GetByShopID 根据店铺ID获取活动关系
	GetByShopID(ctx context.Context, shopID int64) ([]domain.ElmShopActRel, error)
	// GetByActivityID 根据活动ID获取店铺关系列表
	GetByActivityID(ctx context.Context, activityID int64) ([]domain.ElmShopActRel, error)
	// List 获取店铺活动关系列表
	List(ctx context.Context, offset, limit int) ([]domain.ElmShopActRel, error)
	// Delete 删除店铺活动关系
	Delete(ctx context.Context, id int64) error
	// UpdateAuditStatus 更新审核状态
	UpdateAuditStatus(ctx context.Context, id int64, status int8) error
	// UpdatePromotionState 更新推广状态
	UpdatePromotionState(ctx context.Context, id int64, state int64) error
}

// CacheElmShopActRelRepo 饿了么店铺活动关系仓储实现
type CacheElmShopActRelRepo struct {
	dao dao.ElmShopActRelDao
}

// NewCacheElmShopActRelRepo 创建ElmShopActRelRepo实例
func NewCacheElmShopActRelRepo(dao dao.ElmShopActRelDao) *CacheElmShopActRelRepo {
	return &CacheElmShopActRelRepo{dao: dao}
}

func (r *CacheElmShopActRelRepo) toModel(rel domain.ElmShopActRel) model.ElmShopActRel {
	return model.ElmShopActRel{
		ID:                rel.ID,
		ShopID:            rel.ShopID,
		ActRecID:          rel.ActRecID,
		ActivityID:        int64(rel.ActivityID),
		OriShopID:         rel.OriShopID,
		OriShopName:       rel.OriShopName,
		ShopScore:         rel.ShopScore,
		AuditStatus:       rel.AuditStatus,
		BonusFee:          int64(rel.BonusFee),
		BounsOrderNum:     int64(rel.BounsOrderNum),
		ChannelServiceFee: rel.ChannelServiceFee,
		CityServicesFee:   rel.CityServicesFee,
		CommissionRate:    rel.CommissionRate,
		PromotionState:    int64(rel.PromotionState),
		SalesNum:          int64(rel.SalesNum),
		ServicesFee:       int64(rel.ServicesFee),
		OrderAmtLimit:     int64(rel.OrderAmtLimit),
		CityName:          rel.CityName,
		DistrictName:      rel.DistrictName,
		EnrollDate:        rel.EnrollDate,
		StartDate:         rel.StartDate,
		EndDate:           rel.EndDate,
		CreatedAt:         rel.CreatedAt,
		UpdatedAt:         rel.UpdatedAt,
		ShopCode:          rel.ShopCode,
		TerminateTime:     rel.TerminateTime,
		IsDeleted:         rel.IsDeleted,
		MatchName:         rel.MatchName,
		BusinessDistrict:  rel.BusinessDistrict,
		RefuseDate:        rel.RefuseDate,
		PassDate:          rel.PassDate,
		BrandID:           int64(rel.BrandID),
	}
}

func (r *CacheElmShopActRelRepo) toDomain(rel model.ElmShopActRel) domain.ElmShopActRel {
	return domain.ElmShopActRel{
		ID:                rel.ID,
		ShopID:            rel.ShopID,
		ActRecID:          rel.ActRecID,
		ActivityID:        int(rel.ActivityID),
		OriShopID:         rel.OriShopID,
		OriShopName:       rel.OriShopName,
		ShopScore:         rel.ShopScore,
		AuditStatus:       rel.AuditStatus,
		BonusFee:          int(rel.BonusFee),
		BounsOrderNum:     int(rel.BounsOrderNum),
		ChannelServiceFee: rel.ChannelServiceFee,
		CityServicesFee:   rel.CityServicesFee,
		CommissionRate:    rel.CommissionRate,
		PromotionState:    int(rel.PromotionState),
		SalesNum:          int(rel.SalesNum),
		ServicesFee:       int(rel.ServicesFee),
		OrderAmtLimit:     int(rel.OrderAmtLimit),
		CityName:          rel.CityName,
		DistrictName:      rel.DistrictName,
		EnrollDate:        rel.EnrollDate,
		StartDate:         rel.StartDate,
		EndDate:           rel.EndDate,
		CreatedAt:         rel.CreatedAt,
		UpdatedAt:         rel.UpdatedAt,
		ShopCode:          rel.ShopCode,
		TerminateTime:     rel.TerminateTime,
		IsDeleted:         rel.IsDeleted,
		MatchName:         rel.MatchName,
		BusinessDistrict:  rel.BusinessDistrict,
		RefuseDate:        rel.RefuseDate,
		PassDate:          rel.PassDate,
		BrandID:           int(rel.BrandID),
	}
}

// Create 创建店铺活动关系
func (r *CacheElmShopActRelRepo) Create(ctx context.Context, rel domain.ElmShopActRel) error {
	_, err := r.dao.Create(ctx, r.toModel(rel))
	return err
}

// Update 更新店铺活动关系
func (r *CacheElmShopActRelRepo) Update(ctx context.Context, rel domain.ElmShopActRel) error {
	return r.dao.Update(ctx, r.toModel(rel), []string{"shop_id", "act_rec_id", "activity_id", "ori_shop_id", "ori_shop_name",
		"shop_score", "audit_status", "bonus_fee", "bouns_order_num", "channel_service_fee", "city_services_fee",
		"commission_rate", "promotion_state", "sales_num", "services_fee", "order_amt_limit", "city_name",
		"district_name", "enroll_date", "start_date", "end_date", "shop_code", "terminate_time", "is_deleted",
		"match_name", "business_district", "refuse_date", "pass_date", "brand_id", "updated_at"})
}

// Get 获取店铺活动关系
func (r *CacheElmShopActRelRepo) Get(ctx context.Context, id int64) (domain.ElmShopActRel, error) {
	rel, err := r.dao.Get(ctx, id)
	if err != nil {
		return domain.ElmShopActRel{}, err
	}
	return r.toDomain(rel), nil
}

// GetByShopID 根据店铺ID获取活动关系
func (r *CacheElmShopActRelRepo) GetByShopID(ctx context.Context, shopID int64) ([]domain.ElmShopActRel, error) {
	rels, err := r.dao.GetByShopID(ctx, shopID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopActRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

// GetByActivityID 根据活动ID获取店铺关系列表
func (r *CacheElmShopActRelRepo) GetByActivityID(ctx context.Context, activityID int64) ([]domain.ElmShopActRel, error) {
	rels, err := r.dao.GetByActivityID(ctx, activityID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopActRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

// List 获取店铺活动关系列表
func (r *CacheElmShopActRelRepo) List(ctx context.Context, offset, limit int) ([]domain.ElmShopActRel, error) {
	rels, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopActRel, len(rels))
	for i, rel := range rels {
		result[i] = r.toDomain(rel)
	}
	return result, nil
}

// Delete 删除店铺活动关系
func (r *CacheElmShopActRelRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

// UpdateAuditStatus 更新审核状态
func (r *CacheElmShopActRelRepo) UpdateAuditStatus(ctx context.Context, id int64, status int8) error {
	return r.dao.UpdateAuditStatus(ctx, id, status)
}

// UpdatePromotionState 更新推广状态
func (r *CacheElmShopActRelRepo) UpdatePromotionState(ctx context.Context, id int64, state int64) error {
	return r.dao.UpdatePromotionState(ctx, id, state)
}

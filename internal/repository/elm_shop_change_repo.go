package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ElmShopChangeRepo 饿了么店铺变更记录仓储接口
type ElmShopChangeRepo interface {
	// Create 创建店铺变更记录
	Create(ctx context.Context, change domain.ElmShopChange) error
	// GetByID 根据ID查询店铺变更记录
	GetByID(ctx context.Context, id int64) (domain.ElmShopChange, error)
	// ListByShopID 根据店铺ID查询店铺变更记录列表
	ListByShopID(ctx context.Context, shopID int64) ([]domain.ElmShopChange, error)
	// List 分页查询店铺变更记录列表
	List(ctx context.Context, offset, limit int) ([]domain.ElmShopChange, error)
	// Count 统计店铺变更记录总数
	Count(ctx context.Context) (int64, error)
}

type CacheElmShopChangeRepo struct {
	dao dao.ElmShopChangeDao
}

// NewCacheElmShopChangeRepo 创建店铺变更记录仓储实现
func NewCacheElmShopChangeRepo(dao dao.ElmShopChangeDao) *CacheElmShopChangeRepo {
	return &CacheElmShopChangeRepo{dao: dao}
}

func (r *CacheElmShopChangeRepo) toModel(change domain.ElmShopChange) model.ElmShopChange {
	return model.ElmShopChange{
		ID:        change.ID,
		ElmShopID: change.ElmShopID,
		OldName:   change.OldName,
		NewName:   change.NewName,
		CreatedAt: change.CreatedAt,
		UpdatedAt: change.UpdatedAt,
	}
}

func (r *CacheElmShopChangeRepo) toDomain(change model.ElmShopChange) domain.ElmShopChange {
	return domain.ElmShopChange{
		ID:        change.ID,
		ElmShopID: change.ElmShopID,
		OldName:   change.OldName,
		NewName:   change.NewName,
		CreatedAt: change.CreatedAt,
		UpdatedAt: change.UpdatedAt,
	}
}

func (r *CacheElmShopChangeRepo) Create(ctx context.Context, change domain.ElmShopChange) error {
	_, err := r.dao.Create(ctx, r.toModel(change))
	return err
}

func (r *CacheElmShopChangeRepo) GetByID(ctx context.Context, id int64) (domain.ElmShopChange, error) {
	change, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ElmShopChange{}, err
	}
	return r.toDomain(change), nil
}

func (r *CacheElmShopChangeRepo) ListByShopID(ctx context.Context, shopID int64) ([]domain.ElmShopChange, error) {
	changes, err := r.dao.FindByShopID(ctx, shopID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopChange, len(changes))
	for i, change := range changes {
		result[i] = r.toDomain(change)
	}
	return result, nil
}

func (r *CacheElmShopChangeRepo) List(ctx context.Context, offset, limit int) ([]domain.ElmShopChange, error) {
	changes, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ElmShopChange, len(changes))
	for i, change := range changes {
		result[i] = r.toDomain(change)
	}
	return result, nil
}

func (r *CacheElmShopChangeRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

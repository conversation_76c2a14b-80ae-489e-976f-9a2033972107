package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// TrackRecordRepo 跟踪记录仓储接口
type TrackRecordRepo interface {
	// Create 创建跟踪记录
	Create(ctx context.Context, record domain.TrackRecord) (int64, error)
	// Update 更新跟踪记录
	Update(ctx context.Context, record domain.TrackRecord, fields []string) error
	// Delete 删除跟踪记录
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询跟踪记录
	GetByID(ctx context.Context, id int64) (domain.TrackRecord, error)
	// ListByUserID 根据用户ID查询跟踪记录列表
	ListByUserID(ctx context.Context, userID int64) ([]domain.TrackRecord, error)
	// ListByClueID 根据线索ID查询跟踪记录列表
	ListByClueID(ctx context.Context, clueID int64) ([]domain.TrackRecord, error)
	// ListByStatus 根据状态查询跟踪记录列表
	ListByStatus(ctx context.Context, status int) ([]domain.TrackRecord, error)
	// ListByPlatform 根据平台查询跟踪记录列表
	ListByPlatform(ctx context.Context, platform int) ([]domain.TrackRecord, error)
	// List 分页查询跟踪记录列表
	List(ctx context.Context, offset, limit int) ([]domain.TrackRecord, error)
	// Count 统计跟踪记录总数
	Count(ctx context.Context) (int64, error)
}

type CacheTrackRecordRepo struct {
	dao dao.TrackRecordDao
}

func NewCacheTrackRecordRepo(dao dao.TrackRecordDao) *CacheTrackRecordRepo {
	return &CacheTrackRecordRepo{
		dao: dao,
	}
}

func (r *CacheTrackRecordRepo) toModel(record domain.TrackRecord) model.TrackRecord {
	return model.TrackRecord{
		ID:              record.ID,
		ClueID:          record.ClueID,
		ClueType:        record.ClueType,
		UserID:          record.UserID,
		CreateTime:      record.CreateTime,
		UpdateTime:      record.UpdateTime,
		Status:          record.Status,
		CancelTime:      record.CancelTime,
		SubmitTime:      record.SubmitTime,
		StoreNum:        record.StoreNum,
		ActivityName:    record.ActivityName,
		ActivityCity:    record.ActivityCity,
		Bounty:          record.Bounty,
		BountyLimitCent: record.BountyLimitCent,
		DailyQuantity:   record.DailyQuantity,
		IfCustomer:      record.IfCustomer,
		Platform:        record.Platform,
		StoreID:         record.StoreID,
		BelongID:        record.BelongID,
	}
}

func (r *CacheTrackRecordRepo) toDomain(record model.TrackRecord) domain.TrackRecord {
	return domain.TrackRecord{
		ID:              record.ID,
		ClueID:          record.ClueID,
		ClueType:        record.ClueType,
		UserID:          record.UserID,
		CreateTime:      record.CreateTime,
		UpdateTime:      record.UpdateTime,
		Status:          record.Status,
		CancelTime:      record.CancelTime,
		SubmitTime:      record.SubmitTime,
		StoreNum:        record.StoreNum,
		ActivityName:    record.ActivityName,
		ActivityCity:    record.ActivityCity,
		Bounty:          record.Bounty,
		BountyLimitCent: record.BountyLimitCent,
		DailyQuantity:   record.DailyQuantity,
		IfCustomer:      record.IfCustomer,
		Platform:        record.Platform,
		StoreID:         record.StoreID,
		BelongID:        record.BelongID,
	}
}

func (r *CacheTrackRecordRepo) Create(ctx context.Context, record domain.TrackRecord) (int64, error) {
	return r.dao.Create(ctx, r.toModel(record))
}

func (r *CacheTrackRecordRepo) Update(ctx context.Context, record domain.TrackRecord, fields []string) error {
	return r.dao.Update(ctx, r.toModel(record), fields)
}

func (r *CacheTrackRecordRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheTrackRecordRepo) GetByID(ctx context.Context, id int64) (domain.TrackRecord, error) {
	record, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.TrackRecord{}, err
	}
	return r.toDomain(record), nil
}

func (r *CacheTrackRecordRepo) ListByUserID(ctx context.Context, userID int64) ([]domain.TrackRecord, error) {
	records, err := r.dao.ListByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.TrackRecord, len(records))
	for i, record := range records {
		result[i] = r.toDomain(record)
	}
	return result, nil
}

func (r *CacheTrackRecordRepo) ListByClueID(ctx context.Context, clueID int64) ([]domain.TrackRecord, error) {
	records, err := r.dao.ListByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.TrackRecord, len(records))
	for i, record := range records {
		result[i] = r.toDomain(record)
	}
	return result, nil
}

func (r *CacheTrackRecordRepo) ListByStatus(ctx context.Context, status int) ([]domain.TrackRecord, error) {
	records, err := r.dao.ListByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.TrackRecord, len(records))
	for i, record := range records {
		result[i] = r.toDomain(record)
	}
	return result, nil
}

func (r *CacheTrackRecordRepo) ListByPlatform(ctx context.Context, platform int) ([]domain.TrackRecord, error) {
	records, err := r.dao.ListByPlatform(ctx, platform)
	if err != nil {
		return nil, err
	}
	result := make([]domain.TrackRecord, len(records))
	for i, record := range records {
		result[i] = r.toDomain(record)
	}
	return result, nil
}

func (r *CacheTrackRecordRepo) List(ctx context.Context, offset, limit int) ([]domain.TrackRecord, error) {
	records, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.TrackRecord, len(records))
	for i, record := range records {
		result[i] = r.toDomain(record)
	}
	return result, nil
}

func (r *CacheTrackRecordRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

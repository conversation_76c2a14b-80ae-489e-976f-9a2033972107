package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type CircleShopRepo interface {
	Create(ctx context.Context, shop domain.CircleShop) (int64, error)
	Update(ctx context.Context, shop domain.CircleShop, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.CircleShop, error)
	FindByName(ctx context.Context, name string) (domain.CircleShop, error)
	List(ctx context.Context, offset, limit int) ([]domain.CircleShop, error)
	ListByCircleID(ctx context.Context, circleID int64) ([]domain.CircleShop, error)
	ListByBrandID(ctx context.Context, brandID int64) ([]domain.CircleShop, error)
	Count(ctx context.Context) (int64, error)
	ListUnmatchedShops(ctx context.Context, offset, limit int) ([]domain.CircleShop, error)
	BatchUpdateBrandID(ctx context.Context, shopIDs []int64, brandID int64) error
}

type CacheCircleShopRepo struct {
	circleShopDao dao.CircleShopDao
}

func NewCacheCircleShopRepo(circleShopDao dao.CircleShopDao) *CacheCircleShopRepo {
	return &CacheCircleShopRepo{
		circleShopDao: circleShopDao,
	}
}

func (r *CacheCircleShopRepo) Create(ctx context.Context, shop domain.CircleShop) (int64, error) {
	return r.circleShopDao.Create(ctx, r.toModel(shop))
}

func (r *CacheCircleShopRepo) Update(ctx context.Context, shop domain.CircleShop, fields []string) error {
	return r.circleShopDao.Update(ctx, r.toModel(shop), fields)
}

func (r *CacheCircleShopRepo) Delete(ctx context.Context, id int64) error {
	return r.circleShopDao.Delete(ctx, id)
}

func (r *CacheCircleShopRepo) FindByID(ctx context.Context, id int64) (domain.CircleShop, error) {
	shop, err := r.circleShopDao.FindByID(ctx, id)
	if err != nil {
		return domain.CircleShop{}, err
	}
	return r.toDomain(shop), nil
}

func (r *CacheCircleShopRepo) FindByName(ctx context.Context, name string) (domain.CircleShop, error) {
	shop, err := r.circleShopDao.FindByName(ctx, name)
	if err != nil {
		return domain.CircleShop{}, err
	}
	return r.toDomain(shop), nil
}

func (r *CacheCircleShopRepo) List(ctx context.Context, offset, limit int) ([]domain.CircleShop, error) {
	shops, err := r.circleShopDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.CircleShop
	for _, shop := range shops {
		result = append(result, r.toDomain(shop))
	}
	return result, nil
}

func (r *CacheCircleShopRepo) ListByCircleID(ctx context.Context, circleID int64) ([]domain.CircleShop, error) {
	shops, err := r.circleShopDao.ListByCircleID(ctx, circleID)
	if err != nil {
		return nil, err
	}
	var result []domain.CircleShop
	for _, shop := range shops {
		result = append(result, r.toDomain(shop))
	}
	return result, nil
}

func (r *CacheCircleShopRepo) ListByBrandID(ctx context.Context, brandID int64) ([]domain.CircleShop, error) {
	shops, err := r.circleShopDao.ListByBrandID(ctx, brandID)
	if err != nil {
		return nil, err
	}
	var result []domain.CircleShop
	for _, shop := range shops {
		result = append(result, r.toDomain(shop))
	}
	return result, nil
}

func (r *CacheCircleShopRepo) Count(ctx context.Context) (int64, error) {
	return r.circleShopDao.Count(ctx)
}

func (r *CacheCircleShopRepo) ListUnmatchedShops(ctx context.Context, offset, limit int) ([]domain.CircleShop, error) {
	shops, err := r.circleShopDao.ListUnmatchedShops(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.CircleShop
	for _, shop := range shops {
		result = append(result, r.toDomain(shop))
	}
	return result, nil
}

func (r *CacheCircleShopRepo) BatchUpdateBrandID(ctx context.Context, shopIDs []int64, brandID int64) error {
	return r.circleShopDao.BatchUpdateBrandID(ctx, shopIDs, brandID)
}

func (r *CacheCircleShopRepo) toModel(shop domain.CircleShop) model.CircleShop {
	return model.CircleShop{
		ID:             shop.ID,
		CreatedAt:      shop.CreatedAt,
		UpdatedAt:      shop.UpdatedAt,
		Name:           shop.Name,
		CircleID:       shop.CircleID,
		CrawlTime:      shop.CrawlTime,
		Distance:       shop.Distance,
		Tags:           shop.Tags,
		MonthSale:      shop.MonthSale,
		Score:          shop.Score,
		CategoryName:   shop.CategoryName,
		LowerPrice:     shop.LowerPrice,
		TallPrice:      shop.TallPrice,
		IsBrand:        shop.IsBrand,
		IsNew:          shop.IsNew,
		LastUpdateTime: shop.LastUpdateTime,
		BrandID:        shop.BrandID,
	}
}

func (r *CacheCircleShopRepo) toDomain(shop model.CircleShop) domain.CircleShop {
	return domain.CircleShop{
		ID:             shop.ID,
		CreatedAt:      shop.CreatedAt,
		UpdatedAt:      shop.UpdatedAt,
		Name:           shop.Name,
		CircleID:       shop.CircleID,
		CrawlTime:      shop.CrawlTime,
		Distance:       shop.Distance,
		Tags:           shop.Tags,
		MonthSale:      shop.MonthSale,
		Score:          shop.Score,
		CategoryName:   shop.CategoryName,
		LowerPrice:     shop.LowerPrice,
		TallPrice:      shop.TallPrice,
		IsBrand:        shop.IsBrand,
		IsNew:          shop.IsNew,
		LastUpdateTime: shop.LastUpdateTime,
		BrandID:        shop.BrandID,
	}
}

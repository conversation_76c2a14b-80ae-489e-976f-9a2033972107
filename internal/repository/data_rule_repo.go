package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// DataRuleRepo 数据规则仓储接口
type DataRuleRepo interface {
	// Create 创建数据规则
	Create(ctx context.Context, rule domain.DataRule) (int64, error)
	// Update 更新数据规则
	Update(ctx context.Context, rule domain.DataRule, fields []string) error
	// Delete 删除数据规则
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询数据规则
	FindByID(ctx context.Context, id int64) (domain.DataRule, error)
	// FindByType 根据规则类型查询数据规则列表
	FindByType(ctx context.Context, ruleType int) ([]domain.DataRule, error)
	// FindByStatus 根据状态查询数据规则列表
	FindByStatus(ctx context.Context, status int) ([]domain.DataRule, error)
	// FindByCreator 根据创建人查询数据规则列表
	FindByCreator(ctx context.Context, creatorID int64) ([]domain.DataRule, error)
	// List 查询数据规则列表
	List(ctx context.Context, offset, limit int) ([]domain.DataRule, error)
	// Count 统计数据规则总数
	Count(ctx context.Context) (int64, error)
}

type CacheDataRuleRepo struct {
	dataRuleDao dao.DataRuleDao
}

// NewCacheDataRuleRepo 创建数据规则仓储实现
func NewCacheDataRuleRepo(dataRuleDao dao.DataRuleDao) *CacheDataRuleRepo {
	return &CacheDataRuleRepo{
		dataRuleDao: dataRuleDao,
	}
}

func (r *CacheDataRuleRepo) Create(ctx context.Context, rule domain.DataRule) (int64, error) {
	return r.dataRuleDao.Create(ctx, r.toModel(rule))
}

func (r *CacheDataRuleRepo) Update(ctx context.Context, rule domain.DataRule, fields []string) error {
	return r.dataRuleDao.Update(ctx, r.toModel(rule), fields)
}

func (r *CacheDataRuleRepo) Delete(ctx context.Context, id int64) error {
	return r.dataRuleDao.Delete(ctx, id)
}

func (r *CacheDataRuleRepo) FindByID(ctx context.Context, id int64) (domain.DataRule, error) {
	model, err := r.dataRuleDao.FindByID(ctx, id)
	if err != nil {
		return domain.DataRule{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDataRuleRepo) FindByType(ctx context.Context, ruleType int) ([]domain.DataRule, error) {
	models, err := r.dataRuleDao.FindByType(ctx, ruleType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DataRule, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDataRuleRepo) FindByStatus(ctx context.Context, status int) ([]domain.DataRule, error) {
	models, err := r.dataRuleDao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DataRule, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDataRuleRepo) FindByCreator(ctx context.Context, creatorID int64) ([]domain.DataRule, error) {
	models, err := r.dataRuleDao.FindByCreator(ctx, creatorID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DataRule, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDataRuleRepo) List(ctx context.Context, offset, limit int) ([]domain.DataRule, error) {
	models, err := r.dataRuleDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.DataRule, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDataRuleRepo) Count(ctx context.Context) (int64, error) {
	return r.dataRuleDao.Count(ctx)
}

func (r *CacheDataRuleRepo) toModel(rule domain.DataRule) model.DataRule {
	return model.DataRule{
		ID:          rule.ID,
		RuleName:    rule.RuleName,
		RuleType:    rule.RuleType,
		RuleContent: rule.RuleContent,
		Status:      rule.Status,
		Priority:    rule.Priority,
		Description: rule.Description,
		CreatorID:   rule.CreatorID,
		CreatorName: rule.CreatorName,
		CreatedAt:   rule.CreatedAt,
		UpdatedAt:   rule.UpdatedAt,
		DeletedAt:   rule.DeletedAt,
	}
}

func (r *CacheDataRuleRepo) toDomain(rule model.DataRule) domain.DataRule {
	return domain.DataRule{
		ID:          rule.ID,
		RuleName:    rule.RuleName,
		RuleType:    rule.RuleType,
		RuleContent: rule.RuleContent,
		Status:      rule.Status,
		Priority:    rule.Priority,
		Description: rule.Description,
		CreatorID:   rule.CreatorID,
		CreatorName: rule.CreatorName,
		CreatedAt:   rule.CreatedAt,
		UpdatedAt:   rule.UpdatedAt,
		DeletedAt:   rule.DeletedAt,
	}
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ConfigRepo 系统配置仓储接口
type ConfigRepo interface {
	// Create 创建系统配置
	Create(ctx context.Context, config domain.Config) (int64, error)
	// Update 更新系统配置
	Update(ctx context.Context, config domain.Config, fields []string) error
	// Delete 删除系统配置
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询系统配置
	FindByID(ctx context.Context, id int64) (domain.Config, error)
	// FindByKey 根据配置键查询系统配置
	FindByKey(ctx context.Context, key string) (domain.Config, error)
	// FindByType 根据配置类型查询系统配置列表
	FindByType(ctx context.Context, configType string) ([]domain.Config, error)
	// List 查询系统配置列表
	List(ctx context.Context, offset, limit int) ([]domain.Config, error)
	// Count 统计系统配置总数
	Count(ctx context.Context) (int64, error)
}

type CacheConfigRepo struct {
	configDao dao.ConfigDao
}

// NewCacheConfigRepo 创建系统配置仓储实现
func NewCacheConfigRepo(configDao dao.ConfigDao) *CacheConfigRepo {
	return &CacheConfigRepo{
		configDao: configDao,
	}
}

func (r *CacheConfigRepo) Create(ctx context.Context, config domain.Config) (int64, error) {
	return r.configDao.Create(ctx, r.toModel(config))
}

func (r *CacheConfigRepo) Update(ctx context.Context, config domain.Config, fields []string) error {
	return r.configDao.Update(ctx, r.toModel(config), fields)
}

func (r *CacheConfigRepo) Delete(ctx context.Context, id int64) error {
	return r.configDao.Delete(ctx, id)
}

func (r *CacheConfigRepo) FindByID(ctx context.Context, id int64) (domain.Config, error) {
	model, err := r.configDao.FindByID(ctx, id)
	if err != nil {
		return domain.Config{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheConfigRepo) FindByKey(ctx context.Context, key string) (domain.Config, error) {
	model, err := r.configDao.FindByKey(ctx, key)
	if err != nil {
		return domain.Config{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheConfigRepo) FindByType(ctx context.Context, configType string) ([]domain.Config, error) {
	models, err := r.configDao.FindByType(ctx, configType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.Config, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheConfigRepo) List(ctx context.Context, offset, limit int) ([]domain.Config, error) {
	models, err := r.configDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.Config, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheConfigRepo) Count(ctx context.Context) (int64, error) {
	return r.configDao.Count(ctx)
}

func (r *CacheConfigRepo) toModel(config domain.Config) model.Config {
	return model.Config{
		ID:        config.ID,
		Key:       config.Key,
		Value:     config.Value,
		Type:      config.Type,
		Desc:      config.Desc,
		CreatedAt: config.CreatedAt,
		UpdatedAt: config.UpdatedAt,
		DeletedAt: config.DeletedAt,
	}
}

func (r *CacheConfigRepo) toDomain(config model.Config) domain.Config {
	return domain.Config{
		ID:        config.ID,
		Key:       config.Key,
		Value:     config.Value,
		Type:      config.Type,
		Desc:      config.Desc,
		CreatedAt: config.CreatedAt,
		UpdatedAt: config.UpdatedAt,
		DeletedAt: config.DeletedAt,
	}
}

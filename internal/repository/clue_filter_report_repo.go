package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueFilterReportRepo 线索过滤报表仓储接口
type ClueFilterReportRepo interface {
	// Create 创建线索过滤报表
	Create(ctx context.Context, report domain.ClueFilterReport) (int64, error)
	// Update 更新线索过滤报表
	Update(ctx context.Context, report domain.ClueFilterReport, fields []string) error
	// Delete 删除线索过滤报表
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索过滤报表
	FindByID(ctx context.Context, id int64) (domain.ClueFilterReport, error)
	// FindByClueID 根据线索ID查询线索过滤报表
	FindByClueID(ctx context.Context, clueID int64) (domain.ClueFilterReport, error)
	// FindByOperatorID 根据操作人ID查询线索过滤报表列表
	FindByOperatorID(ctx context.Context, operatorID int64) ([]domain.ClueFilterReport, error)
	// FindByFilterType 根据过滤类型查询线索过滤报表列表
	FindByFilterType(ctx context.Context, filterType int) ([]domain.ClueFilterReport, error)
	// List 查询线索过滤报表列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueFilterReport, error)
	// Count 统计线索过滤报表总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueFilterReportRepo struct {
	dao dao.ClueFilterReportDao
}

// NewCacheClueFilterReportRepo 创建线索过滤报表仓储实现
func NewCacheClueFilterReportRepo(dao dao.ClueFilterReportDao) *CacheClueFilterReportRepo {
	return &CacheClueFilterReportRepo{
		dao: dao,
	}
}

func (r *CacheClueFilterReportRepo) Create(ctx context.Context, report domain.ClueFilterReport) (int64, error) {
	return r.dao.Create(ctx, r.toModel(report))
}

func (r *CacheClueFilterReportRepo) Update(ctx context.Context, report domain.ClueFilterReport, fields []string) error {
	return r.dao.Update(ctx, r.toModel(report), fields)
}

func (r *CacheClueFilterReportRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheClueFilterReportRepo) FindByID(ctx context.Context, id int64) (domain.ClueFilterReport, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueFilterReport{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueFilterReportRepo) FindByClueID(ctx context.Context, clueID int64) (domain.ClueFilterReport, error) {
	model, err := r.dao.FindByClueID(ctx, clueID)
	if err != nil {
		return domain.ClueFilterReport{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueFilterReportRepo) FindByOperatorID(ctx context.Context, operatorID int64) ([]domain.ClueFilterReport, error) {
	models, err := r.dao.FindByOperatorID(ctx, operatorID)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueFilterReport
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueFilterReportRepo) FindByFilterType(ctx context.Context, filterType int) ([]domain.ClueFilterReport, error) {
	models, err := r.dao.FindByFilterType(ctx, filterType)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueFilterReport
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueFilterReportRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueFilterReport, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueFilterReport
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueFilterReportRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

func (r *CacheClueFilterReportRepo) toModel(report domain.ClueFilterReport) model.ClueFilterReport {
	return model.ClueFilterReport{
		ID:           report.ID,
		CreatedAt:    report.CreatedAt,
		UpdatedAt:    report.UpdatedAt,
		DeletedAt:    report.DeletedAt,
		ClueID:       report.ClueID,
		FilterType:   report.FilterType,
		FilterReason: report.FilterReason,
		OperatorID:   report.OperatorID,
		OperatorName: report.OperatorName,
	}
}

func (r *CacheClueFilterReportRepo) toDomain(report model.ClueFilterReport) domain.ClueFilterReport {
	return domain.ClueFilterReport{
		ID:           report.ID,
		CreatedAt:    report.CreatedAt,
		UpdatedAt:    report.UpdatedAt,
		DeletedAt:    report.DeletedAt,
		ClueID:       report.ClueID,
		FilterType:   report.FilterType,
		FilterReason: report.FilterReason,
		OperatorID:   report.OperatorID,
		OperatorName: report.OperatorName,
	}
}

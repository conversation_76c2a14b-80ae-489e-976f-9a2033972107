package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type CategoryRepo interface {
	Create(ctx context.Context, category domain.Category) (int64, error)
	Update(ctx context.Context, category domain.Category, fields []string) error
	Delete(ctx context.Context, id int64) error
	FindByID(ctx context.Context, id int64) (domain.Category, error)
	FindByName(ctx context.Context, name string) (domain.Category, error)
	List(ctx context.Context, offset, limit int) ([]domain.Category, error)
	ListByParentID(ctx context.Context, parentID int64) ([]domain.Category, error)
	Count(ctx context.Context) (int64, error)
}

type CacheCategoryRepo struct {
	categoryDao dao.CategoryDao
}

func NewCacheCategoryRepo(categoryDao dao.CategoryDao) *CacheCategoryRepo {
	return &CacheCategoryRepo{
		categoryDao: categoryDao,
	}
}

func (r *CacheCategoryRepo) Create(ctx context.Context, category domain.Category) (int64, error) {
	return r.categoryDao.Create(ctx, r.toModel(category))
}

func (r *CacheCategoryRepo) Update(ctx context.Context, category domain.Category, fields []string) error {
	return r.categoryDao.Update(ctx, r.toModel(category), fields)
}

func (r *CacheCategoryRepo) Delete(ctx context.Context, id int64) error {
	return r.categoryDao.Delete(ctx, id)
}

func (r *CacheCategoryRepo) FindByID(ctx context.Context, id int64) (domain.Category, error) {
	category, err := r.categoryDao.FindByID(ctx, id)
	if err != nil {
		return domain.Category{}, err
	}
	return r.toDomain(category), nil
}

func (r *CacheCategoryRepo) FindByName(ctx context.Context, name string) (domain.Category, error) {
	category, err := r.categoryDao.FindByName(ctx, name)
	if err != nil {
		return domain.Category{}, err
	}
	return r.toDomain(category), nil
}

func (r *CacheCategoryRepo) List(ctx context.Context, offset, limit int) ([]domain.Category, error) {
	categories, err := r.categoryDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.Category
	for _, category := range categories {
		result = append(result, r.toDomain(category))
	}
	return result, nil
}

func (r *CacheCategoryRepo) ListByParentID(ctx context.Context, parentID int64) ([]domain.Category, error) {
	categories, err := r.categoryDao.ListByParentID(ctx, parentID)
	if err != nil {
		return nil, err
	}
	var result []domain.Category
	for _, category := range categories {
		result = append(result, r.toDomain(category))
	}
	return result, nil
}

func (r *CacheCategoryRepo) Count(ctx context.Context) (int64, error) {
	return r.categoryDao.Count(ctx)
}

func (r *CacheCategoryRepo) toModel(category domain.Category) model.Category {
	return model.Category{
		ID:        category.ID,
		Name:      category.Name,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
		Type:      category.Type,
		Level:     category.Level,
		ParentID:  category.ParentID,
	}
}

func (r *CacheCategoryRepo) toDomain(category model.Category) domain.Category {
	return domain.Category{
		ID:        category.ID,
		Name:      category.Name,
		CreatedAt: category.CreatedAt,
		UpdatedAt: category.UpdatedAt,
		Type:      category.Type,
		Level:     category.Level,
		ParentID:  category.ParentID,
	}
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type UserSessionRepo interface {
	Create(ctx context.Context, session domain.UserSession) (int64, error)
	Update(ctx context.Context, session domain.UserSession, fields []string) error
	Delete(ctx context.Context, id int64) error
	GetByID(ctx context.Context, id int64) (domain.UserSession, error)
	GetByToken(ctx context.Context, token string) (domain.UserSession, error)
	GetByUserIDAndTerminal(ctx context.Context, userID int64, terminal int8) (domain.UserSession, error)
	ListByUserID(ctx context.Context, userID int64) ([]domain.UserSession, error)
	List(ctx context.Context, offset, limit int) ([]domain.UserSession, error)
	Count(ctx context.Context) (int64, error)
}

type CacheUserSessionRepo struct {
	userSessionDao dao.UserSessionDao
}

func NewCacheUserSessionRepo(userSessionDao dao.UserSessionDao) *CacheUserSessionRepo {
	return &CacheUserSessionRepo{
		userSessionDao: userSessionDao,
	}
}

func (r *CacheUserSessionRepo) toModel(session domain.UserSession) model.UserSession {
	return model.UserSession{
		ID:         session.ID,
		UserID:     session.UserID,
		Terminal:   session.Terminal,
		Token:      session.Token,
		UpdateTime: session.UpdateTime,
		ExpireTime: session.ExpireTime,
		Status:     session.Status,
	}
}

func (r *CacheUserSessionRepo) toDomain(session model.UserSession) domain.UserSession {
	return domain.UserSession{
		ID:         session.ID,
		UserID:     session.UserID,
		Terminal:   session.Terminal,
		Token:      session.Token,
		UpdateTime: session.UpdateTime,
		ExpireTime: session.ExpireTime,
		Status:     session.Status,
	}
}

func (r *CacheUserSessionRepo) Create(ctx context.Context, session domain.UserSession) (int64, error) {
	return r.userSessionDao.Create(ctx, r.toModel(session))
}

func (r *CacheUserSessionRepo) Update(ctx context.Context, session domain.UserSession, fields []string) error {
	return r.userSessionDao.Update(ctx, r.toModel(session), fields)
}

func (r *CacheUserSessionRepo) Delete(ctx context.Context, id int64) error {
	return r.userSessionDao.Delete(ctx, id)
}

func (r *CacheUserSessionRepo) GetByID(ctx context.Context, id int64) (domain.UserSession, error) {
	session, err := r.userSessionDao.FindByID(ctx, id)
	if err != nil {
		return domain.UserSession{}, err
	}
	return r.toDomain(session), nil
}

func (r *CacheUserSessionRepo) GetByToken(ctx context.Context, token string) (domain.UserSession, error) {
	session, err := r.userSessionDao.FindByToken(ctx, token)
	if err != nil {
		return domain.UserSession{}, err
	}
	return r.toDomain(session), nil
}

func (r *CacheUserSessionRepo) GetByUserIDAndTerminal(ctx context.Context, userID int64, terminal int8) (domain.UserSession, error) {
	session, err := r.userSessionDao.FindByUserIDAndTerminal(ctx, userID, terminal)
	if err != nil {
		return domain.UserSession{}, err
	}
	return r.toDomain(session), nil
}

func (r *CacheUserSessionRepo) ListByUserID(ctx context.Context, userID int64) ([]domain.UserSession, error) {
	sessions, err := r.userSessionDao.ListByUserID(ctx, userID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.UserSession, 0, len(sessions))
	for _, session := range sessions {
		result = append(result, r.toDomain(session))
	}
	return result, nil
}

func (r *CacheUserSessionRepo) List(ctx context.Context, offset, limit int) ([]domain.UserSession, error) {
	sessions, err := r.userSessionDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.UserSession, 0, len(sessions))
	for _, session := range sessions {
		result = append(result, r.toDomain(session))
	}
	return result, nil
}

func (r *CacheUserSessionRepo) Count(ctx context.Context) (int64, error) {
	return r.userSessionDao.Count(ctx)
}

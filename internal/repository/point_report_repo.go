package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

type PointReportRepo interface {
	// Create 创建积分报告
	Create(ctx context.Context, report domain.PointReport) (int64, error)
	// Update 更新积分报告
	Update(ctx context.Context, report domain.PointReport, fields []string) error
	// Delete 删除积分报告
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询积分报告
	GetByID(ctx context.Context, id int64) (domain.PointReport, error)
	// ListByCity 根据城市查询积分报告列表
	ListByCity(ctx context.Context, city string) ([]domain.PointReport, error)
	// ListByNumber 根据数量查询积分报告列表
	ListByNumber(ctx context.Context, number int) ([]domain.PointReport, error)
	// List 查询积分报告列表
	List(ctx context.Context, offset, limit int) ([]domain.PointReport, error)
	// Count 统计积分报告总数
	Count(ctx context.Context) (int64, error)
}

type CachePointReportRepo struct {
	dao dao.PointReportDao
}

func NewCachePointReportRepo(dao dao.PointReportDao) *CachePointReportRepo {
	return &CachePointReportRepo{
		dao: dao,
	}
}

func (r *CachePointReportRepo) toModel(report domain.PointReport) model.PointReport {
	return model.PointReport{
		ID:                   report.ID,
		City:                 report.City,
		Number:               report.Number,
		WaitCount:            report.WaitCount,
		ExecCount:            report.ExecCount,
		FinishCount:          report.FinishCount,
		ListGatherCount:      report.ListGatherCount,
		DetailGatherCount:    report.DetailGatherCount,
		NewShopGatherCount:   report.NewShopGatherCount,
		BrandGatherCount:     report.BrandGatherCount,
		BrandShopGatherCount: report.BrandShopGatherCount,
		CreateTime:           report.CreateTime,
		UpdateTime:           report.UpdateTime,
	}
}

func (r *CachePointReportRepo) toDomain(report model.PointReport) domain.PointReport {
	return domain.PointReport{
		ID:                   report.ID,
		City:                 report.City,
		Number:               report.Number,
		WaitCount:            report.WaitCount,
		ExecCount:            report.ExecCount,
		FinishCount:          report.FinishCount,
		ListGatherCount:      report.ListGatherCount,
		DetailGatherCount:    report.DetailGatherCount,
		NewShopGatherCount:   report.NewShopGatherCount,
		BrandGatherCount:     report.BrandGatherCount,
		BrandShopGatherCount: report.BrandShopGatherCount,
		CreateTime:           report.CreateTime,
		UpdateTime:           report.UpdateTime,
	}
}

func (r *CachePointReportRepo) Create(ctx context.Context, report domain.PointReport) (int64, error) {
	model := r.toModel(report)
	return r.dao.Create(ctx, model)
}

func (r *CachePointReportRepo) Update(ctx context.Context, report domain.PointReport, fields []string) error {
	model := r.toModel(report)
	return r.dao.Update(ctx, model, fields)
}

func (r *CachePointReportRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CachePointReportRepo) GetByID(ctx context.Context, id int64) (domain.PointReport, error) {
	report, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.PointReport{}, err
	}
	return r.toDomain(report), nil
}

func (r *CachePointReportRepo) ListByCity(ctx context.Context, city string) ([]domain.PointReport, error) {
	reports, err := r.dao.ListByCity(ctx, city)
	if err != nil {
		return nil, err
	}
	result := make([]domain.PointReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CachePointReportRepo) ListByNumber(ctx context.Context, number int) ([]domain.PointReport, error) {
	reports, err := r.dao.ListByNumber(ctx, number)
	if err != nil {
		return nil, err
	}
	result := make([]domain.PointReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CachePointReportRepo) List(ctx context.Context, offset, limit int) ([]domain.PointReport, error) {
	reports, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.PointReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CachePointReportRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ComplaintRecRepo 投诉记录仓储接口
type ComplaintRecRepo interface {
	// Create 创建投诉记录
	Create(ctx context.Context, rec domain.ComplaintRec) (int64, error)
	// Update 更新投诉记录
	Update(ctx context.Context, rec domain.ComplaintRec, fields []string) error
	// Delete 删除投诉记录
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询投诉记录
	FindByID(ctx context.Context, id int64) (domain.ComplaintRec, error)
	// FindByClueID 根据线索ID查询投诉记录
	FindByClueID(ctx context.Context, clueID int64) ([]domain.ComplaintRec, error)
	// FindByProcessorID 根据处理人ID查询投诉记录
	FindByProcessorID(ctx context.Context, processorID int64) ([]domain.ComplaintRec, error)
	// FindByStatus 根据处理状态查询投诉记录
	FindByStatus(ctx context.Context, status int) ([]domain.ComplaintRec, error)
	// FindByDateRange 根据时间范围查询投诉记录
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.ComplaintRec, error)
	// List 查询投诉记录列表
	List(ctx context.Context, offset, limit int) ([]domain.ComplaintRec, error)
	// Count 统计投诉记录总数
	Count(ctx context.Context) (int64, error)
}

type CacheComplaintRecRepo struct {
	complaintRecDao dao.ComplaintRecDao
}

// NewCacheComplaintRecRepo 创建投诉记录仓储实现
func NewCacheComplaintRecRepo(complaintRecDao dao.ComplaintRecDao) *CacheComplaintRecRepo {
	return &CacheComplaintRecRepo{
		complaintRecDao: complaintRecDao,
	}
}

func (r *CacheComplaintRecRepo) Create(ctx context.Context, rec domain.ComplaintRec) (int64, error) {
	return r.complaintRecDao.Create(ctx, r.toModel(rec))
}

func (r *CacheComplaintRecRepo) Update(ctx context.Context, rec domain.ComplaintRec, fields []string) error {
	return r.complaintRecDao.Update(ctx, r.toModel(rec), fields)
}

func (r *CacheComplaintRecRepo) Delete(ctx context.Context, id int64) error {
	return r.complaintRecDao.Delete(ctx, id)
}

func (r *CacheComplaintRecRepo) FindByID(ctx context.Context, id int64) (domain.ComplaintRec, error) {
	model, err := r.complaintRecDao.FindByID(ctx, id)
	if err != nil {
		return domain.ComplaintRec{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheComplaintRecRepo) FindByClueID(ctx context.Context, clueID int64) ([]domain.ComplaintRec, error) {
	models, err := r.complaintRecDao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ComplaintRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheComplaintRecRepo) FindByProcessorID(ctx context.Context, processorID int64) ([]domain.ComplaintRec, error) {
	models, err := r.complaintRecDao.FindByProcessorID(ctx, processorID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ComplaintRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheComplaintRecRepo) FindByStatus(ctx context.Context, status int) ([]domain.ComplaintRec, error) {
	models, err := r.complaintRecDao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ComplaintRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheComplaintRecRepo) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.ComplaintRec, error) {
	models, err := r.complaintRecDao.FindByDateRange(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ComplaintRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheComplaintRecRepo) List(ctx context.Context, offset, limit int) ([]domain.ComplaintRec, error) {
	models, err := r.complaintRecDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ComplaintRec, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheComplaintRecRepo) Count(ctx context.Context) (int64, error) {
	return r.complaintRecDao.Count(ctx)
}

func (r *CacheComplaintRecRepo) toModel(rec domain.ComplaintRec) model.ComplaintRec {
	return model.ComplaintRec{
		ID:               rec.ID,
		ClueID:           rec.ClueID,
		ComplaintType:    rec.ComplaintType,
		ComplaintReason:  rec.ComplaintReason,
		ComplaintContent: rec.ComplaintContent,
		ProcessStatus:    rec.ProcessStatus,
		ProcessResult:    rec.ProcessResult,
		ProcessorID:      rec.ProcessorID,
		ProcessorName:    rec.ProcessorName,
		ProcessTime:      rec.ProcessTime,
		CreatedAt:        rec.CreatedAt,
		UpdatedAt:        rec.UpdatedAt,
		DeletedAt:        rec.DeletedAt,
	}
}

func (r *CacheComplaintRecRepo) toDomain(rec model.ComplaintRec) domain.ComplaintRec {
	return domain.ComplaintRec{
		ID:               rec.ID,
		ClueID:           rec.ClueID,
		ComplaintType:    rec.ComplaintType,
		ComplaintReason:  rec.ComplaintReason,
		ComplaintContent: rec.ComplaintContent,
		ProcessStatus:    rec.ProcessStatus,
		ProcessResult:    rec.ProcessResult,
		ProcessorID:      rec.ProcessorID,
		ProcessorName:    rec.ProcessorName,
		ProcessTime:      rec.ProcessTime,
		CreatedAt:        rec.CreatedAt,
		UpdatedAt:        rec.UpdatedAt,
		DeletedAt:        rec.DeletedAt,
	}
}

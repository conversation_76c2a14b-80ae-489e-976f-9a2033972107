package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// DingRepo 钉钉配置仓储接口
type DingRepo interface {
	// Create 创建钉钉配置
	Create(ctx context.Context, ding domain.Ding) (int64, error)
	// Update 更新钉钉配置
	Update(ctx context.Context, ding domain.Ding, fields []string) error
	// Delete 删除钉钉配置
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询钉钉配置
	FindByID(ctx context.Context, id int64) (domain.Ding, error)
	// FindByAppKey 根据AppKey查询钉钉配置
	FindByAppKey(ctx context.Context, appKey string) (domain.Ding, error)
	// FindByCorpID 根据企业ID查询钉钉配置
	FindByCorpID(ctx context.Context, corpID string) (domain.Ding, error)
	// FindByStatus 根据状态查询钉钉配置列表
	FindByStatus(ctx context.Context, status int) ([]domain.Ding, error)
	// List 查询钉钉配置列表
	List(ctx context.Context, offset, limit int) ([]domain.Ding, error)
	// Count 统计钉钉配置总数
	Count(ctx context.Context) (int64, error)
}

type CacheDingRepo struct {
	dingDao dao.DingDao
}

// NewCacheDingRepo 创建钉钉配置仓储实现
func NewCacheDingRepo(dingDao dao.DingDao) *CacheDingRepo {
	return &CacheDingRepo{
		dingDao: dingDao,
	}
}

func (r *CacheDingRepo) Create(ctx context.Context, ding domain.Ding) (int64, error) {
	return r.dingDao.Create(ctx, r.toModel(ding))
}

func (r *CacheDingRepo) Update(ctx context.Context, ding domain.Ding, fields []string) error {
	return r.dingDao.Update(ctx, r.toModel(ding), fields)
}

func (r *CacheDingRepo) Delete(ctx context.Context, id int64) error {
	return r.dingDao.Delete(ctx, id)
}

func (r *CacheDingRepo) FindByID(ctx context.Context, id int64) (domain.Ding, error) {
	model, err := r.dingDao.FindByID(ctx, id)
	if err != nil {
		return domain.Ding{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDingRepo) FindByAppKey(ctx context.Context, appKey string) (domain.Ding, error) {
	model, err := r.dingDao.FindByAppKey(ctx, appKey)
	if err != nil {
		return domain.Ding{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDingRepo) FindByCorpID(ctx context.Context, corpID string) (domain.Ding, error) {
	model, err := r.dingDao.FindByCorpID(ctx, corpID)
	if err != nil {
		return domain.Ding{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheDingRepo) FindByStatus(ctx context.Context, status int) ([]domain.Ding, error) {
	models, err := r.dingDao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	result := make([]domain.Ding, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDingRepo) List(ctx context.Context, offset, limit int) ([]domain.Ding, error) {
	models, err := r.dingDao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.Ding, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheDingRepo) Count(ctx context.Context) (int64, error) {
	return r.dingDao.Count(ctx)
}

func (r *CacheDingRepo) toModel(ding domain.Ding) model.Ding {
	return model.Ding{
		ID:          ding.ID,
		AppKey:      ding.AppKey,
		AppSecret:   ding.AppSecret,
		AgentID:     ding.AgentID,
		CorpID:      ding.CorpID,
		Status:      ding.Status,
		Description: ding.Description,
		CreatorID:   ding.CreatorID,
		CreatorName: ding.CreatorName,
		CreatedAt:   ding.CreatedAt,
		UpdatedAt:   ding.UpdatedAt,
		DeletedAt:   ding.DeletedAt,
	}
}

func (r *CacheDingRepo) toDomain(ding model.Ding) domain.Ding {
	return domain.Ding{
		ID:          ding.ID,
		AppKey:      ding.AppKey,
		AppSecret:   ding.AppSecret,
		AgentID:     ding.AgentID,
		CorpID:      ding.CorpID,
		Status:      ding.Status,
		Description: ding.Description,
		CreatorID:   ding.CreatorID,
		CreatorName: ding.CreatorName,
		CreatedAt:   ding.CreatedAt,
		UpdatedAt:   ding.UpdatedAt,
		DeletedAt:   ding.DeletedAt,
	}
}

package repository

import (
	"context"
	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
	"time"
)

// CallRepo 通话记录仓储接口
type CallRepo interface {
	// Create 创建通话记录
	Create(ctx context.Context, record domain.CallRecord) (int64, error)
	// Update 更新通话记录
	Update(ctx context.Context, record domain.CallRecord, fields []string) error
	// GetByOrderSn 根据订单编号获取通话记录
	GetByOrderSn(ctx context.Context, orderSn string) (domain.CallRecord, error)
	// GetByOrderId 根据外部订单编号获取通话记录
	GetByOrderId(ctx context.Context, orderId string) (domain.CallRecord, error)
	// ListByClueID 根据线索ID获取通话记录列表
	ListByClueID(ctx context.Context, clueID int64) ([]domain.CallRecord, error)
}

type CacheCallRepo struct {
	dao dao.CallRecordDao
}

// NewCacheCallRepo 创建通话记录仓储
func NewCacheCallRepo(dao dao.CallRecordDao) *CacheCallRepo {
	return &CacheCallRepo{
		dao: dao,
	}
}

// Create 创建通话记录
func (r *CacheCallRepo) Create(ctx context.Context, record domain.CallRecord) (int64, error) {
	return r.dao.Create(ctx, r.toModel(record))
}

// Update 更新通话记录
func (r *CacheCallRepo) Update(ctx context.Context, record domain.CallRecord, fields []string) error {
	return r.dao.Update(ctx, r.toModel(record), fields)
}

// GetByOrderSn 根据订单编号获取通话记录
func (r *CacheCallRepo) GetByOrderSn(ctx context.Context, orderSn string) (domain.CallRecord, error) {
	record, err := r.dao.FindByOrderSn(ctx, orderSn)
	if err != nil {
		return domain.CallRecord{}, err
	}
	return r.toDomain(record), nil
}

// GetByOrderId 根据订单编号获取通话记录
func (r *CacheCallRepo) GetByOrderId(ctx context.Context, orderId string) (domain.CallRecord, error) {
	record, err := r.dao.FindByOrderId(ctx, orderId)
	if err != nil {
		return domain.CallRecord{}, err
	}
	return r.toDomain(record), nil
}

// ListByClueID 根据线索ID获取通话记录列表
func (r *CacheCallRepo) ListByClueID(ctx context.Context, clueID int64) ([]domain.CallRecord, error) {
	records, err := r.dao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	return r.toDomainList(records), nil
}

// toModel 领域对象转换为模型对象
func (r *CacheCallRepo) toModel(record domain.CallRecord) model.CallRecord {
	startTime := int64(0)
	if !record.StartTime.IsZero() {
		startTime = record.StartTime.Unix()
	}
	endTime := int64(0)
	if !record.EndTime.IsZero() {
		endTime = record.EndTime.Unix()
	}
	return model.CallRecord{
		ID:          record.ID,
		Caller:      record.Caller,
		Callee:      record.Callee,
		UserID:      record.UserID,
		ClueID:      record.ClueID,
		OrderSn:     record.OrderSn,
		TelID:       record.TelID,
		ChannelType: record.ChannelType.Int8(),
		StartTime:   startTime,
		EndTime:     endTime,
		HoldTime:    record.HoldTime,
		Status:      record.Status.Int8(),
		ErrorMsg:    record.ErrorMsg,
		OriAudioURL: record.OriAudioURL,
		AudioURL:    record.AudioURL,
		ShowNo:      record.ShowNo,
		OrderID:     record.OrderID,
		EventType:   record.EventType,
		BindId:      record.BindId,
		CallType:    record.CallType,
		CreateTime:  record.CreateTime.Unix(),
		UpdateTime:  record.UpdateTime.Unix(),
	}
}

// toDomain 模型对象转换为领域对象
func (r *CacheCallRepo) toDomain(record model.CallRecord) domain.CallRecord {
	return domain.CallRecord{
		ID:          record.ID,
		Caller:      record.Caller,
		Callee:      record.Callee,
		UserID:      record.UserID,
		ClueID:      record.ClueID,
		OrderSn:     record.OrderSn,
		TelID:       record.TelID,
		ChannelType: domain.CallChannel(record.ChannelType),
		StartTime:   time.Unix(record.StartTime, 0),
		EndTime:     time.Unix(record.EndTime, 0),
		HoldTime:    record.HoldTime,
		Status:      domain.CallRecordStatus(record.Status),
		ErrorMsg:    record.ErrorMsg,
		OriAudioURL: record.OriAudioURL,
		AudioURL:    record.AudioURL,
		ShowNo:      record.ShowNo,
		OrderID:     record.OrderID,
		EventType:   record.EventType,
		BindId:      record.BindId,
		CallType:    record.CallType,
	}
}

// toDomainList 模型对象列表转换为领域对象列表
func (r *CacheCallRepo) toDomainList(records []model.CallRecord) []domain.CallRecord {
	result := make([]domain.CallRecord, 0, len(records))
	for _, record := range records {
		result = append(result, r.toDomain(record))
	}
	return result
}

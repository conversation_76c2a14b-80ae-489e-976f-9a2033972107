package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueReportRepo 线索报告仓储接口
type ClueReportRepo interface {
	// Create 创建线索报告
	Create(ctx context.Context, report domain.ClueReport) error
	// Update 更新线索报告
	Update(ctx context.Context, report domain.ClueReport) error
	// Delete 删除线索报告
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询线索报告
	GetByID(ctx context.Context, id int64) (domain.ClueReport, error)
	// ListByClueID 根据线索ID查询线索报告列表
	ListByClueID(ctx context.Context, clueID int64) ([]domain.ClueReport, error)
	// ListByType 根据报告类型查询线索报告列表
	ListByType(ctx context.Context, reportType int) ([]domain.ClueReport, error)
	// List 分页查询线索报告列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueReport, error)
	// Count 统计线索报告总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueReportRepo struct {
	dao dao.ClueReportDao
}

// NewCacheClueReportRepo 创建线索报告仓储实现
func NewCacheClueReportRepo(dao dao.ClueReportDao) *CacheClueReportRepo {
	return &CacheClueReportRepo{dao: dao}
}

func (r *CacheClueReportRepo) toModel(report domain.ClueReport) model.ClueReport {
	return model.ClueReport{
		ID:          report.ID,
		ClueID:      report.ClueID,
		Type:        report.Type,
		Content:     report.Content,
		CreatorID:   report.CreatorID,
		CreatorName: report.CreatorName,
		Status:      report.Status,
		CreatedAt:   report.CreatedAt,
		UpdatedAt:   report.UpdatedAt,
		DeletedAt:   report.DeletedAt,
	}
}

func (r *CacheClueReportRepo) toDomain(report model.ClueReport) domain.ClueReport {
	return domain.ClueReport{
		ID:          report.ID,
		ClueID:      report.ClueID,
		Type:        report.Type,
		Content:     report.Content,
		CreatorID:   report.CreatorID,
		CreatorName: report.CreatorName,
		Status:      report.Status,
		CreatedAt:   report.CreatedAt,
		UpdatedAt:   report.UpdatedAt,
		DeletedAt:   report.DeletedAt,
	}
}

func (r *CacheClueReportRepo) Create(ctx context.Context, report domain.ClueReport) error {
	_, err := r.dao.Create(ctx, r.toModel(report))
	return err
}

func (r *CacheClueReportRepo) Update(ctx context.Context, report domain.ClueReport) error {
	return r.dao.Update(ctx, r.toModel(report), []string{"clue_id", "type", "content", "creator_id", "creator_name", "status", "updated_at"})
}

func (r *CacheClueReportRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheClueReportRepo) GetByID(ctx context.Context, id int64) (domain.ClueReport, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueReport{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueReportRepo) ListByClueID(ctx context.Context, clueID int64) ([]domain.ClueReport, error) {
	models, err := r.dao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueReport, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueReportRepo) ListByType(ctx context.Context, reportType int) ([]domain.ClueReport, error) {
	models, err := r.dao.FindByType(ctx, reportType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueReport, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueReportRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueReport, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ClueReport, len(models))
	for i, m := range models {
		result[i] = r.toDomain(m)
	}
	return result, nil
}

func (r *CacheClueReportRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

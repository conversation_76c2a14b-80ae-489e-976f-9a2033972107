package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// MessageRepo 消息仓储接口
type MessageRepo interface {
	// Create 创建消息
	Create(ctx context.Context, message domain.Message) error
	// Update 更新消息
	Update(ctx context.Context, message domain.Message) error
	// Delete 删除消息
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询消息
	FindByID(ctx context.Context, id int64) (domain.Message, error)
	// FindByReceiverID 根据接收人ID查询消息列表
	FindByReceiverID(ctx context.Context, receiverID int64) ([]domain.Message, error)
	// FindBySenderID 根据发送人ID查询消息列表
	FindBySenderID(ctx context.Context, senderID int64) ([]domain.Message, error)
	// FindByStatus 根据状态查询消息列表
	FindByStatus(ctx context.Context, status int) ([]domain.Message, error)
	// FindByType 根据类型查询消息列表
	FindByType(ctx context.Context, messageType int) ([]domain.Message, error)
	// FindByPriority 根据优先级查询消息列表
	FindByPriority(ctx context.Context, priority int) ([]domain.Message, error)
	// FindByDateRange 根据时间范围查询消息列表
	FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.Message, error)
	// List 查询消息列表
	List(ctx context.Context, offset, limit int) ([]domain.Message, error)
	// Count 统计消息总数
	Count(ctx context.Context) (int64, error)
}

type CacheMessageRepo struct {
	dao dao.MessageDao
}

// NewCacheMessageRepo 创建消息仓储实现
func NewCacheMessageRepo(dao dao.MessageDao) *CacheMessageRepo {
	return &CacheMessageRepo{
		dao: dao,
	}
}

func (r *CacheMessageRepo) toModel(message domain.Message) model.Message {
	return model.Message{
		ID:           message.ID,
		MessageType:  int64(message.MessageType),
		Title:        message.Title,
		Content:      message.Content,
		Status:       int64(message.Status),
		Priority:     int64(message.Priority),
		ReceiverID:   message.ReceiverID,
		ReceiverName: message.ReceiverName,
		SenderID:     message.SenderID,
		SenderName:   message.SenderName,
		ReadTime:     message.ReadTime,
		CreatedAt:    message.CreatedAt,
		UpdatedAt:    message.UpdatedAt,
		DeletedAt:    message.DeletedAt,
	}
}

func (r *CacheMessageRepo) toDomain(model model.Message) domain.Message {
	return domain.Message{
		ID:           model.ID,
		MessageType:  int(model.MessageType),
		Title:        model.Title,
		Content:      model.Content,
		Status:       int(model.Status),
		Priority:     int(model.Priority),
		ReceiverID:   model.ReceiverID,
		ReceiverName: model.ReceiverName,
		SenderID:     model.SenderID,
		SenderName:   model.SenderName,
		ReadTime:     model.ReadTime,
		CreatedAt:    model.CreatedAt,
		UpdatedAt:    model.UpdatedAt,
		DeletedAt:    model.DeletedAt,
	}
}

func (r *CacheMessageRepo) toDomains(models []model.Message) []domain.Message {
	domains := make([]domain.Message, len(models))
	for i, m := range models {
		domains[i] = r.toDomain(m)
	}
	return domains
}

func (r *CacheMessageRepo) Create(ctx context.Context, message domain.Message) error {
	_, err := r.dao.Create(ctx, r.toModel(message))
	return err
}

func (r *CacheMessageRepo) Update(ctx context.Context, message domain.Message) error {
	return r.dao.Update(ctx, r.toModel(message), []string{"message_type", "title", "content", "status", "priority", "receiver_id", "receiver_name", "sender_id", "sender_name", "read_time", "updated_at"})
}

func (r *CacheMessageRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheMessageRepo) FindByID(ctx context.Context, id int64) (domain.Message, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.Message{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheMessageRepo) FindByReceiverID(ctx context.Context, receiverID int64) ([]domain.Message, error) {
	models, err := r.dao.FindByReceiverID(ctx, receiverID)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) FindBySenderID(ctx context.Context, senderID int64) ([]domain.Message, error) {
	models, err := r.dao.FindBySenderID(ctx, senderID)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) FindByStatus(ctx context.Context, status int) ([]domain.Message, error) {
	models, err := r.dao.FindByStatus(ctx, int64(status))
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) FindByType(ctx context.Context, messageType int) ([]domain.Message, error) {
	models, err := r.dao.FindByType(ctx, int64(messageType))
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) FindByPriority(ctx context.Context, priority int) ([]domain.Message, error) {
	models, err := r.dao.FindByPriority(ctx, int64(priority))
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) FindByDateRange(ctx context.Context, startTime, endTime time.Time) ([]domain.Message, error) {
	models, err := r.dao.FindByDateRange(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) List(ctx context.Context, offset, limit int) ([]domain.Message, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	return r.toDomains(models), nil
}

func (r *CacheMessageRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ClueAuditRepo 线索审核仓储接口
type ClueAuditRepo interface {
	// Create 创建线索审核
	Create(ctx context.Context, audit domain.ClueAudit) (int64, error)
	// Update 更新线索审核
	Update(ctx context.Context, audit domain.ClueAudit, fields []string) error
	// Delete 删除线索审核
	Delete(ctx context.Context, id int64) error
	// FindByID 根据ID查询线索审核
	FindByID(ctx context.Context, id int64) (domain.ClueAudit, error)
	// FindByClueID 根据线索ID查询线索审核列表
	FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueAudit, error)
	// FindByType 根据审核类型查询线索审核列表
	FindByType(ctx context.Context, auditType int) ([]domain.ClueAudit, error)
	// FindByStatus 根据审核状态查询线索审核列表
	FindByStatus(ctx context.Context, status int) ([]domain.ClueAudit, error)
	// List 分页查询线索审核列表
	List(ctx context.Context, offset, limit int) ([]domain.ClueAudit, error)
	// Count 统计线索审核总数
	Count(ctx context.Context) (int64, error)
}

type CacheClueAuditRepo struct {
	dao dao.ClueAuditDao
}

// NewCacheClueAuditRepo 创建线索审核仓储实现
func NewCacheClueAuditRepo(dao dao.ClueAuditDao) *CacheClueAuditRepo {
	return &CacheClueAuditRepo{
		dao: dao,
	}
}

func (r *CacheClueAuditRepo) Create(ctx context.Context, audit domain.ClueAudit) (int64, error) {
	return r.dao.Create(ctx, r.toModel(audit))
}

func (r *CacheClueAuditRepo) Update(ctx context.Context, audit domain.ClueAudit, fields []string) error {
	return r.dao.Update(ctx, r.toModel(audit), fields)
}

func (r *CacheClueAuditRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheClueAuditRepo) FindByID(ctx context.Context, id int64) (domain.ClueAudit, error) {
	model, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ClueAudit{}, err
	}
	return r.toDomain(model), nil
}

func (r *CacheClueAuditRepo) FindByClueID(ctx context.Context, clueID int64) ([]domain.ClueAudit, error) {
	models, err := r.dao.FindByClueID(ctx, clueID)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueAudit
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueAuditRepo) FindByType(ctx context.Context, auditType int) ([]domain.ClueAudit, error) {
	models, err := r.dao.FindByType(ctx, auditType)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueAudit
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueAuditRepo) FindByStatus(ctx context.Context, status int) ([]domain.ClueAudit, error) {
	models, err := r.dao.FindByStatus(ctx, status)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueAudit
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueAuditRepo) List(ctx context.Context, offset, limit int) ([]domain.ClueAudit, error) {
	models, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	var result []domain.ClueAudit
	for _, model := range models {
		result = append(result, r.toDomain(model))
	}
	return result, nil
}

func (r *CacheClueAuditRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

func (r *CacheClueAuditRepo) toModel(audit domain.ClueAudit) model.ClueAudit {
	return model.ClueAudit{
		ID:          audit.ID,
		CreatedAt:   audit.CreatedAt,
		UpdatedAt:   audit.UpdatedAt,
		DeletedAt:   audit.DeletedAt,
		ClueID:      audit.ClueID,
		Type:        audit.Type,
		Status:      audit.Status,
		Remark:      audit.Remark,
		AuditorID:   audit.AuditorID,
		AuditorName: audit.AuditorName,
		CreatorID:   audit.CreatorID,
		CreatorName: audit.CreatorName,
	}
}

func (r *CacheClueAuditRepo) toDomain(audit model.ClueAudit) domain.ClueAudit {
	return domain.ClueAudit{
		ID:          audit.ID,
		CreatedAt:   audit.CreatedAt,
		UpdatedAt:   audit.UpdatedAt,
		DeletedAt:   audit.DeletedAt,
		ClueID:      audit.ClueID,
		Type:        audit.Type,
		Status:      audit.Status,
		Remark:      audit.Remark,
		AuditorID:   audit.AuditorID,
		AuditorName: audit.AuditorName,
		CreatorID:   audit.CreatorID,
		CreatorName: audit.CreatorName,
	}
}

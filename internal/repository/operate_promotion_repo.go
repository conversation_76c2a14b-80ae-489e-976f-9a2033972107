package repository

import (
	"context"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// OperatePromotionRepo 运营推广仓储接口
type OperatePromotionRepo interface {
	// Create 创建运营推广
	Create(ctx context.Context, promotion domain.OperatePromotion) (int64, error)
	// Update 更新运营推广
	Update(ctx context.Context, promotion domain.OperatePromotion, fields []string) error
	// Delete 删除运营推广
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询运营推广
	GetByID(ctx context.Context, id int64) (domain.OperatePromotion, error)
	// List 分页查询运营推广列表
	List(ctx context.Context, offset, limit int) ([]domain.OperatePromotion, error)
	// ListByType 根据类型查询运营推广列表
	ListByType(ctx context.Context, promotionType int8) ([]domain.OperatePromotion, error)
	// Count 统计运营推广总数
	Count(ctx context.Context) (int64, error)
}

type CacheOperatePromotionRepo struct {
	dao dao.OperatePromotionDao
}

func NewCacheOperatePromotionRepo(dao dao.OperatePromotionDao) *CacheOperatePromotionRepo {
	return &CacheOperatePromotionRepo{dao: dao}
}

func (r *CacheOperatePromotionRepo) toModel(promotion domain.OperatePromotion) model.OperatePromotion {
	return model.OperatePromotion{
		ID:         promotion.ID,
		Title:      promotion.Title,
		Content:    promotion.Content,
		Img:        promotion.Img,
		Type:       promotion.Type,
		CreateTime: promotion.CreateTime,
		UpdateTime: promotion.UpdateTime,
		PV:         promotion.PV,
	}
}

func (r *CacheOperatePromotionRepo) toDomain(promotion model.OperatePromotion) domain.OperatePromotion {
	return domain.OperatePromotion{
		ID:         promotion.ID,
		Title:      promotion.Title,
		Content:    promotion.Content,
		Img:        promotion.Img,
		Type:       promotion.Type,
		CreateTime: promotion.CreateTime,
		UpdateTime: promotion.UpdateTime,
		PV:         promotion.PV,
	}
}

func (r *CacheOperatePromotionRepo) Create(ctx context.Context, promotion domain.OperatePromotion) (int64, error) {
	model := r.toModel(promotion)
	return r.dao.Create(ctx, model)
}

func (r *CacheOperatePromotionRepo) Update(ctx context.Context, promotion domain.OperatePromotion, fields []string) error {
	model := r.toModel(promotion)
	return r.dao.Update(ctx, model, fields)
}

func (r *CacheOperatePromotionRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheOperatePromotionRepo) GetByID(ctx context.Context, id int64) (domain.OperatePromotion, error) {
	promotion, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.OperatePromotion{}, err
	}
	return r.toDomain(promotion), nil
}

func (r *CacheOperatePromotionRepo) List(ctx context.Context, offset, limit int) ([]domain.OperatePromotion, error) {
	promotions, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperatePromotion, len(promotions))
	for i, promotion := range promotions {
		result[i] = r.toDomain(promotion)
	}
	return result, nil
}

func (r *CacheOperatePromotionRepo) ListByType(ctx context.Context, promotionType int8) ([]domain.OperatePromotion, error) {
	promotions, err := r.dao.ListByType(ctx, promotionType)
	if err != nil {
		return nil, err
	}
	result := make([]domain.OperatePromotion, len(promotions))
	for i, promotion := range promotions {
		result[i] = r.toDomain(promotion)
	}
	return result, nil
}

func (r *CacheOperatePromotionRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package repository

import (
	"context"
	"time"

	"enter/internal/domain"
	"enter/internal/repository/dao"
	"enter/internal/repository/dao/model"
)

// ShopReportRepo 店铺报告仓储接口
type ShopReportRepo interface {
	// Create 创建店铺报告
	Create(ctx context.Context, report domain.ShopReport) (int64, error)
	// Update 更新店铺报告
	Update(ctx context.Context, report domain.ShopReport, fields []string) error
	// Delete 删除店铺报告
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID查询店铺报告
	GetByID(ctx context.Context, id int64) (domain.ShopReport, error)
	// ListByShopID 根据店铺ID查询店铺报告列表
	ListByShopID(ctx context.Context, shopID int64) ([]domain.ShopReport, error)
	// ListByShopCode 根据店铺编码查询店铺报告列表
	ListByShopCode(ctx context.Context, shopCode string) ([]domain.ShopReport, error)
	// ListByEmployeeID 根据员工ID查询店铺报告列表
	ListByEmployeeID(ctx context.Context, employeeID int) ([]domain.ShopReport, error)
	// ListByCreateTime 根据创建时间查询店铺报告列表
	ListByCreateTime(ctx context.Context, startTime, endTime time.Time) ([]domain.ShopReport, error)
	// ListByBrandName 根据品牌名称查询店铺报告列表
	ListByBrandName(ctx context.Context, brandName string) ([]domain.ShopReport, error)
	// List 分页查询店铺报告列表
	List(ctx context.Context, offset, limit int) ([]domain.ShopReport, error)
	// Count 统计店铺报告总数
	Count(ctx context.Context) (int64, error)
}

type CacheShopReportRepo struct {
	dao dao.ShopReportDao
}

func NewCacheShopReportRepo(dao dao.ShopReportDao) *CacheShopReportRepo {
	return &CacheShopReportRepo{
		dao: dao,
	}
}

func (r *CacheShopReportRepo) toModel(report domain.ShopReport) model.ShopReport {
	return model.ShopReport{
		ID:                      report.ID,
		ShopID:                  report.ShopID,
		ShopCode:                report.ShopCode,
		ShopName:                report.ShopName,
		BrandName:               report.BrandName,
		UV:                      report.UV,
		PromoteTaokeNum:         report.PromoteTaokeNum,
		PayAmount:               report.PayAmount,
		PayNum:                  report.PayNum,
		OrderOriginalServiceFee: report.OrderOriginalServiceFee,
		OrderServicesFee:        report.OrderServicesFee,
		UserSettleAmount:        report.UserSettleAmount,
		ShopSettleAmount:        report.ShopSettleAmount,
		SettleNum:               report.SettleNum,
		OriginalServiceFee:      report.OriginalServiceFee,
		SettleFee:               report.SettleFee,
		CityServiceFee:          report.CityServiceFee,
		CitySettleFee:           report.CitySettleFee,
		OrderServiceFee:         report.OrderServiceFee,
		OrderSettleFee:          report.OrderSettleFee,
		City:                    report.City,
		District:                report.District,
		BusinessDistrict:        report.BusinessDistrict,
		CreateTime:              report.CreateTime,
		EmployeeID:              report.EmployeeID,
		IsNewShop:               report.IsNewShop,
		Timeline:                report.Timeline,
		NewSignature:            report.NewSignature,
		Type:                    report.Type,
		OfficialUV:              report.OfficialUV,
		OfficialSettleNum:       report.OfficialSettleNum,
		OfficialSettleFee:       report.OfficialSettleFee,
	}
}

func (r *CacheShopReportRepo) toDomain(report model.ShopReport) domain.ShopReport {
	return domain.ShopReport{
		ID:                      report.ID,
		ShopID:                  report.ShopID,
		ShopCode:                report.ShopCode,
		ShopName:                report.ShopName,
		BrandName:               report.BrandName,
		UV:                      report.UV,
		PromoteTaokeNum:         report.PromoteTaokeNum,
		PayAmount:               report.PayAmount,
		PayNum:                  report.PayNum,
		OrderOriginalServiceFee: report.OrderOriginalServiceFee,
		OrderServicesFee:        report.OrderServicesFee,
		UserSettleAmount:        report.UserSettleAmount,
		ShopSettleAmount:        report.ShopSettleAmount,
		SettleNum:               report.SettleNum,
		OriginalServiceFee:      report.OriginalServiceFee,
		SettleFee:               report.SettleFee,
		CityServiceFee:          report.CityServiceFee,
		CitySettleFee:           report.CitySettleFee,
		OrderServiceFee:         report.OrderServiceFee,
		OrderSettleFee:          report.OrderSettleFee,
		City:                    report.City,
		District:                report.District,
		BusinessDistrict:        report.BusinessDistrict,
		CreateTime:              report.CreateTime,
		EmployeeID:              report.EmployeeID,
		IsNewShop:               report.IsNewShop,
		Timeline:                report.Timeline,
		NewSignature:            report.NewSignature,
		Type:                    report.Type,
		OfficialUV:              report.OfficialUV,
		OfficialSettleNum:       report.OfficialSettleNum,
		OfficialSettleFee:       report.OfficialSettleFee,
	}
}

func (r *CacheShopReportRepo) Create(ctx context.Context, report domain.ShopReport) (int64, error) {
	return r.dao.Create(ctx, r.toModel(report))
}

func (r *CacheShopReportRepo) Update(ctx context.Context, report domain.ShopReport, fields []string) error {
	return r.dao.Update(ctx, r.toModel(report), fields)
}

func (r *CacheShopReportRepo) Delete(ctx context.Context, id int64) error {
	return r.dao.Delete(ctx, id)
}

func (r *CacheShopReportRepo) GetByID(ctx context.Context, id int64) (domain.ShopReport, error) {
	report, err := r.dao.FindByID(ctx, id)
	if err != nil {
		return domain.ShopReport{}, err
	}
	return r.toDomain(report), nil
}

func (r *CacheShopReportRepo) ListByShopID(ctx context.Context, shopID int64) ([]domain.ShopReport, error) {
	reports, err := r.dao.ListByShopID(ctx, shopID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) ListByShopCode(ctx context.Context, shopCode string) ([]domain.ShopReport, error) {
	reports, err := r.dao.ListByShopCode(ctx, shopCode)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) ListByEmployeeID(ctx context.Context, employeeID int) ([]domain.ShopReport, error) {
	reports, err := r.dao.ListByEmployeeID(ctx, employeeID)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) ListByCreateTime(ctx context.Context, startTime, endTime time.Time) ([]domain.ShopReport, error) {
	reports, err := r.dao.ListByCreateTime(ctx, startTime, endTime)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) ListByBrandName(ctx context.Context, brandName string) ([]domain.ShopReport, error) {
	reports, err := r.dao.ListByBrandName(ctx, brandName)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) List(ctx context.Context, offset, limit int) ([]domain.ShopReport, error) {
	reports, err := r.dao.List(ctx, offset, limit)
	if err != nil {
		return nil, err
	}
	result := make([]domain.ShopReport, len(reports))
	for i, report := range reports {
		result[i] = r.toDomain(report)
	}
	return result, nil
}

func (r *CacheShopReportRepo) Count(ctx context.Context) (int64, error) {
	return r.dao.Count(ctx)
}

package errs

// BizError 业务错误
type BizError struct {
	message string
}

// Error 实现 error 接口
func (e *BizError) Error() string {
	return e.message
}

// NewBizError 创建业务错误
func NewBizError(message string) error {
	return &BizError{
		message: message,
	}
}

// IsBizError 判断是否是业务错误
func IsBizError(err error) bool {
	_, ok := err.(*BizError)
	return ok
}

// GetBizErrorMessage 获取业务错误信息
func GetBizErrorMessage(err error) string {
	if bizErr, ok := err.(*BizError); ok {
		return bizErr.message
	}
	return ""
}

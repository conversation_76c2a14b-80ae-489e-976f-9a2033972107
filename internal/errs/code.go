package errs

const (
	CommonInputValid          = 400001
	CommonInternalServerError = 500001

	// UserInputValid 用户模块 01
	UserInputValid = 401001
	// UserInvalidOrPassword 用户不存在或者密码错误
	UserInvalidOrPassword   = 401002
	UserForbidden           = 401003
	UserInternalServerError = 501001
)

// 通用错误码
const (
	OK           = 0   // 成功
	InvalidParam = 400 // 参数错误
	Unauthorized = 401 // 未授权
	Forbidden    = 403 // 禁止访问
	NotFound     = 404 // 资源不存在
	ServerError  = 500 // 服务器内部错误
)

// Error 自定义错误
type Error struct {
	Code    int
	Message string
}

// NewError 创建自定义错误
func NewError(code int, message string) *Error {
	return &Error{
		Code:    code,
		Message: message,
	}
}

// Error 实现error接口
func (e *Error) Error() string {
	return e.Message
}

// Is 实现errors.Is接口
func (e *Error) Is(target error) bool {
	t, ok := target.(*Error)
	if !ok {
		return false
	}
	return e.Code == t.Code
}

package service

import (
	"context"
	"fmt"

	"enter/internal/domain"
	"enter/internal/repository"
	"enter/pkg/dingtalk"
)

// DingTalkSvc 钉钉消息服务接口
type DingTalkSvc interface {
	// SendTextMessage 发送文本消息
	SendTextMessage(ctx context.Context, dingID int64, content string, atUsers ...string) error
	// SendMarkdownMessage 发送Markdown消息
	SendMarkdownMessage(ctx context.Context, dingID int64, title, text string, atUsers ...string) error
	// SendLinkMessage 发送链接消息
	SendLinkMessage(ctx context.Context, dingID int64, title, text, messageURL, picURL string) error
	// SendActionCardMessage 发送ActionCard消息
	SendActionCardMessage(ctx context.Context, dingID int64, title, text, singleTitle, singleURL string) error
	// SendMessage 发送自定义消息
	SendMessage(ctx context.Context, dingID int64, message dingtalk.Message) error
	// BatchSendTextMessage 批量发送文本消息
	BatchSendTextMessage(ctx context.Context, dingIDs []int64, content string, atUsers ...string) []error
	// BatchSendMessage 批量发送自定义消息
	BatchSendMessage(ctx context.Context, dingIDs []int64, message dingtalk.Message) []error
}

// dingTalkSvc 钉钉消息服务实现
type dingTalkSvc struct {
	dingRepo repository.DingRepo
	config   *dingtalk.Config
}

// NewDingTalkService 创建钉钉消息服务
func NewDingTalkService(dingRepo repository.DingRepo, opts ...dingtalk.Option) DingTalkSvc {
	config := dingtalk.DefaultConfig()
	for _, opt := range opts {
		opt(config)
	}

	return &dingTalkSvc{
		dingRepo: dingRepo,
		config:   config,
	}
}

// SendTextMessage 发送文本消息
func (s *dingTalkSvc) SendTextMessage(ctx context.Context, dingID int64, content string, atUsers ...string) error {
	message := &dingtalk.TextMessage{
		Content: content,
		AtUsers: atUsers,
	}
	return s.SendMessage(ctx, dingID, message)
}

// SendMarkdownMessage 发送Markdown消息
func (s *dingTalkSvc) SendMarkdownMessage(ctx context.Context, dingID int64, title, text string, atUsers ...string) error {
	message := &dingtalk.MarkdownMessage{
		Title:   title,
		Text:    text,
		AtUsers: atUsers,
	}
	return s.SendMessage(ctx, dingID, message)
}

// SendLinkMessage 发送链接消息
func (s *dingTalkSvc) SendLinkMessage(ctx context.Context, dingID int64, title, text, messageURL, picURL string) error {
	message := &dingtalk.LinkMessage{
		Title:      title,
		Text:       text,
		MessageURL: messageURL,
		PicURL:     picURL,
	}
	return s.SendMessage(ctx, dingID, message)
}

// SendActionCardMessage 发送ActionCard消息
func (s *dingTalkSvc) SendActionCardMessage(ctx context.Context, dingID int64, title, text, singleTitle, singleURL string) error {
	message := &dingtalk.ActionCardMessage{
		Title:       title,
		Text:        text,
		SingleTitle: singleTitle,
		SingleURL:   singleURL,
	}
	return s.SendMessage(ctx, dingID, message)
}

// SendMessage 发送自定义消息
func (s *dingTalkSvc) SendMessage(ctx context.Context, dingID int64, message dingtalk.Message) error {
	if dingID <= 0 {
		return dingtalk.ErrInvalidDingID
	}

	// 获取钉钉配置
	dingConfig, err := s.getDingConfig(ctx, dingID)
	if err != nil {
		return fmt.Errorf("获取钉钉配置失败: %w", err)
	}

	// 构建Webhook URL
	webhookURL := fmt.Sprintf("https://oapi.dingtalk.com/robot/send?access_token=%s", dingConfig.AccessToken)

	// 创建客户端
	client := dingtalk.NewClient(
		dingtalk.WithWebhookURL(webhookURL),
		dingtalk.WithSecret(dingConfig.Secret),
		dingtalk.WithTimeout(s.config.Timeout),
		dingtalk.WithRetry(s.config.RetryCount, s.config.RetryInterval),
	)

	// 发送消息
	return client.SendMessage(ctx, message)
}

// BatchSendTextMessage 批量发送文本消息
func (s *dingTalkSvc) BatchSendTextMessage(ctx context.Context, dingIDs []int64, content string, atUsers ...string) []error {
	message := &dingtalk.TextMessage{
		Content: content,
		AtUsers: atUsers,
	}
	return s.BatchSendMessage(ctx, dingIDs, message)
}

// BatchSendMessage 批量发送自定义消息
func (s *dingTalkSvc) BatchSendMessage(ctx context.Context, dingIDs []int64, message dingtalk.Message) []error {
	if len(dingIDs) == 0 {
		return []error{dingtalk.ErrInvalidDingID}
	}

	errors := make([]error, len(dingIDs))
	for i, dingID := range dingIDs {
		errors[i] = s.SendMessage(ctx, dingID, message)
	}

	return errors
}

// getDingConfig 获取钉钉配置
func (s *dingTalkSvc) getDingConfig(ctx context.Context, dingID int64) (*dingtalk.DingConfig, error) {
	// 从repository获取domain对象
	domainDing, err := s.dingRepo.FindByID(ctx, dingID)
	if err != nil {
		return nil, fmt.Errorf("查询钉钉配置失败: %w", err)
	}

	// 转换为钉钉配置
	return s.domainToDingConfig(domainDing), nil
}

// domainToDingConfig 将domain对象转换为钉钉配置
func (s *dingTalkSvc) domainToDingConfig(d domain.Ding) *dingtalk.DingConfig {
	return &dingtalk.DingConfig{
		ID:          d.ID,
		Name:        d.Name,
		AccessToken: d.AccessToken,
		Secret:      d.Secret,
		CreateTime:  d.CreateTime,
		UpdateTime:  d.UpdateTime,
	}
}

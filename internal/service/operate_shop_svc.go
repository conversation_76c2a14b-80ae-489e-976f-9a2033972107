package service

import (
	"context"
	"enter/internal/domain"
	"enter/internal/errs"
	"enter/internal/repository"
	"time"
)

// OperateShopService 运营店铺服务接口
type OperateShopService interface {
	// Create 创建运营店铺
	Create(ctx context.Context, shop domain.OperateShop) (int64, error)
	// Update 更新运营店铺
	Update(ctx context.Context, shop domain.OperateShop) error
	// Delete 删除运营店铺
	Delete(ctx context.Context, id int64) error
	// GetByID 根据ID获取运营店铺
	GetByID(ctx context.Context, id int64) (domain.OperateShop, error)
	// List 获取运营店铺列表
	List(ctx context.Context, req ListOperateShopReq) ([]domain.OperateShop, int64, error)
}

// ListOperateShopReq 查询运营店铺列表请求
type ListOperateShopReq struct {
	Page     int
	PageSize int
	Name     string
	City     string
	Platform int
	Status   int8
	UserID   int64
	BrandID  int64
}

type operateShopService struct {
	operateShopRepo repository.OperateShopRepo
}

// NewOperateShopService 创建运营店铺服务
func NewOperateShopService(operateShopRepo repository.OperateShopRepo) OperateShopService {
	return &operateShopService{
		operateShopRepo: operateShopRepo,
	}
}

// Create 创建运营店铺
func (s *operateShopService) Create(ctx context.Context, shop domain.OperateShop) (int64, error) {
	// 业务验证
	if shop.Name == "" {
		return 0, errs.NewBizError("店铺名称不能为空")
	}
	if shop.Tel == "" {
		return 0, errs.NewBizError("联系方式不能为空")
	}
	if shop.Address == "" {
		return 0, errs.NewBizError("地址不能为空")
	}
	if shop.City == "" {
		return 0, errs.NewBizError("城市不能为空")
	}
	if shop.Platform == 0 {
		return 0, errs.NewBizError("平台不能为空")
	}
	if shop.UserID == 0 {
		return 0, errs.NewBizError("用户ID不能为空")
	}

	// 设置创建时间和更新时间
	now := int(time.Now().Unix())
	shop.CreateTime = now
	shop.UpdateTime = now

	// 检查店铺名称是否已存在
	existingShops, err := s.operateShopRepo.GetByName(ctx, shop.Name)
	if err != nil {
		return 0, err
	}
	if len(existingShops) > 0 {
		return 0, errs.NewBizError("店铺名称已存在")
	}

	return s.operateShopRepo.Create(ctx, shop)
}

// Update 更新运营店铺
func (s *operateShopService) Update(ctx context.Context, shop domain.OperateShop) error {
	// 业务验证
	if shop.ID == 0 {
		return errs.NewBizError("店铺ID不能为空")
	}

	// 检查店铺是否存在
	_, err := s.operateShopRepo.GetByID(ctx, shop.ID)
	if err != nil {
		return errs.NewBizError("店铺不存在")
	}

	// 如果更新了店铺名称，检查是否重复
	if shop.Name != "" {
		existingShops, err := s.operateShopRepo.GetByName(ctx, shop.Name)
		if err != nil {
			return err
		}
		for _, existingShop := range existingShops {
			if existingShop.ID != shop.ID {
				return errs.NewBizError("店铺名称已存在")
			}
		}
	}

	// 设置更新时间
	shop.UpdateTime = int(time.Now().Unix())

	// 构建更新字段列表
	fields := []string{"update_time"}
	if shop.Name != "" {
		fields = append(fields, "name")
	}
	if shop.CategoryID != 0 {
		fields = append(fields, "category_id")
	}
	if shop.Tel != "" {
		fields = append(fields, "tel")
	}
	if shop.Address != "" {
		fields = append(fields, "address")
	}
	if shop.IsDine != 0 {
		fields = append(fields, "is_dine")
	}
	if shop.IsNew != 0 {
		fields = append(fields, "is_new")
	}
	if shop.StoreArea != 0 {
		fields = append(fields, "store_area")
	}
	if shop.BrandID != 0 {
		fields = append(fields, "brand_id")
	}
	if shop.Platform != 0 {
		fields = append(fields, "platform")
	}
	if shop.ShopCode != "" {
		fields = append(fields, "shop_code")
	}
	if shop.IsCharge != 0 {
		fields = append(fields, "is_charge")
	}
	if shop.Fee != 0 {
		fields = append(fields, "fee")
	}
	if shop.CommissionRate != 0 {
		fields = append(fields, "commission_rate")
	}
	if shop.Remark != "" {
		fields = append(fields, "remark")
	}
	if shop.ClueID != 0 {
		fields = append(fields, "clue_id")
	}
	if shop.City != "" {
		fields = append(fields, "city")
	}
	if shop.StartTime != 0 {
		fields = append(fields, "start_time")
	}
	if shop.EndTime != 0 {
		fields = append(fields, "end_time")
	}
	if shop.UserID != 0 {
		fields = append(fields, "user_id")
	}
	if shop.Status != 0 {
		fields = append(fields, "status")
	}
	if shop.LevelTier != "" {
		fields = append(fields, "level_tier")
	}
	if shop.HasPromotion != 0 {
		fields = append(fields, "has_promotion")
	}
	if shop.HasBwc != 0 {
		fields = append(fields, "has_bwc")
	}
	if shop.MainIssues != "" {
		fields = append(fields, "main_issues")
	}
	if shop.TerminationTime != 0 {
		fields = append(fields, "termination_time")
	}
	if shop.IsScaling != 0 {
		fields = append(fields, "is_scaling")
	}
	if shop.Reason != "" {
		fields = append(fields, "reason")
	}
	if shop.UserType != 0 {
		fields = append(fields, "user_type")
	}
	if shop.Mode != 0 {
		fields = append(fields, "mode")
	}
	if shop.StoreID != "" {
		fields = append(fields, "store_id")
	}

	return s.operateShopRepo.Update(ctx, shop, fields)
}

// Delete 删除运营店铺
func (s *operateShopService) Delete(ctx context.Context, id int64) error {
	if id == 0 {
		return errs.NewBizError("店铺ID不能为空")
	}

	// 检查店铺是否存在
	_, err := s.operateShopRepo.GetByID(ctx, id)
	if err != nil {
		return errs.NewBizError("店铺不存在")
	}

	return s.operateShopRepo.Delete(ctx, id)
}

// GetByID 根据ID获取运营店铺
func (s *operateShopService) GetByID(ctx context.Context, id int64) (domain.OperateShop, error) {
	if id == 0 {
		return domain.OperateShop{}, errs.NewBizError("店铺ID不能为空")
	}

	shop, err := s.operateShopRepo.GetByID(ctx, id)
	if err != nil {
		return domain.OperateShop{}, errs.NewBizError("店铺不存在")
	}

	return shop, nil
}

// List 获取运营店铺列表
func (s *operateShopService) List(ctx context.Context, req ListOperateShopReq) ([]domain.OperateShop, int64, error) {
	// 设置默认分页参数
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}
	if req.PageSize > 100 {
		req.PageSize = 100
	}

	offset := (req.Page - 1) * req.PageSize

	// 根据查询条件获取数据
	var shops []domain.OperateShop
	var total int64
	var err error

	// 根据不同条件查询
	if req.Name != "" {
		shops, err = s.operateShopRepo.GetByName(ctx, req.Name)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else if req.City != "" {
		shops, err = s.operateShopRepo.GetByCity(ctx, req.City)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else if req.Platform != 0 {
		shops, err = s.operateShopRepo.GetByPlatform(ctx, req.Platform)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else if req.Status != 0 {
		shops, err = s.operateShopRepo.GetByStatus(ctx, int(req.Status))
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else if req.UserID != 0 {
		shops, err = s.operateShopRepo.GetByUserID(ctx, req.UserID)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else if req.BrandID != 0 {
		shops, err = s.operateShopRepo.GetByBrandID(ctx, req.BrandID)
		if err != nil {
			return nil, 0, err
		}
		total = int64(len(shops))
		// 手动分页
		start := offset
		end := offset + req.PageSize
		if start >= len(shops) {
			shops = []domain.OperateShop{}
		} else {
			if end > len(shops) {
				end = len(shops)
			}
			shops = shops[start:end]
		}
	} else {
		// 默认分页查询
		shops, err = s.operateShopRepo.List(ctx, offset, req.PageSize)
		if err != nil {
			return nil, 0, err
		}
		total, err = s.operateShopRepo.Count(ctx)
		if err != nil {
			return nil, 0, err
		}
	}

	return shops, total, nil
}

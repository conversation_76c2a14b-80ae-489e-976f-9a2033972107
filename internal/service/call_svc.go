package service

import (
	"bytes"
	"context"
	"enter/internal/domain"
	"enter/internal/repository"
	"enter/pkg/waihu"
	"errors"
	"fmt"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/spf13/cast"
	"github.com/spf13/viper"
	"io"
	"net/http"
	"strconv"
	"time"
)

// CallService 呼叫服务接口
type CallService interface {
	// Call 拨打电话
	Call(ctx context.Context, in domain.CallParam) (domain.CallResult, error)
	// HandleCallback 处理回调
	HandleCallback(ctx context.Context, in domain.CallbackResult) error
}

type callService struct {
	callRepo  repository.CallRepo
	waihu     *waihu.WaihuService
	ossClient *oss.Client
}

// NewCallService 创建呼叫服务
func NewCallService(callRepo repository.CallRepo, waihu *waihu.WaihuService, ossClient *oss.Client) CallService {
	return &callService{
		callRepo:  callRepo,
		waihu:     waihu,
		ossClient: ossClient,
	}
}

// Call 拨打电话
func (s *callService) Call(ctx context.Context, in domain.CallParam) (domain.CallResult, error) {
	// 创建通话记录
	record := domain.CallRecord{
		Caller:      in.Caller,
		Callee:      in.Callee,
		UserID:      in.UserID,
		ClueID:      in.ClueID,
		OrderSn:     in.OrderSn,
		TelID:       in.TelID,
		ChannelType: in.Channel,                        // 假设1代表营客云
		Status:      domain.CallRecordStatusExhalation, // 初始状态
		CreateTime:  time.Now(),
		UpdateTime:  time.Now(),
	}

	// 调用外呼服务
	result, err := s.waihu.Call(ctx, in.Channel.Int8(), waihu.CallParam{
		Caller:  in.Caller,
		Callee:  in.Callee,
		OrderSn: in.OrderSn,
		TelId:   in.TelID,
		UnionId: in.UserID,
	})
	if err != nil {
		record.Status = domain.CallRecordStatusFail // 失败状态
		record.ErrorMsg = err.Error()
		_, err = s.callRepo.Create(ctx, record)
		if err != nil {
			return domain.CallResult{}, err
		}
		return domain.CallResult{}, nil
	}

	// 更新通话记录
	record.OrderID = result.OrderId
	record.ShowNo = result.ShowNo
	record.BindId = result.BindId
	record.CallType = result.CallType
	_, err = s.callRepo.Create(ctx, record)
	if err != nil {
		return domain.CallResult{}, err
	}

	return domain.CallResult{
		AppLink: result.AppLink,
	}, nil
}

// HandleCallback 处理回调
func (s *callService) HandleCallback(ctx context.Context, in domain.CallbackResult) error {
	err := s.waihu.CallBack(ctx, in.Channel.Int8(), func() error {
		switch in.Channel {
		case domain.CallChannelXiaoBang:
			return s.xiaobangHandle(ctx, in.XiaoBangCallbackRecord)
		case domain.CallChannelYingKeYun:
			return s.yingkeyunHandle(ctx, in.YingKeYunCallbackRecord)
		}
		return errors.New("unknown channel")
	})
	if err != nil {
		return err
	}

	return nil
}

func (s *callService) xiaobangHandle(ctx context.Context, in domain.XiaoBangCallbackRecord) error {
	record, err := s.callRepo.GetByOrderId(ctx, in.OrderId)
	if err != nil {
		return err
	}
	record.StartTime = cast.ToTime(in.StartTime)
	record.EndTime = cast.ToTime(in.EndTime)
	record.HoldTime = cast.ToInt(in.HoldTime)
	record.EventType = in.EventType
	record.OriAudioURL = in.AudioUrl
	record.Status = domain.CallRecordStatusSuccess

	// 设置状态
	if record.HoldTime <= 5 {
		record.Status = domain.CallRecordStatusFail // 失败状态
	}

	// 更新记录
	err = s.callRepo.Update(ctx, record, []string{
		"status", "start_time", "end_time",
		"hold_time", "ori_audio_url", "event_type",
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *callService) yingkeyunHandle(ctx context.Context, in domain.YingKeYunCallbackRecord) error {
	// 获取通话记录
	record, err := s.callRepo.GetByOrderSn(ctx, in.Payload)
	if err != nil {
		return err
	}

	// 更新通话记录
	record.StartTime = time.Unix(in.CallConnectedTime, 0)
	record.EndTime = time.Unix(in.CallHangUpTime, 0)
	record.HoldTime = in.CallDuration
	record.OriAudioURL = in.SoundRecording
	record.EventType = cast.ToString(in.CallRecordType)
	record.ShowNo = in.TelX
	record.OrderID = in.CallID
	record.Status = domain.CallRecordStatusSuccess

	// 设置状态
	if in.CallStatus == 0 {
		record.Status = domain.CallRecordStatusFail // 失败状态
		record.ErrorMsg = in.EndReason
	}

	record.AudioURL, err = s.fetchAudioURL(in.SoundRecording)
	if err != nil && record.Status == domain.CallRecordStatusSuccess {
		record.Status = domain.CallRecordStatusAudioFail
	}

	// 更新记录
	err = s.callRepo.Update(ctx, record, []string{
		"status", "error_msg", "start_time", "end_time",
		"hold_time", "ori_audio_url", "event_type",
		"show_no", "order_id", "audio_url",
	})
	if err != nil {
		return err
	}
	return nil
}

func (s *callService) fetchAudioURL(audioURL string) (string, error) {
	resp, err := http.Get(audioURL)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		return "", errors.New("sound recording status code " + strconv.Itoa(resp.StatusCode))
	}

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	buffers := bytes.NewBuffer(body)
	// 保存到 OSS
	fileName := fmt.Sprintf("call/zhaoshangbao/%d.mp3", time.Now().Unix())

	bucket, err := s.ossClient.Bucket("rebate-robot")
	if err != nil {
		return "", fmt.Errorf("获取 OSS bucket 失败: %v", err)
	}

	err = bucket.PutObject(fileName, buffers)
	if err != nil {
		return "", fmt.Errorf("上传文件到 OSS 失败: %v", err)
	}

	fileUrl := fmt.Sprintf("%s/%s", viper.GetString("image_url"), fileName)

	return fileUrl, nil
}

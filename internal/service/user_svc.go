package service

import (
	"context"
	"enter/internal/domain"
	"enter/internal/repository"
)

type UserSvc interface {
	GetUser(ctx context.Context, userID int64) (domain.User, error)
}

type userSvc struct {
	userRepo repository.UserRepo
}

func NewUserService(userRepo repository.UserRepo) UserSvc {
	return &userSvc{userRepo: userRepo}
}

func (u *userSvc) GetUser(ctx context.Context, userID int64) (domain.User, error) {
	return u.userRepo.GetByID(ctx, userID)
}

package service

import (
	"context"
	"strings"

	"enter/internal/domain"
	"enter/internal/repository"
)

type BrandMatchService interface {
	// MatchAndUpdateBrands 匹配并更新店铺的品牌ID
	MatchAndUpdateBrands(ctx context.Context, batchSize int) (int, error)
}

type brandMatchService struct {
	brandRepo      repository.BrandRepo
	circleShopRepo repository.CircleShopRepo
}

func NewBrandMatchService(brandRepo repository.BrandRepo, circleShopRepo repository.CircleShopRepo) BrandMatchService {
	return &brandMatchService{
		brandRepo:      brandRepo,
		circleShopRepo: circleShopRepo,
	}
}

func (s *brandMatchService) MatchAndUpdateBrands(ctx context.Context, batchSize int) (int, error) {
	// 获取未匹配品牌的店铺
	shops, err := s.circleShopRepo.ListUnmatchedShops(ctx, 0, batchSize)
	if err != nil {
		return 0, err
	}

	brands, err := s.brandRepo.List(ctx, 0, 20000)
	if err != nil {
		return 0, err
	}

	// 收集需要更新的店铺和对应的品牌ID
	type ShopBrandPair struct {
		ShopIDs []int64
		BrandID int64
	}
	brandGroups := make(map[int64]*ShopBrandPair)

	for _, shop := range shops {
		// 找到最佳匹配的品牌
		if bestBrand := s.findBestMatch(shop.Name, brands); bestBrand != nil {
			if group, exists := brandGroups[bestBrand.ID]; exists {
				group.ShopIDs = append(group.ShopIDs, shop.ID)
			} else {
				brandGroups[bestBrand.ID] = &ShopBrandPair{
					ShopIDs: []int64{shop.ID},
					BrandID: bestBrand.ID,
				}
			}
		}
	}

	// 按品牌ID分组批量更新
	totalUpdated := 0
	for _, group := range brandGroups {
		err = s.circleShopRepo.BatchUpdateBrandID(ctx, group.ShopIDs, group.BrandID)
		if err != nil {
			return totalUpdated, err
		}
		totalUpdated += len(group.ShopIDs)
	}

	return totalUpdated, nil
}

// findBestMatch 找到最佳匹配的品牌
func (s *brandMatchService) findBestMatch(shopName string, brands []domain.Brand) *domain.Brand {
	if len(brands) == 0 {
		return nil
	}

	// 如果只有一个匹配项，直接返回
	if len(brands) == 1 {
		return &brands[0]
	}

	// 找到名称最长的品牌（避免短名称误匹配）
	var bestMatch *domain.Brand
	maxLen := 0

	for i, brand := range brands {
		// 确保品牌名称是店铺名称的一部分
		if strings.Contains(shopName, brand.BrandName) && len(brand.BrandName) > maxLen {
			maxLen = len(brand.BrandName)
			bestMatch = &brands[i]
		}
	}

	return bestMatch
}

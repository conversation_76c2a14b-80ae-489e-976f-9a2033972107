package job

import (
	"context"
	"fmt"
	rlock "github.com/gotomicro/redis-lock"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"time"
)

type Job interface {
	Name() string
	Run() error
}

type CronJobBuilder struct {
}

func NewCronJobBuilder() *CronJobBuilder {
	return &CronJobBuilder{}
}

func (b *CronJobBuilder) Build(j Job) cron.Job {
	return jobAdapter(func() error {
		err := j.Run()
		if err != nil {
			zap.L().Error(fmt.Sprintf("job：%s 运行失败", j.Name()), zap.Error(err))
		}
		return err
	})
}

type jobAdapter func() error

func (f jobAdapter) Run() {
	_ = f()
}

type OneRunningCronJobBuilder struct {
	client *rlock.Client
}

func NewOneRunningCronJobBuilder(client *rlock.Client) *OneRunningCronJobBuilder {
	return &OneRunningCronJobBuilder{client: client}
}

func (b *OneRunningCronJobBuilder) Build(j Job) cron.Job {
	return jobAdapter(func() error {
		ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
		defer cancel()

		// 走配置
		runTime := time.Minute

		lock, err := b.client.Lock(ctx, fmt.Sprintf("cron_job:lock:%s", j.Name()), runTime, &rlock.FixIntervalRetry{
			Interval: time.Millisecond * 100,
			Max:      3,
		}, time.Second*10)
		if err != nil {
			zap.L().Error(fmt.Sprintf("job：%s 加锁失败", j.Name()), zap.Error(err))
			return err
		}

		go func() {
			er := lock.AutoRefresh(runTime/2, time.Second*10)
			if er != nil {
				zap.L().Error(fmt.Sprintf("job：%s 自动续约失败", j.Name()), zap.Error(er))
			}
		}()

		defer func() {
			ctx, cancel := context.WithTimeout(context.Background(), time.Second*10)
			defer cancel()
			er := lock.Unlock(ctx)
			if er != nil {
				zap.L().Error(fmt.Sprintf("job：%s 解锁失败", j.Name()), zap.Error(er))
			}
		}()

		err = j.Run()
		if err != nil {
			zap.L().Error(fmt.Sprintf("job：%s 运行失败", j.Name()), zap.Error(err))
		}

		return err
	})
}

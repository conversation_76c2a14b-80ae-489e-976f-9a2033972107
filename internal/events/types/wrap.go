package types

import (
	"context"
	"encoding/json"
	"github.com/hibiken/asynq"
)

func WrapHandle[T any](topic string, handler func(ctx context.Context, t T) error) asynq.HandlerFunc {
	return func(ctx context.Context, task *asynq.Task) error {
		if task.Type() != topic {
			return nil
		}
		var t T
		if err := json.Unmarshal(task.Payload(), &t); err != nil {
			return err
		}
		return handler(ctx, t)
	}
}

package domain

import (
	"enter/internal/repository/dao/model"
)

// Ding 钉钉配置领域对象 (匹配DBML中的sq_ding表结构)
type Ding struct {
	ID          int64  // 主键ID
	Name        string // 钉钉名字
	AccessToken string // 钉钉token
	Secret      string // 加密密钥
	CreateTime  int64  // 创建时间
	UpdateTime  int64  // 更新时间
}

// ToModel 转换为数据库模型
func (d *Ding) ToModel() *model.Ding {
	return &model.Ding{
		ID:          d.ID,
		Name:        d.Name,
		AccessToken: d.AccessToken,
		Secret:      d.Secret,
		CreateTime:  d.CreateTime,
		UpdateTime:  d.UpdateTime,
	}
}

// FromDingModel 从数据库模型创建领域对象
func FromDingModel(m *model.Ding) *Ding {
	if m == nil {
		return nil
	}
	return &Ding{
		ID:          m.ID,
		Name:        m.Name,
		AccessToken: m.AccessToken,
		Secret:      m.Secret,
		CreateTime:  m.CreateTime,
		UpdateTime:  m.UpdateTime,
	}
}

// FromDingModels 从数据库模型列表创建领域对象列表
func FromDingModels(models []*model.Ding) []*Ding {
	if len(models) == 0 {
		return nil
	}
	result := make([]*Ding, len(models))
	for i, m := range models {
		result[i] = FromDingModel(m)
	}
	return result
}

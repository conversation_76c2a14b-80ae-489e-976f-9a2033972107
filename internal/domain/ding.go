package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// Ding 钉钉配置领域对象
type Ding struct {
	ID          int64      // 主键ID
	AppKey      string     // 应用的唯一标识key
	AppSecret   string     // 应用的密钥
	AgentID     string     // 应用的AgentID
	CorpID      string     // 企业ID
	Status      int        // 状态：0-禁用 1-启用
	Description string     // 配置描述
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *Ding) ToModel() *model.Ding {
	return &model.Ding{
		ID:          d.ID,
		AppKey:      d.AppKey,
		AppSecret:   d.AppSecret,
		AgentID:     d.AgentID,
		CorpID:      d.CorpID,
		Status:      d.Status,
		Description: d.Description,
		CreatorID:   d.CreatorID,
		CreatorName: d.CreatorName,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromDingModel 从数据库模型创建领域对象
func FromDingModel(m *model.Ding) *Ding {
	if m == nil {
		return nil
	}
	return &Ding{
		ID:          m.ID,
		AppKey:      m.AppKey,
		AppSecret:   m.AppSecret,
		AgentID:     m.AgentID,
		CorpID:      m.CorpID,
		Status:      m.Status,
		Description: m.Description,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromDingModels 从数据库模型列表创建领域对象列表
func FromDingModels(models []*model.Ding) []*Ding {
	if len(models) == 0 {
		return nil
	}
	result := make([]*Ding, len(models))
	for i, m := range models {
		result[i] = FromDingModel(m)
	}
	return result
}

package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// Config 系统配置领域对象
type Config struct {
	ID        int64      // 主键ID
	Key       string     // 配置键
	Value     string     // 配置值
	Type      string     // 配置类型
	Desc      string     // 配置描述
	CreatedAt time.Time  // 创建时间
	UpdatedAt time.Time  // 更新时间
	DeletedAt *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *Config) ToModel() *model.Config {
	return &model.Config{
		ID:        d.ID,
		Key:       d.Key,
		Value:     d.Value,
		Type:      d.Type,
		Desc:      d.Desc,
		CreatedAt: d.CreatedAt,
		UpdatedAt: d.UpdatedAt,
		DeletedAt: d.DeletedAt,
	}
}

// FromConfigModel 从数据库模型创建领域对象
func FromConfigModel(m *model.Config) *Config {
	return &Config{
		ID:        m.ID,
		Key:       m.Key,
		Value:     m.Value,
		Type:      m.Type,
		Desc:      m.Desc,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
		DeletedAt: m.DeletedAt,
	}
}

// FromConfigModels 从数据库模型列表创建领域对象列表
func FromConfigModels(models []*model.Config) []*Config {
	result := make([]*Config, len(models))
	for i, m := range models {
		result[i] = FromConfigModel(m)
	}
	return result
}

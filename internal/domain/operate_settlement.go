package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// OperateSettlement 运营结算领域对象
type OperateSettlement struct {
	ID               int64      // 主键ID
	ShopID           string     // 店铺ID
	ShopName         string     // 店铺名称
	OperatorID       int64      // 运营人员ID
	OperatorName     string     // 运营人员姓名
	SettlementAmount float64    // 结算金额
	SettlementType   int        // 结算类型：1-日结 2-周结 3-月结
	SettlementStatus int        // 结算状态：0-待结算 1-已结算 2-已取消
	SettlementTime   *time.Time // 结算时间
	StartTime        time.Time  // 结算开始时间
	EndTime          time.Time  // 结算结束时间
	Remark           string     // 备注
	CreatedAt        time.Time  // 创建时间
	UpdatedAt        time.Time  // 更新时间
	DeletedAt        *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *OperateSettlement) ToModel() *model.OperateSettlement {
	return &model.OperateSettlement{
		ID:               d.ID,
		ShopID:           d.ShopID,
		ShopName:         d.ShopName,
		OperatorID:       d.OperatorID,
		OperatorName:     d.OperatorName,
		SettlementAmount: d.SettlementAmount,
		SettlementType:   d.SettlementType,
		SettlementStatus: d.SettlementStatus,
		SettlementTime:   d.SettlementTime,
		StartTime:        d.StartTime,
		EndTime:          d.EndTime,
		Remark:           d.Remark,
		CreatedAt:        d.CreatedAt,
		UpdatedAt:        d.UpdatedAt,
		DeletedAt:        d.DeletedAt,
	}
}

// FromOperateSettlementModel 从数据库模型创建领域对象
func FromOperateSettlementModel(m *model.OperateSettlement) *OperateSettlement {
	if m == nil {
		return nil
	}
	return &OperateSettlement{
		ID:               m.ID,
		ShopID:           m.ShopID,
		ShopName:         m.ShopName,
		OperatorID:       m.OperatorID,
		OperatorName:     m.OperatorName,
		SettlementAmount: m.SettlementAmount,
		SettlementType:   m.SettlementType,
		SettlementStatus: m.SettlementStatus,
		SettlementTime:   m.SettlementTime,
		StartTime:        m.StartTime,
		EndTime:          m.EndTime,
		Remark:           m.Remark,
		CreatedAt:        m.CreatedAt,
		UpdatedAt:        m.UpdatedAt,
		DeletedAt:        m.DeletedAt,
	}
}

// FromOperateSettlementModels 从数据库模型列表创建领域对象列表
func FromOperateSettlementModels(models []*model.OperateSettlement) []*OperateSettlement {
	if len(models) == 0 {
		return nil
	}
	result := make([]*OperateSettlement, len(models))
	for i, m := range models {
		result[i] = FromOperateSettlementModel(m)
	}
	return result
}

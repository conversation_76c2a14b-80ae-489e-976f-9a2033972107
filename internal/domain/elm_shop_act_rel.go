package domain

import "time"

// ElmShopActRel 饿了么店铺活动关系
type ElmShopActRel struct {
	ID                int64     // 自增ID
	ShopID            int64     // 店铺ID
	ActRecID          int64     // 报名ID
	ActivityID        int       // 活动ID
	OriShopID         string    // 饿了么店铺ID
	OriShopName       string    // 店铺名称
	ShopScore         float64   // 店铺评分
	AuditStatus       int8      // 审核状态
	BonusFee          int       // 奖励金
	BounsOrderNum     int       // 每日活动库存
	ChannelServiceFee float64   // 渠道推广费率
	CityServicesFee   float64   // 招商费率
	CommissionRate    float64   // 佣金比率
	PromotionState    int       // 推广状态
	SalesNum          int       // 销量
	ServicesFee       int       // 招商服务费
	OrderAmtLimit     int       // 满XX元
	CityName          string    // 城市
	DistrictName      string    // 区
	EnrollDate        time.Time // 报名时间
	StartDate         time.Time // 开始时间
	EndDate           time.Time // 结束时间
	CreatedAt         time.Time // 创建时间
	UpdatedAt         time.Time // 更新时间
	ShopCode          string    // 店铺的code
	TerminateTime     time.Time // 退出时间
	IsDeleted         int8      // 是否删除
	MatchName         string    // 去除所有特殊字符的店铺名
	BusinessDistrict  string    // 商圈
	RefuseDate        time.Time // 拒绝时间
	PassDate          time.Time // 通过时间
	BrandID           int       // 品牌id
}

// NewElmShopActRel 创建ElmShopActRel实例
func NewElmShopActRel(shopID int64, actRecID int64, activityID int, oriShopID, oriShopName string) ElmShopActRel {
	return ElmShopActRel{
		ShopID:      shopID,
		ActRecID:    actRecID,
		ActivityID:  activityID,
		OriShopID:   oriShopID,
		OriShopName: oriShopName,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
	}
}

package domain

import (
	"time"
)

type CallRecordStatus int8

const (
	CallRecordStatusExhalation CallRecordStatus = iota
	CallRecordStatusFail
	CallRecordStatusSuccess
	CallRecordStatusAudioSuccess
	CallRecordStatusAudioFail
)

func (s CallRecordStatus) Int8() int8 {
	return int8(s)
}

// CallRecord 通话记录领域对象
type CallRecord struct {
	ID          int64            // 主键ID
	Caller      string           // 主叫号码
	Callee      string           // 被叫号码
	UserID      int64            // 用户ID
	ClueID      int64            // 线索ID
	OrderSn     string           // 订单编号
	TelID       string           // 电话ID
	ChannelType CallChannel      // 渠道类型
	StartTime   time.Time        // 开始时间
	EndTime     time.Time        // 结束时间
	HoldTime    int              // 通话时长
	Status      CallRecordStatus // 状态
	ErrorMsg    string           // 错误信息
	OriAudioURL string           // 原始录音URL
	AudioURL    string           // 处理后的录音URL
	ShowNo      string           // 显示号码
	OrderID     string           // 通话ID
	EventType   string           // 事件类型
	BindId      string           // 绑定信息
	CallType    string           // 呼叫类型
	CreateTime  time.Time        // 创建时间
	UpdateTime  time.Time        // 更新时间
}

package domain

import "time"

// BusinessDistrictLocation 商圈位置领域对象
type BusinessDistrictLocation struct {
	ID               int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	BusinessDistrict string
	Province         string
	City             string
	District         string
	Location         string
	FormattedAddress string
	CrawlTime        int
	Level            int
	Type             int8
	CrawlNum         int
}

package domain

// OperateShop 运营店铺领域模型
type OperateShop struct {
	ID              int64   // 主键ID
	Name            string  // 店铺名称
	CreateTime      int     // 创建时间
	UpdateTime      int     // 更新时间
	CategoryID      int     // 分类id
	Tel             string  // 联系方式
	Address         string  // 地址
	IsDine          int8    // 有无堂食
	IsNew           int8    // 是否新店
	StoreArea       int     // 门店面积
	BrandID         int64   // 品牌id
	Platform        int     // 平台
	ShopCode        string  // 店铺编码
	IsCharge        int8    // 前置收费
	Fee             float64 // 收费金额
	CommissionRate  float64 // 佣金比例
	Remark          string  // 备注
	ClueID          int64   // 线索id
	City            string  // 城市
	StartTime       int     // 签约时间
	EndTime         int     // 结束时间
	UserID          int64   // 用户id
	Status          int8    // 状态
	LevelTier       string  // 分层等级
	HasPromotion    int     // 有无推广
	HasBwc          int     // 推霸王餐
	MainIssues      string  // 主要问题
	TerminationTime int     // 解约时间
	IsScaling       int     // 是否起量
	Reason          string  // 原因
	UserType        int8    // 用户类型
	Mode            int8    // 模式
	StoreID         string  // 店铺id
}

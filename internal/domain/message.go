package domain

import (
	"time"
)

// Message 消息领域对象
type Message struct {
	ID           int64      // 主键ID
	MessageType  int        // 消息类型：1-系统消息 2-业务消息 3-通知消息
	Title        string     // 消息标题
	Content      string     // 消息内容
	Status       int        // 状态：0-未读 1-已读
	Priority     int        // 优先级：1-低 2-中 3-高
	ReceiverID   int64      // 接收人ID
	ReceiverName string     // 接收人姓名
	SenderID     int64      // 发送人ID
	SenderName   string     // 发送人姓名
	ReadTime     *time.Time // 阅读时间
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

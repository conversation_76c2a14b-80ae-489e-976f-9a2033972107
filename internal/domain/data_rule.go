package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// DataRule 数据规则领域对象
type DataRule struct {
	ID          int64      // 主键ID
	RuleName    string     // 规则名称
	RuleType    int        // 规则类型：1-过滤规则 2-分配规则 3-其他
	RuleContent string     // 规则内容（JSON格式）
	Status      int        // 状态：0-禁用 1-启用
	Priority    int        // 优先级
	Description string     // 规则描述
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *DataRule) ToModel() *model.DataRule {
	return &model.DataRule{
		ID:          d.ID,
		RuleName:    d.RuleName,
		RuleType:    d.RuleType,
		RuleContent: d.RuleContent,
		Status:      d.Status,
		Priority:    d.Priority,
		Description: d.Description,
		CreatorID:   d.CreatorID,
		CreatorName: d.CreatorName,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromDataRuleModel 从数据库模型创建领域对象
func FromDataRuleModel(m *model.DataRule) *DataRule {
	if m == nil {
		return nil
	}
	return &DataRule{
		ID:          m.ID,
		RuleName:    m.RuleName,
		RuleType:    m.RuleType,
		RuleContent: m.RuleContent,
		Status:      m.Status,
		Priority:    m.Priority,
		Description: m.Description,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromDataRuleModels 从数据库模型列表创建领域对象列表
func FromDataRuleModels(models []*model.DataRule) []*DataRule {
	if len(models) == 0 {
		return nil
	}
	result := make([]*DataRule, len(models))
	for i, m := range models {
		result[i] = FromDataRuleModel(m)
	}
	return result
}

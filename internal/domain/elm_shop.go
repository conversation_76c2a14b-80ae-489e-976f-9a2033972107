package domain

import "time"

// ElmShop 霸王餐导入店铺
type ElmShop struct {
	ID                 int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
	Name               string
	Logo               string
	EmployeeID         int64
	OriShopID          string
	ShopCode           string
	Longitude          float64
	Latitude           float64
	Province           string
	City               string
	District           string
	Address            string
	BusinessDistrict   string
	Type               int
	Status             int
	YesterdayOrderNum  int
	BeforeDayOrderNum  int
	MonthOrderNum      int
	LastMonthOrderNum  int
	WeekOrderNum       int
	TotalOrderNum      int
	ShopLink           string
	IsOpen             bool
	IsValid            bool
	JobPriority        bool
	Tags               string
	Tel                string
	MonthSale          int
	Score              float64
	Evaluate           int
	CrawlTime          int
	SourceType         bool
	ClaimID            int64
	UsedName           string
	DistrictCode       string
	IntentLevel        int
	CallTime           int
	Priority           int
	BrandLeaderID      int64
	BrandType          int
	BrandID            int
	CategoryName       string
	LowerPrice         float64
	TallPrice          float64
	IsBrand            bool
	IsNew              bool
	OrderSnapshot      string
	EvaluateSnapshot   string
	ShopSnapshot       string
	IsCpsOpen          int
	CategoryID         int
	IsDpEffect         int
	BusinessUpdateTime int
	BusinessType       string
	CpsClaimID         int64
	CpsEmployeeID      int64
	ShopType           int
	BonusFee           string
	ServiceFee         string
	MatchName          string
	SourceID           int
	LastTakeTime       time.Time
}

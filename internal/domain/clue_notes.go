package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ClueNotes 线索笔记领域对象
type ClueNotes struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Content     string     // 笔记内容
	Type        int        // 笔记类型 1:普通笔记 2:重要笔记
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ClueNotes) ToModel() *model.ClueNotes {
	return &model.ClueNotes{
		ID:          d.ID,
		ClueID:      d.ClueID,
		Content:     d.Content,
		Type:        d.Type,
		CreatorID:   d.CreatorID,
		CreatorName: d.CreatorName,
		Status:      d.Status,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromClueNotesModel 从数据库模型创建领域对象
func FromClueNotesModel(m *model.ClueNotes) *ClueNotes {
	return &ClueNotes{
		ID:          m.ID,
		ClueID:      m.ClueID,
		Content:     m.Content,
		Type:        m.Type,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		Status:      m.Status,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromClueNotesModels 从数据库模型列表创建领域对象列表
func FromClueNotesModels(models []*model.ClueNotes) []*ClueNotes {
	result := make([]*ClueNotes, len(models))
	for i, m := range models {
		result[i] = FromClueNotesModel(m)
	}
	return result
}

package domain

import "time"

// ElmAccount 饿了么账号信息
type ElmAccount struct {
	ID        int64     // 自增ID
	Username  string    // 用户名
	Password  string    // 密码
	City      string    // 城市
	Cookie    string    // 登录cookie
	AppID     string    // 应用id
	OpenCrawl bool      // 是否开启爬虫
	CreatedAt time.Time // 创建时间
	UpdatedAt time.Time // 更新时间
}

// NewElmAccount 创建ElmAccount实例
func NewElmAccount(username, password, city string) ElmAccount {
	return ElmAccount{
		Username:  username,
		Password:  password,
		City:      city,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}
}

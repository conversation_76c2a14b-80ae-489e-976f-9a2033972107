package domain

import "time"

// Brand 品牌领域对象
type Brand struct {
	ID               int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	BrandName        string
	BrandLogo        string
	DetailImg        string
	Desc             string
	DetailDesc       string
	Status           int8
	Weight           int
	Policy           string
	StoreNum         int
	ContactName      string
	ContactPhone     string
	Company          string
	Average          float64
	CategoryID       int64
	InStoreNum       int64
	City             string
	Province         string
	CreateType       int8
	CrawlTime        int
	LockStatus       int8
	OperationMode    string
	BrandCode        string
	AreaMin          int64
	AreaMax          int64
	ShowType         int
	PlatformStoreNum int
	MinMonthSale     int
	MaxMonthSale     int
}

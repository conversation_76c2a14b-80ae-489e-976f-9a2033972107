package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ClueReport 线索报告领域对象
type ClueReport struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Title       string     // 报告标题
	Content     string     // 报告内容
	Type        int        // 报告类型 1:日报 2:周报 3:月报
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ClueReport) ToModel() *model.ClueReport {
	return &model.ClueReport{
		ID:          d.ID,
		ClueID:      d.ClueID,
		Title:       d.Title,
		Content:     d.Content,
		Type:        d.Type,
		CreatorID:   d.CreatorID,
		CreatorName: d.CreatorName,
		Status:      d.Status,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromClueReportModel 从数据库模型创建领域对象
func FromClueReportModel(m *model.ClueReport) *ClueReport {
	return &ClueReport{
		ID:          m.ID,
		ClueID:      m.ClueID,
		Title:       m.Title,
		Content:     m.Content,
		Type:        m.Type,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		Status:      m.Status,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromClueReportModels 从数据库模型列表创建领域对象列表
func FromClueReportModels(models []*model.ClueReport) []*ClueReport {
	result := make([]*ClueReport, len(models))
	for i, m := range models {
		result[i] = FromClueReportModel(m)
	}
	return result
}

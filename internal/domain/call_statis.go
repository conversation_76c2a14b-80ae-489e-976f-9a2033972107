package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// CallStatis 通话统计领域对象
type CallStatis struct {
	ID             int64      // 主键ID
	EmployeeID     int64      // 员工ID
	EmployeeName   string     // 员工姓名
	Date           time.Time  // 统计日期
	TotalCalls     int        // 总通话次数
	ConnectedCalls int        // 接通次数
	TotalDuration  int        // 总通话时长(秒)
	AvgDuration    float64    // 平均通话时长(秒)
	CreatedAt      time.Time  // 创建时间
	UpdatedAt      time.Time  // 更新时间
	DeletedAt      *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *CallStatis) ToModel() *model.CallStatis {
	return &model.CallStatis{
		ID:             d.ID,
		EmployeeID:     d.EmployeeID,
		EmployeeName:   d.EmployeeName,
		Date:           d.Date,
		TotalCalls:     d.TotalCalls,
		ConnectedCalls: d.ConnectedCalls,
		TotalDuration:  d.TotalDuration,
		AvgDuration:    d.AvgDuration,
		CreatedAt:      d.CreatedAt,
		UpdatedAt:      d.UpdatedAt,
		DeletedAt:      d.DeletedAt,
	}
}

// FromCallStatisModel 从数据库模型创建领域对象
func FromCallStatisModel(m *model.CallStatis) *CallStatis {
	return &CallStatis{
		ID:             m.ID,
		EmployeeID:     m.EmployeeID,
		EmployeeName:   m.EmployeeName,
		Date:           m.Date,
		TotalCalls:     m.TotalCalls,
		ConnectedCalls: m.ConnectedCalls,
		TotalDuration:  m.TotalDuration,
		AvgDuration:    m.AvgDuration,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
		DeletedAt:      m.DeletedAt,
	}
}

// FromCallStatisModels 从数据库模型列表创建领域对象列表
func FromCallStatisModels(models []*model.CallStatis) []*CallStatis {
	result := make([]*CallStatis, len(models))
	for i, m := range models {
		result[i] = FromCallStatisModel(m)
	}
	return result
}

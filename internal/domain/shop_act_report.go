package domain

import "time"

// ShopActReport 店铺活动报告领域对象
type ShopActReport struct {
	ID                      int64
	CreatedAt               time.Time
	UpdatedAt               time.Time
	ShopID                  int64
	ShopCode                string
	ShopName                string
	ActName                 string
	BrandName               string
	UV                      int64
	PromoteTaokeNum         int64
	PayAmount               float64
	PayNum                  int64
	OrderOriginalServiceFee float64
	OrderServicesFee        float64
	UserSettleAmount        float64
	ShopSettleAmount        float64
	SettleNum               int64
	OriginalServiceFee      float64
	SettleFee               float64
	CityServiceFee          float64
	CitySettleFee           float64
	OrderServiceFee         float64
	OrderSettleFee          float64
	City                    string
	District                string
	BusinessDistrict        string
	CreateTime              time.Time
	EmployeeID              int
	IsNewShop               int8
	Bounty                  float64
	UserPayThreshold        float64
	ActID                   int64
	Type                    int8
}
 
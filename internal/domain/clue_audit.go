package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ClueAudit 线索审核领域对象
type ClueAudit struct {
	ID          int64      // 主键ID
	ClueID      int64      // 线索ID
	Type        int        // 审核类型 1:线索分配 2:线索转移 3:线索关闭
	Status      int        // 审核状态 1:待审核 2:审核通过 3:审核拒绝
	Remark      string     // 审核备注
	AuditorID   int64      // 审核人ID
	AuditorName string     // 审核人姓名
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ClueAudit) ToModel() *model.ClueAudit {
	return &model.ClueAudit{
		ID:          d.ID,
		ClueID:      d.ClueID,
		Type:        d.Type,
		Status:      d.Status,
		Remark:      d.Remark,
		AuditorID:   d.AuditorID,
		AuditorName: d.AuditorName,
		CreatorID:   d.CreatorID,
		CreatorName: d.Creator<PERSON>ame,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromClueAuditModel 从数据库模型创建领域对象
func FromClueAuditModel(m *model.ClueAudit) *ClueAudit {
	return &ClueAudit{
		ID:          m.ID,
		ClueID:      m.ClueID,
		Type:        m.Type,
		Status:      m.Status,
		Remark:      m.Remark,
		AuditorID:   m.AuditorID,
		AuditorName: m.AuditorName,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromClueAuditModels 从数据库模型列表创建领域对象列表
func FromClueAuditModels(models []*model.ClueAudit) []*ClueAudit {
	result := make([]*ClueAudit, len(models))
	for i, m := range models {
		result[i] = FromClueAuditModel(m)
	}
	return result
}

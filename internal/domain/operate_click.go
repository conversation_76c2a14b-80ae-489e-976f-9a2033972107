package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// OperateClick 运营点击领域对象
type OperateClick struct {
	ID           int64      // 主键ID
	ShopID       string     // 店铺ID
	ShopName     string     // 店铺名称
	OperatorID   int64      // 运营人员ID
	OperatorName string     // 运营人员姓名
	ClickType    int        // 点击类型：1-商品 2-活动 3-其他
	ClickTarget  string     // 点击目标
	ClickTime    time.Time  // 点击时间
	ClickIP      string     // 点击IP
	ClickUA      string     // 点击UA
	ClickReferer string     // 点击来源
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *OperateClick) ToModel() *model.OperateClick {
	return &model.OperateClick{
		ID:           d.ID,
		ShopID:       d.ShopID,
		ShopName:     d.ShopName,
		OperatorID:   d.OperatorID,
		OperatorName: d.OperatorName,
		ClickType:    d.ClickType,
		ClickTarget:  d.ClickTarget,
		ClickTime:    d.ClickTime,
		ClickIP:      d.ClickIP,
		ClickUA:      d.ClickUA,
		ClickReferer: d.ClickReferer,
		CreatedAt:    d.CreatedAt,
		UpdatedAt:    d.UpdatedAt,
		DeletedAt:    d.DeletedAt,
	}
}

// FromOperateClickModel 从数据库模型创建领域对象
func FromOperateClickModel(m *model.OperateClick) *OperateClick {
	if m == nil {
		return nil
	}
	return &OperateClick{
		ID:           m.ID,
		ShopID:       m.ShopID,
		ShopName:     m.ShopName,
		OperatorID:   m.OperatorID,
		OperatorName: m.OperatorName,
		ClickType:    m.ClickType,
		ClickTarget:  m.ClickTarget,
		ClickTime:    m.ClickTime,
		ClickIP:      m.ClickIP,
		ClickUA:      m.ClickUA,
		ClickReferer: m.ClickReferer,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
		DeletedAt:    m.DeletedAt,
	}
}

// FromOperateClickModels 从数据库模型列表创建领域对象列表
func FromOperateClickModels(models []*model.OperateClick) []*OperateClick {
	if len(models) == 0 {
		return nil
	}
	result := make([]*OperateClick, len(models))
	for i, m := range models {
		result[i] = FromOperateClickModel(m)
	}
	return result
}

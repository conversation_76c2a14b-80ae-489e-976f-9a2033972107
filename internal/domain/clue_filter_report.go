package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ClueFilterReport 线索过滤报表领域对象
type ClueFilterReport struct {
	ID           int64      // 主键ID
	ClueID       int64      // 线索ID
	FilterType   int        // 过滤类型 1:重复 2:黑名单 3:无效号码 4:其他
	FilterReason string     // 过滤原因
	OperatorID   int64      // 操作人ID
	OperatorName string     // 操作人姓名
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ClueFilterReport) ToModel() *model.ClueFilterReport {
	return &model.ClueFilterReport{
		ID:           d.ID,
		ClueID:       d.ClueID,
		FilterType:   d.FilterType,
		FilterReason: d.FilterReason,
		OperatorID:   d.OperatorID,
		OperatorName: d.OperatorName,
		CreatedAt:    d.CreatedAt,
		UpdatedAt:    d.Updated<PERSON>t,
		DeletedAt:    d.DeletedAt,
	}
}

// FromClueFilterReportModel 从数据库模型创建领域对象
func FromClueFilterReportModel(m *model.ClueFilterReport) *ClueFilterReport {
	return &ClueFilterReport{
		ID:           m.ID,
		ClueID:       m.ClueID,
		FilterType:   m.FilterType,
		FilterReason: m.FilterReason,
		OperatorID:   m.OperatorID,
		OperatorName: m.OperatorName,
		CreatedAt:    m.CreatedAt,
		UpdatedAt:    m.UpdatedAt,
		DeletedAt:    m.DeletedAt,
	}
}

// FromClueFilterReportModels 从数据库模型列表创建领域对象列表
func FromClueFilterReportModels(models []*model.ClueFilterReport) []*ClueFilterReport {
	result := make([]*ClueFilterReport, len(models))
	for i, m := range models {
		result[i] = FromClueFilterReportModel(m)
	}
	return result
}

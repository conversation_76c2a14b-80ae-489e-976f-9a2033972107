package domain

// User 用户领域模型
type User struct {
	ID                  int64
	SN                  int64
	Avatar              string
	RealName            string
	Nickname            string
	Account             string
	Password            string
	Mobile              string
	Sex                 int8
	Channel             int8
	IsDisable           int8
	LoginIP             string
	LoginTime           int64
	IsNewUser           int8
	UserMoney           float64
	TotalRechargeAmount float64
	EmployeeID          int64
	System              int8
	BdmID               int64
	BusinessType        int8
	CreateTime          int64
	UpdateTime          int64
	DeleteTime          int64
	DingID              int64
	City                string
	TelID               string
	IsCallUser          int64
	RoleID              int64
	CallType            int8
	ParentID            int64
	Level               int64
	Version             string
}

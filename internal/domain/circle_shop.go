package domain

import "time"

// CircleShop 商圈店铺领域对象
type CircleShop struct {
	ID             int64
	CreatedAt      time.Time
	UpdatedAt      time.Time
	Name           string
	CircleID       int64
	CrawlTime      int
	Distance       int
	Tags           string
	MonthSale      int
	Score          float64
	CategoryName   string
	LowerPrice     float64
	TallPrice      float64
	IsBrand        int8
	IsNew          int8
	LastUpdateTime time.Time
	BrandID        int64
}
 
package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ClueTaskInfo 线索任务详情领域对象
type ClueTaskInfo struct {
	ID          int64      // 主键ID
	TaskID      int64      // 任务ID
	Content     string     // 任务详情内容
	Type        int        // 详情类型 1:进度更新 2:问题反馈 3:其他
	CreatorID   int64      // 创建人ID
	CreatorName string     // 创建人姓名
	Status      int        // 状态 1:正常 2:删除
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ClueTaskInfo) ToModel() *model.ClueTaskInfo {
	return &model.ClueTaskInfo{
		ID:          d.ID,
		TaskID:      d.TaskID,
		Content:     d.Content,
		Type:        d.Type,
		CreatorID:   d.CreatorID,
		CreatorName: d.CreatorName,
		Status:      d.Status,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromClueTaskInfoModel 从数据库模型创建领域对象
func FromClueTaskInfoModel(m *model.ClueTaskInfo) *ClueTaskInfo {
	return &ClueTaskInfo{
		ID:          m.ID,
		TaskID:      m.TaskID,
		Content:     m.Content,
		Type:        m.Type,
		CreatorID:   m.CreatorID,
		CreatorName: m.CreatorName,
		Status:      m.Status,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromClueTaskInfoModels 从数据库模型列表创建领域对象列表
func FromClueTaskInfoModels(models []*model.ClueTaskInfo) []*ClueTaskInfo {
	result := make([]*ClueTaskInfo, len(models))
	for i, m := range models {
		result[i] = FromClueTaskInfoModel(m)
	}
	return result
}

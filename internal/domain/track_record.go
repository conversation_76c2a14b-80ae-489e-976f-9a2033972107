package domain

// TrackRecord 跟进记录领域模型
type TrackRecord struct {
	ID              int64
	ClueID          int64
	ClueType        int8
	UserID          int64
	CreateTime      int64
	UpdateTime      int64
	Status          int
	CancelTime      int64
	SubmitTime      int64
	StoreNum        int
	ActivityName    string
	ActivityCity    string
	Bounty          float64
	BountyLimitCent float64
	DailyQuantity   int
	IfCustomer      int8
	Platform        int
	StoreID         string
	BelongID        int64
}

package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// BusinessDistrictGrade 商圈等级领域对象
type BusinessDistrictGrade struct {
	ID          int64      // 主键ID
	Name        string     // 等级名称
	Description string     // 等级描述
	Level       int        // 等级值
	Status      int        // 状态 1:正常 2:禁用
	CreatedAt   time.Time  // 创建时间
	UpdatedAt   time.Time  // 更新时间
	DeletedAt   *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *BusinessDistrictGrade) ToModel() *model.BusinessDistrictGrade {
	return &model.BusinessDistrictGrade{
		ID:          d.ID,
		Name:        d.Name,
		Description: d.Description,
		Level:       d.Level,
		Status:      d.Status,
		CreatedAt:   d.CreatedAt,
		UpdatedAt:   d.UpdatedAt,
		DeletedAt:   d.DeletedAt,
	}
}

// FromBusinessDistrictGradeModel 从数据库模型创建领域对象
func FromBusinessDistrictGradeModel(m *model.BusinessDistrictGrade) *BusinessDistrictGrade {
	return &BusinessDistrictGrade{
		ID:          m.ID,
		Name:        m.Name,
		Description: m.Description,
		Level:       m.Level,
		Status:      m.Status,
		CreatedAt:   m.CreatedAt,
		UpdatedAt:   m.UpdatedAt,
		DeletedAt:   m.DeletedAt,
	}
}

// FromBusinessDistrictGradeModels 从数据库模型列表创建领域对象列表
func FromBusinessDistrictGradeModels(models []*model.BusinessDistrictGrade) []*BusinessDistrictGrade {
	result := make([]*BusinessDistrictGrade, len(models))
	for i, m := range models {
		result[i] = FromBusinessDistrictGradeModel(m)
	}
	return result
}

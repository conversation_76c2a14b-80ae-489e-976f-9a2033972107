package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// DeviceRec 设备记录领域对象
type DeviceRec struct {
	ID             int64      // 主键ID
	DeviceID       string     // 设备ID
	DeviceType     int        // 设备类型：1-Android 2-iOS 3-Web 4-其他
	DeviceName     string     // 设备名称
	DeviceModel    string     // 设备型号
	DeviceVersion  string     // 设备版本
	EmployeeID     int64      // 员工ID
	EmployeeName   string     // 员工姓名
	Status         int        // 状态：0-离线 1-在线
	LastActiveTime *time.Time // 最后活跃时间
	CreatedAt      time.Time  // 创建时间
	UpdatedAt      time.Time  // 更新时间
	DeletedAt      *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *DeviceRec) ToModel() *model.DeviceRec {
	return &model.DeviceRec{
		ID:             d.ID,
		DeviceID:       d.DeviceID,
		DeviceType:     d.DeviceType,
		DeviceName:     d.DeviceName,
		DeviceModel:    d.DeviceModel,
		DeviceVersion:  d.DeviceVersion,
		EmployeeID:     d.EmployeeID,
		EmployeeName:   d.EmployeeName,
		Status:         d.Status,
		LastActiveTime: d.LastActiveTime,
		CreatedAt:      d.CreatedAt,
		UpdatedAt:      d.UpdatedAt,
		DeletedAt:      d.DeletedAt,
	}
}

// FromDeviceRecModel 从数据库模型创建领域对象
func FromDeviceRecModel(m *model.DeviceRec) *DeviceRec {
	if m == nil {
		return nil
	}
	return &DeviceRec{
		ID:             m.ID,
		DeviceID:       m.DeviceID,
		DeviceType:     m.DeviceType,
		DeviceName:     m.DeviceName,
		DeviceModel:    m.DeviceModel,
		DeviceVersion:  m.DeviceVersion,
		EmployeeID:     m.EmployeeID,
		EmployeeName:   m.EmployeeName,
		Status:         m.Status,
		LastActiveTime: m.LastActiveTime,
		CreatedAt:      m.CreatedAt,
		UpdatedAt:      m.UpdatedAt,
		DeletedAt:      m.DeletedAt,
	}
}

// FromDeviceRecModels 从数据库模型列表创建领域对象列表
func FromDeviceRecModels(models []*model.DeviceRec) []*DeviceRec {
	if len(models) == 0 {
		return nil
	}
	result := make([]*DeviceRec, len(models))
	for i, m := range models {
		result[i] = FromDeviceRecModel(m)
	}
	return result
}

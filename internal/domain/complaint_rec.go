package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ComplaintRec 投诉记录领域对象
type ComplaintRec struct {
	ID               int64      // 主键ID
	ClueID           int64      // 线索ID
	ComplaintType    int        // 投诉类型：1-客户投诉 2-内部投诉
	ComplaintReason  string     // 投诉原因
	ComplaintContent string     // 投诉内容
	ProcessStatus    int        // 处理状态：0-待处理 1-处理中 2-已处理
	ProcessResult    string     // 处理结果
	ProcessorID      int64      // 处理人ID
	ProcessorName    string     // 处理人姓名
	ProcessTime      *time.Time // 处理时间
	CreatedAt        time.Time  // 创建时间
	UpdatedAt        time.Time  // 更新时间
	DeletedAt        *time.Time // 删除时间
}

// ToModel 转换为数据库模型
func (d *ComplaintRec) ToModel() *model.ComplaintRec {
	return &model.ComplaintRec{
		ID:               d.ID,
		ClueID:           d.ClueID,
		ComplaintType:    d.ComplaintType,
		ComplaintReason:  d.ComplaintReason,
		ComplaintContent: d.ComplaintContent,
		ProcessStatus:    d.ProcessStatus,
		ProcessResult:    d.ProcessResult,
		ProcessorID:      d.ProcessorID,
		ProcessorName:    d.ProcessorName,
		ProcessTime:      d.ProcessTime,
		CreatedAt:        d.CreatedAt,
		UpdatedAt:        d.UpdatedAt,
		DeletedAt:        d.DeletedAt,
	}
}

// FromComplaintRecModel 从数据库模型创建领域对象
func FromComplaintRecModel(m *model.ComplaintRec) *ComplaintRec {
	if m == nil {
		return nil
	}
	return &ComplaintRec{
		ID:               m.ID,
		ClueID:           m.ClueID,
		ComplaintType:    m.ComplaintType,
		ComplaintReason:  m.ComplaintReason,
		ComplaintContent: m.ComplaintContent,
		ProcessStatus:    m.ProcessStatus,
		ProcessResult:    m.ProcessResult,
		ProcessorID:      m.ProcessorID,
		ProcessorName:    m.ProcessorName,
		ProcessTime:      m.ProcessTime,
		CreatedAt:        m.CreatedAt,
		UpdatedAt:        m.UpdatedAt,
		DeletedAt:        m.DeletedAt,
	}
}

// FromComplaintRecModels 从数据库模型列表创建领域对象列表
func FromComplaintRecModels(models []*model.ComplaintRec) []*ComplaintRec {
	if len(models) == 0 {
		return nil
	}
	result := make([]*ComplaintRec, len(models))
	for i, m := range models {
		result[i] = FromComplaintRecModel(m)
	}
	return result
}

package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// ElmShopCodeEmployeeRel 饿了么店铺编码员工关系领域对象
type ElmShopCodeEmployeeRel struct {
	ID         int       // 主键ID
	EmployeeID int       // 员工ID
	StartTime  time.Time // 开始时间
	EndTime    time.Time // 结束时间
	TagID      int       // 类型 1--新签 0--其他
	Remark     string    // 备注
	CreatedAt  time.Time // 创建时间
	UpdatedAt  time.Time // 更新时间
	DeletedAt  time.Time // 删除时间
	City       string    // 城市
	ShopCode   string    // 店铺的code
	ShopName   string    // 店铺名称
	Type       int8      // 1-非品牌 2-品牌
	BrandName  string    // 品牌名称
	ExpireTime int       // 过期时间
}

// ToModel 将领域对象转换为数据库模型
func (d *ElmShopCodeEmployeeRel) ToModel() *model.ElmShopCodeEmployeeRel {
	return &model.ElmShopCodeEmployeeRel{
		ID:         int64(d.ID),
		EmployeeID: int64(d.EmployeeID),
		StartTime:  d.StartTime,
		EndTime:    d.EndTime,
		TagID:      int64(d.TagID),
		Remark:     d.Remark,
		CreatedAt:  d.CreatedAt,
		UpdatedAt:  d.UpdatedAt,
		DeletedAt:  d.DeletedAt,
		City:       d.City,
		ShopCode:   d.ShopCode,
		ShopName:   d.ShopName,
		Type:       d.Type,
		BrandName:  d.BrandName,
		ExpireTime: int64(d.ExpireTime),
	}
}

// FromElmShopCodeEmployeeRelModel 从数据库模型创建领域对象
func FromElmShopCodeEmployeeRelModel(m *model.ElmShopCodeEmployeeRel) *ElmShopCodeEmployeeRel {
	return &ElmShopCodeEmployeeRel{
		ID:         int(m.ID),
		EmployeeID: int(m.EmployeeID),
		StartTime:  m.StartTime,
		EndTime:    m.EndTime,
		TagID:      int(m.TagID),
		Remark:     m.Remark,
		CreatedAt:  m.CreatedAt,
		UpdatedAt:  m.UpdatedAt,
		DeletedAt:  m.DeletedAt,
		City:       m.City,
		ShopCode:   m.ShopCode,
		ShopName:   m.ShopName,
		Type:       m.Type,
		BrandName:  m.BrandName,
		ExpireTime: int(m.ExpireTime),
	}
}

// FromElmShopCodeEmployeeRelModels 从数据库模型列表创建领域对象列表
func FromElmShopCodeEmployeeRelModels(models []*model.ElmShopCodeEmployeeRel) []*ElmShopCodeEmployeeRel {
	result := make([]*ElmShopCodeEmployeeRel, len(models))
	for i, m := range models {
		result[i] = FromElmShopCodeEmployeeRelModel(m)
	}
	return result
}

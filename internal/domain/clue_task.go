package domain

import (
	"time"
)

// ClueTask 线索任务领域对象
type ClueTask struct {
	ID           int64      // 主键ID
	ClueID       int64      // 线索ID
	Title        string     // 任务标题
	Content      string     // 任务内容
	Priority     int        // 优先级 1:低 2:中 3:高
	Deadline     time.Time  // 截止时间
	AssigneeID   int64      // 负责人ID
	AssigneeName string     // 负责人姓名
	CreatorID    int64      // 创建人ID
	CreatorName  string     // 创建人姓名
	Status       int        // 状态 1:待处理 2:处理中 3:已完成 4:已取消
	CreatedAt    time.Time  // 创建时间
	UpdatedAt    time.Time  // 更新时间
	DeletedAt    *time.Time // 删除时间
}

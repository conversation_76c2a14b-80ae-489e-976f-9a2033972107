package domain

import "time"

// ShopReport 店铺报告领域模型
type ShopReport struct {
	ID                      int64     // 主键ID
	ShopID                  int64     // 店铺ID
	ShopCode                string    // 店铺编码
	ShopName                string    // 店铺名称
	BrandName               string    // 品牌名称
	UV                      int64     // 引流uv
	PromoteTaokeNum         int64     // 推广淘客数
	PayAmount               float64   // 付款金额
	PayNum                  int64     // 付款笔数
	OrderOriginalServiceFee float64   // 预估订单原始服务费
	OrderServicesFee        float64   // 预估订单招商服务费
	UserSettleAmount        float64   // 用户结算金额
	ShopSettleAmount        float64   // 商家结算金额
	SettleNum               int64     // 结算笔数
	OriginalServiceFee      float64   // 总预估结算原始服务费
	SettleFee               float64   // 总预估结算招商服务费
	CityServiceFee          float64   // 总预估订单城市服务商服务费
	CitySettleFee           float64   // 总预估结算城市服务商服务费
	OrderServiceFee         float64   // 总预估订单招商服务商服务费
	OrderSettleFee          float64   // 总预估结算招商服务商服务费
	City                    string    // 城市
	District                string    // 区域
	BusinessDistrict        string    // 商圈
	CreateTime              time.Time // 创建时间
	EmployeeID              int       // 员工表id
	IsNewShop               int8      // 是否新店
	Timeline                int8      // 时间线
	NewSignature            int8      // 新签
	Type                    int8      // 1-非品牌 2-品牌
	OfficialUV              int64     // 官方引流uv
	OfficialSettleNum       int64     // 官方结算笔数
	OfficialSettleFee       float64   // 官方总预估结算招商服务费
}

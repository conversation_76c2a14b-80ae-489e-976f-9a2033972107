package domain

type CallChannel int8

const (
	CallChannelXiaoBang CallChannel = iota + 1
	CallChannelYingKeYun
)

func (c CallChannel) Int8() int8 {
	return int8(c)
}

type CallParam struct {
	// Caller 主叫号码
	Caller string
	// Callee 被叫号码
	Callee string
	// UserID 用户ID
	UserID int64
	// ClueID 线索ID
	ClueID int64
	// OrderSn 自有订单ID
	OrderSn string
	// TelID 电话ID
	TelID string
	// Channel 渠道 1销帮 2赢客云
	Channel CallChannel
}

// CallResult 拨打电话响应
type CallResult struct {
	// AppLink 通话链接
	AppLink string `json:"appLink"`
}

// CallbackResult 回调请求参数
type CallbackResult struct {
	Channel CallChannel
	XiaoBangCallbackRecord
	YingKeYunCallbackRecord
}

// XiaoBangCallbackRecord 销帮回调记录
type XiaoBangCallbackRecord struct {
	Caller    string `json:"caller"`
	Callee    string `json:"callee"`
	StartTime string `json:"startTime"`
	EndTime   string `json:"endTime"`
	OrderId   string `json:"orderId"`
	BindId    string `json:"bind_id"`
	EventType string `json:"eventType"`
	HoldTime  string `json:"hold_time"`
	Showno    string `json:"showno"`
	AudioUrl  string `json:"audio_url"`
	Msg       string `json:"msg"`
}

// YingKeYunCallbackRecord 赢客云回调记录
type YingKeYunCallbackRecord struct {
	// Payload 订单编号
	Payload string `json:"payload"`
	// CallConnectedTime 通话开始时间
	CallConnectedTime int64 `json:"callConnectedTime"`
	// CallHangUpTime 通话结束时间
	CallHangUpTime int64 `json:"callHangUpTime"`
	// CallDuration 通话时长
	CallDuration int `json:"callDuration"`
	// SoundRecording 录音URL
	SoundRecording string `json:"soundRecording"`
	// CallRecordType 通话记录类型
	CallRecordType int `json:"callRecordType"`
	// TelX 显示号码
	TelX string `json:"telX"`
	// CallID 通话ID
	CallID string `json:"callId"`
	// CallStatus 通话状态
	CallStatus int `json:"callStatus"`
	// EndReason 结束原因
	EndReason string `json:"endReason"`
}

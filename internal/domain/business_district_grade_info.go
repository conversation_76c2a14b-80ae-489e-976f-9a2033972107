package domain

import (
	"time"

	"enter/internal/repository/dao/model"
)

// BusinessDistrictGradeInfo 商圈等级信息领域对象
type BusinessDistrictGradeInfo struct {
	ID               int64      // 主键ID
	GradeID          int64      // 等级ID
	BusinessDistrict string     // 商圈名称
	City             string     // 城市
	Province         string     // 省份
	Area             string     // 区域
	Address          string     // 详细地址
	Longitude        float64    // 经度
	Latitude         float64    // 纬度
	Status           int        // 状态 1:正常 2:禁用
	CreatedAt        time.Time  // 创建时间
	UpdatedAt        time.Time  // 更新时间
	DeletedAt        *time.Time // 删除时间
}

// ToModel 将领域对象转换为数据库模型
func (d *BusinessDistrictGradeInfo) ToModel() *model.BusinessDistrictGradeInfo {
	return &model.BusinessDistrictGradeInfo{
		ID:               d.ID,
		GradeID:          d.GradeID,
		BusinessDistrict: d.BusinessDistrict,
		City:             d.City,
		Province:         d.Province,
		Area:             d.Area,
		Address:          d.Address,
		Longitude:        d.Longitude,
		Latitude:         d.Latitude,
		Status:           d.Status,
		CreatedAt:        d.CreatedAt,
		UpdatedAt:        d.UpdatedAt,
		DeletedAt:        d.DeletedAt,
	}
}

// FromModel 从数据库模型创建领域对象
func FromModel(m *model.BusinessDistrictGradeInfo) *BusinessDistrictGradeInfo {
	return &BusinessDistrictGradeInfo{
		ID:               m.ID,
		GradeID:          m.GradeID,
		BusinessDistrict: m.BusinessDistrict,
		City:             m.City,
		Province:         m.Province,
		Area:             m.Area,
		Address:          m.Address,
		Longitude:        m.Longitude,
		Latitude:         m.Latitude,
		Status:           m.Status,
		CreatedAt:        m.CreatedAt,
		UpdatedAt:        m.UpdatedAt,
		DeletedAt:        m.DeletedAt,
	}
}

// FromModels 从数据库模型列表创建领域对象列表
func FromModels(models []*model.BusinessDistrictGradeInfo) []*BusinessDistrictGradeInfo {
	result := make([]*BusinessDistrictGradeInfo, len(models))
	for i, m := range models {
		result[i] = FromModel(m)
	}
	return result
}

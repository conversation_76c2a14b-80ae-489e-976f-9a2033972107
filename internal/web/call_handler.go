package web

import (
	"enter/internal/domain"
	"enter/internal/errs"
	"enter/internal/service"
	"enter/internal/web/jwt"
	"enter/internal/web/vo"
	"enter/pkg/ginx"
	"enter/pkg/uniqueid"
	"github.com/gin-gonic/gin"
	"net/http"
	"time"
)

type CallHandler struct {
	userSvc service.UserSvc
	callSvc service.CallService
}

func NewCallHandler(userSvc service.UserSvc, callSvc service.CallService) *CallHandler {
	return &CallHandler{
		userSvc: userSvc,
		callSvc: callSvc,
	}
}

// RegisterRoutes 注册路由
func (h *CallHandler) RegisterRoutes(engine *gin.Engine) {
	group := engine.Group("/api/v1")
	{
		group.POST("/call", ginx.WrapWithReqAndClaims(h.Call))
		group.POST("/callback", h.Callback)
	}
}

func (h *CallHandler) Call(ctx *gin.Context, req *vo.CallRequest, uc jwt.TokenClaim) (Result, error) {
	user, err := h.userSvc.GetUser(ctx, uc.GetUid())
	if err != nil {
		return Result{
			Code:    errs.CommonInternalServerError,
			Message: "用户出现错误",
		}, err
	}
	result, err := h.callSvc.Call(ctx, domain.CallParam{
		Callee:  req.Callee,
		Caller:  user.Mobile,
		UserID:  uc.GetUid(),
		ClueID:  req.ShopId,
		OrderSn: uniqueid.Generate(),
		TelID:   user.TelID,
		Channel: domain.CallChannel(req.Channel),
	})
	if err != nil {
		return Result{
			Code:    errs.CommonInternalServerError,
			Message: "呼叫出现错误",
		}, err
	}
	return Result{
		Code: errs.OK,
		Data: map[string]string{
			"app_link": result.AppLink,
		},
	}, nil
}

func (h *CallHandler) Callback(ctx *gin.Context) {
	var req vo.CallBackRequest
	if err := ctx.ShouldBind(&req); err != nil {
		ctx.JSON(http.StatusOK, map[string]any{
			"code": 500,
			"msg":  "fail",
			"data": false,
			"time": time.Now().Format(time.DateTime),
		})
		return
	}
	channel := domain.CallChannelYingKeYun
	if req.OrderId != "" {
		channel = domain.CallChannelXiaoBang
	}
	err := h.callSvc.HandleCallback(ctx, domain.CallbackResult{
		Channel: channel,
		XiaoBangCallbackRecord: domain.XiaoBangCallbackRecord{
			Caller:    req.Caller,
			Callee:    req.Callee,
			StartTime: req.StartTime,
			EndTime:   req.EndTime,
			OrderId:   req.OrderId,
			BindId:    req.BindId,
			EventType: req.EventType,
			HoldTime:  req.HoldTime,
			Showno:    req.Showno,
			AudioUrl:  req.AudioUrl,
			Msg:       req.Msg,
		},
		YingKeYunCallbackRecord: domain.YingKeYunCallbackRecord{
			Payload:           req.Payload,
			CallConnectedTime: req.CallConnectedTime,
			CallHangUpTime:    req.CallHangUpTime,
			CallDuration:      req.CallDuration,
			SoundRecording:    req.SoundRecording,
			CallRecordType:    req.CallRecordType,
			TelX:              req.TelX,
			CallID:            req.CallID,
			CallStatus:        req.CallStatus,
			EndReason:         req.EndReason,
		},
	})
	if err != nil {
		ctx.JSON(http.StatusOK, map[string]any{
			"code": 500,
			"msg":  "fail",
			"data": false,
			"time": time.Now().Format(time.DateTime),
		})
		return
	}
	ctx.JSON(http.StatusOK, map[string]any{
		"code": 200,
		"msg":  "success",
		"data": true,
		"time": time.Now().Format(time.DateTime),
	})
}

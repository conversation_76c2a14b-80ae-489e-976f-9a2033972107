package middleware

import (
	"net/http"
	"strings"

	"enter/internal/web/jwt"

	"github.com/gin-gonic/gin"
)

type LoginJwtMiddlewareBuilder struct {
	whileList []string
	auth      jwt.JWT
}

func NewLoginJwtMiddlewareBuilder(
	auth jwt.JWT,

) *LoginJwtMiddlewareBuilder {
	return &LoginJwtMiddlewareBuilder{
		whileList: []string{"/metrics", "/ping"},
		auth:      auth,
	}
}

func (b *LoginJwtMiddlewareBuilder) IgnorePaths(paths ...string) *LoginJwtMiddlewareBuilder {
	b.whileList = append(b.whileList, paths...)
	return b
}

func (b *LoginJwtMiddlewareBuilder) Build() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		for _, path := range b.whileList {
			if path == ctx.Request.URL.Path {
				return
			}
		}
		var userClaim jwt.TokenClaim
		userClaim.Info.Uid = 53
		//err := b.auth.ParseToken(ctx, &userClaim)
		//if err != nil {
		//	ctx.AbortWithStatus(http.StatusUnauthorized)
		//	return
		//}

		if userClaim.GetUid() == 0 {
			ctx.AbortWithStatus(http.StatusUnauthorized)
			return
		}
		// TODO 检测 UA
		// TODO 查询用户信息
		requestURI := ctx.Request.RequestURI
		switch userClaim.GetFlag() {
		case "admin":
			if !strings.HasPrefix(requestURI, "/admin") {
				ctx.AbortWithStatus(http.StatusForbidden)
				return
			}
		default:
			if !strings.HasPrefix(requestURI, "/api") {
				ctx.AbortWithStatus(http.StatusForbidden)
				return
			}
		}

		// 刷新 token
		// jwt 的 token 刷新即使生成了一个新的 token
		// 每隔10秒刷新一次
		//if userClaim.ExpiresAt.Sub(time.Now()) <= 50*time.Second {
		//	userClaim.ExpiresAt = jwt.NewNumericDate(time.Now().Add(time.Second * 60))
		//	tokenStr, err := token.SignedString([]byte("go-book"))
		//	if err != nil {
		//		// 打印日志
		//		fmt.Println(err)
		//	}
		//	ctx.Header("x-jwt-token", tokenStr)
		//}

		ctx.Set("claims", userClaim)
	}
}

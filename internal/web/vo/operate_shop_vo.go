package vo

import (
	"enter/internal/domain"
	"enter/pkg/validate"
)

// CreateOperateShopReq 创建运营店铺请求
type CreateOperateShopReq struct {
	// Name 店铺名称
	Name string `json:"name"`
	// CategoryID 分类ID
	CategoryID int `json:"category_id"`
	// Tel 联系方式
	Tel string `json:"tel"`
	// Address 地址
	Address string `json:"address"`
	// IsDine 有无堂食
	IsDine int8 `json:"is_dine"`
	// IsNew 是否新店
	IsNew int8 `json:"is_new"`
	// StoreArea 门店面积
	StoreArea int `json:"store_area"`
	// BrandID 品牌ID
	BrandID int64 `json:"brand_id"`
	// Platform 平台
	Platform int `json:"platform"`
	// ShopCode 店铺编码
	ShopCode string `json:"shop_code"`
	// IsCharge 前置收费
	IsCharge int8 `json:"is_charge"`
	// Fee 收费金额
	Fee float64 `json:"fee"`
	// CommissionRate 佣金比例
	CommissionRate float64 `json:"commission_rate"`
	// Remark 备注
	Remark string `json:"remark"`
	// ClueID 线索ID
	ClueID int64 `json:"clue_id"`
	// City 城市
	City string `json:"city"`
	// StartTime 签约时间
	StartTime int `json:"start_time"`
	// EndTime 结束时间
	EndTime int `json:"end_time"`
	// UserID 用户ID
	UserID int64 `json:"user_id"`
	// Status 状态
	Status int8 `json:"status"`
	// LevelTier 分层等级
	LevelTier string `json:"level_tier"`
	// HasPromotion 有无推广
	HasPromotion int `json:"has_promotion"`
	// HasBwc 推霸王餐
	HasBwc int `json:"has_bwc"`
	// MainIssues 主要问题
	MainIssues string `json:"main_issues"`
	// TerminationTime 解约时间
	TerminationTime int `json:"termination_time"`
	// IsScaling 是否起量
	IsScaling int `json:"is_scaling"`
	// Reason 原因
	Reason string `json:"reason"`
	// UserType 用户类型
	UserType int8 `json:"user_type"`
	// Mode 模式
	Mode int8 `json:"mode"`
	// StoreID 店铺ID
	StoreID string `json:"store_id"`
}

var _ validate.Validator = (*CreateOperateShopReq)(nil)

func (v *CreateOperateShopReq) Rules() map[string][]string {
	return map[string][]string{
		"name":        {"required"},
		"category_id": {"required"},
		"tel":         {"required"},
		"address":     {"required"},
		"platform":    {"required"},
		"city":        {"required"},
		"user_id":     {"required"},
	}
}

func (v *CreateOperateShopReq) Messages() map[string][]string {
	return map[string][]string{
		"name":        {"required:店铺名称不能为空"},
		"category_id": {"required:分类ID不能为空"},
		"tel":         {"required:联系方式不能为空"},
		"address":     {"required:地址不能为空"},
		"platform":    {"required:平台不能为空"},
		"city":        {"required:城市不能为空"},
		"user_id":     {"required:用户ID不能为空"},
	}
}

// ToDomain 转换为领域对象
func (v *CreateOperateShopReq) ToDomain() domain.OperateShop {
	return domain.OperateShop{
		Name:            v.Name,
		CategoryID:      v.CategoryID,
		Tel:             v.Tel,
		Address:         v.Address,
		IsDine:          v.IsDine,
		IsNew:           v.IsNew,
		StoreArea:       v.StoreArea,
		BrandID:         v.BrandID,
		Platform:        v.Platform,
		ShopCode:        v.ShopCode,
		IsCharge:        v.IsCharge,
		Fee:             v.Fee,
		CommissionRate:  v.CommissionRate,
		Remark:          v.Remark,
		ClueID:          v.ClueID,
		City:            v.City,
		StartTime:       v.StartTime,
		EndTime:         v.EndTime,
		UserID:          v.UserID,
		Status:          v.Status,
		LevelTier:       v.LevelTier,
		HasPromotion:    v.HasPromotion,
		HasBwc:          v.HasBwc,
		MainIssues:      v.MainIssues,
		TerminationTime: v.TerminationTime,
		IsScaling:       v.IsScaling,
		Reason:          v.Reason,
		UserType:        v.UserType,
		Mode:            v.Mode,
		StoreID:         v.StoreID,
	}
}

type UpdateOperateShopReq struct {
	// ID 店铺ID
	ID int64 `uri:"id"`
	// Name 店铺名称
	Name string `json:"name"`
	// CategoryID 分类ID
	CategoryID int `json:"category_id"`
	// Tel 联系方式
	Tel string `json:"tel"`
	// Address 地址
	Address string `json:"address"`
	// IsDine 有无堂食
	IsDine int8 `json:"is_dine"`
	// IsNew 是否新店
	IsNew int8 `json:"is_new"`
	// StoreArea 门店面积
	StoreArea int `json:"store_area"`
	// BrandID 品牌ID
	BrandID int64 `json:"brand_id"`
	// Platform 平台
	Platform int `json:"platform"`
	// ShopCode 店铺编码
	ShopCode string `json:"shop_code"`
	// IsCharge 前置收费
	IsCharge int8 `json:"is_charge"`
	// Fee 收费金额
	Fee float64 `json:"fee"`
	// CommissionRate 佣金比例
	CommissionRate float64 `json:"commission_rate"`
	// Remark 备注
	Remark string `json:"remark"`
	// ClueID 线索ID
	ClueID int64 `json:"clue_id"`
	// City 城市
	City string `json:"city"`
	// StartTime 签约时间
	StartTime int `json:"start_time"`
	// EndTime 结束时间
	EndTime int `json:"end_time"`
	// UserID 用户ID
	UserID int64 `json:"user_id"`
	// Status 状态
	Status int8 `json:"status"`
	// LevelTier 分层等级
	LevelTier string `json:"level_tier"`
	// HasPromotion 有无推广
	HasPromotion int `json:"has_promotion"`
	// HasBwc 推霸王餐
	HasBwc int `json:"has_bwc"`
	// MainIssues 主要问题
	MainIssues string `json:"main_issues"`
	// TerminationTime 解约时间
	TerminationTime int `json:"termination_time"`
	// IsScaling 是否起量
	IsScaling int `json:"is_scaling"`
	// Reason 原因
	Reason string `json:"reason"`
	// UserType 用户类型
	UserType int8 `json:"user_type"`
	// Mode 模式
	Mode int8 `json:"mode"`
	// StoreID 店铺ID
	StoreID string `json:"store_id"`
}

var _ validate.Validator = (*UpdateOperateShopReq)(nil)

func (v *UpdateOperateShopReq) Rules() map[string][]string {
	return map[string][]string{
		"id": {"required"},
	}
}

func (v *UpdateOperateShopReq) Messages() map[string][]string {
	return map[string][]string{
		"id": {"required:店铺ID不能为空"},
	}
}

// ToDomain 转换为领域对象
func (v *UpdateOperateShopReq) ToDomain() domain.OperateShop {
	return domain.OperateShop{
		ID:              v.ID,
		Name:            v.Name,
		CategoryID:      v.CategoryID,
		Tel:             v.Tel,
		Address:         v.Address,
		IsDine:          v.IsDine,
		IsNew:           v.IsNew,
		StoreArea:       v.StoreArea,
		BrandID:         v.BrandID,
		Platform:        v.Platform,
		ShopCode:        v.ShopCode,
		IsCharge:        v.IsCharge,
		Fee:             v.Fee,
		CommissionRate:  v.CommissionRate,
		Remark:          v.Remark,
		ClueID:          v.ClueID,
		City:            v.City,
		StartTime:       v.StartTime,
		EndTime:         v.EndTime,
		UserID:          v.UserID,
		Status:          v.Status,
		LevelTier:       v.LevelTier,
		HasPromotion:    v.HasPromotion,
		HasBwc:          v.HasBwc,
		MainIssues:      v.MainIssues,
		TerminationTime: v.TerminationTime,
		IsScaling:       v.IsScaling,
		Reason:          v.Reason,
		UserType:        v.UserType,
		Mode:            v.Mode,
		StoreID:         v.StoreID,
	}
}

type GetOperateShopReq struct {
	// ID 店铺ID
	ID int64 `uri:"id"`
}

type ListOperateShopReq struct {
	// Page 页码
	Page int `form:"page"`
	// PageSize 每页数量
	PageSize int `form:"page_size"`
	// Name 店铺名称
	Name string `form:"name"`
	// City 城市
	City string `form:"city"`
	// Platform 平台
	Platform int `form:"platform"`
	// Status 状态
	Status int8 `form:"status"`
	// UserID 用户ID
	UserID int64 `form:"user_id"`
	// BrandID 品牌ID
	BrandID int64 `form:"brand_id"`
}

type DeleteOperateShopReq struct {
	// ID 店铺ID
	ID int64 `uri:"id"`
}

type OperateShopResp struct {
	// ID 店铺ID
	ID int64 `json:"id"`
	// Name 店铺名称
	Name string `json:"name"`
	// CreateTime 创建时间
	CreateTime int `json:"create_time"`
	// UpdateTime 更新时间
	UpdateTime int `json:"update_time"`
	// CategoryID 分类ID
	CategoryID int `json:"category_id"`
	// Tel 联系方式
	Tel string `json:"tel"`
	// Address 地址
	Address string `json:"address"`
	// IsDine 有无堂食
	IsDine int8 `json:"is_dine"`
	// IsNew 是否新店
	IsNew int8 `json:"is_new"`
	// StoreArea 门店面积
	StoreArea int `json:"store_area"`
	// BrandID 品牌ID
	BrandID int64 `json:"brand_id"`
	// Platform 平台
	Platform int `json:"platform"`
	// ShopCode 店铺编码
	ShopCode string `json:"shop_code"`
	// IsCharge 前置收费
	IsCharge int8 `json:"is_charge"`
	// Fee 收费金额
	Fee float64 `json:"fee"`
	// CommissionRate 佣金比例
	CommissionRate float64 `json:"commission_rate"`
	// Remark 备注
	Remark string `json:"remark"`
	// ClueID 线索ID
	ClueID int64 `json:"clue_id"`
	// City 城市
	City string `json:"city"`
	// StartTime 签约时间
	StartTime int `json:"start_time"`
	// EndTime 结束时间
	EndTime int `json:"end_time"`
	// UserID 用户ID
	UserID int64 `json:"user_id"`
	// Status 状态
	Status int8 `json:"status"`
	// LevelTier 分层等级
	LevelTier string `json:"level_tier"`
	// HasPromotion 有无推广
	HasPromotion int `json:"has_promotion"`
	// HasBwc 推霸王餐
	HasBwc int `json:"has_bwc"`
	// MainIssues 主要问题
	MainIssues string `json:"main_issues"`
	// TerminationTime 解约时间
	TerminationTime int `json:"termination_time"`
	// IsScaling 是否起量
	IsScaling int `json:"is_scaling"`
	// Reason 原因
	Reason string `json:"reason"`
	// UserType 用户类型
	UserType int8 `json:"user_type"`
	// Mode 模式
	Mode int8 `json:"mode"`
	// StoreID 店铺ID
	StoreID string `json:"store_id"`
}

// FromDomain 从领域对象转换
func (v *OperateShopResp) FromDomain(shop domain.OperateShop) *OperateShopResp {
	return &OperateShopResp{
		ID:              shop.ID,
		Name:            shop.Name,
		CreateTime:      shop.CreateTime,
		UpdateTime:      shop.UpdateTime,
		CategoryID:      shop.CategoryID,
		Tel:             shop.Tel,
		Address:         shop.Address,
		IsDine:          shop.IsDine,
		IsNew:           shop.IsNew,
		StoreArea:       shop.StoreArea,
		BrandID:         shop.BrandID,
		Platform:        shop.Platform,
		ShopCode:        shop.ShopCode,
		IsCharge:        shop.IsCharge,
		Fee:             shop.Fee,
		CommissionRate:  shop.CommissionRate,
		Remark:          shop.Remark,
		ClueID:          shop.ClueID,
		City:            shop.City,
		StartTime:       shop.StartTime,
		EndTime:         shop.EndTime,
		UserID:          shop.UserID,
		Status:          shop.Status,
		LevelTier:       shop.LevelTier,
		HasPromotion:    shop.HasPromotion,
		HasBwc:          shop.HasBwc,
		MainIssues:      shop.MainIssues,
		TerminationTime: shop.TerminationTime,
		IsScaling:       shop.IsScaling,
		Reason:          shop.Reason,
		UserType:        shop.UserType,
		Mode:            shop.Mode,
		StoreID:         shop.StoreID,
	}
}

type ListOperateShopResp struct {
	// List 店铺列表
	List []OperateShopResp `json:"list"`
	// Total 总数
	Total int64 `json:"total"`
	// Page 当前页
	Page int `json:"page"`
	// Size 每页数量
	Size int `json:"size"`
}

// FromDomainList 从领域对象列表转换
func (v *ListOperateShopResp) FromDomainList(shops []domain.OperateShop, total int64, page, size int) *ListOperateShopResp {
	list := make([]OperateShopResp, len(shops))
	for i, shop := range shops {
		resp := &OperateShopResp{}
		list[i] = *resp.FromDomain(shop)
	}
	return &ListOperateShopResp{
		List:  list,
		Total: total,
		Page:  page,
		Size:  size,
	}
}

package vo

import "enter/pkg/validate"

type CallRequest struct {
	ShopId   int64  `json:"shop_id"`
	CallType int    `json:"call_type"`
	Callee   string `json:"callee"`
	Channel  int8   `json:"channel" valid:"channel"`
}

var _ validate.Validator = (*CallRequest)(nil)

func (v *CallRequest) Rules() map[string][]string {
	return map[string][]string{
		"channel": []string{"required"},
	}
}

func (v *CallRequest) Messages() map[string][]string {
	return map[string][]string{
		"channel": []string{"required:渠道必须存在"},
	}
}

type CallBackRequest struct {
	// Caller 主叫
	Caller string `json:"caller"`
	// Callee 被叫
	Callee string `json:"callee"`
	// StartTime 通话开始时间
	StartTime string `json:"startTime"`
	// EndTime 通话结束时间
	EndTime string `json:"endTime"`
	// OrderId 通话ID
	OrderId string `json:"orderId"`
	// BindId 绑定ID
	BindId string `json:"bind_id"`
	// EventType 事件类型
	EventType string `json:"eventType"`
	// HoldTime 通话时长
	HoldTime string `json:"hold_time"`
	// Showno 小号
	Showno string `json:"showno"`
	// AudioUrl 录音URL
	AudioUrl string `json:"audio_url"`
	// Msg 结束原因
	Msg string `json:"msg"`
	// Payload 订单编号
	Payload string `json:"payload"`
	// CallConnectedTime 通话开始时间
	CallConnectedTime int64 `json:"callConnectedTime"`
	// CallHangUpTime 通话结束时间
	CallHangUpTime int64 `json:"callHangUpTime"`
	// CallDuration 通话时长
	CallDuration int `json:"callDuration"`
	// SoundRecording 录音URL
	SoundRecording string `json:"soundRecording"`
	// CallRecordType 通话记录类型
	CallRecordType int `json:"callRecordType"`
	// TelX 显示号码
	TelX string `json:"telX"`
	// CallID 通话ID
	CallID string `json:"callId"`
	// CallStatus 通话状态
	CallStatus int `json:"callStatus"`
	// EndReason 结束原因
	EndReason string `json:"endReason"`
}

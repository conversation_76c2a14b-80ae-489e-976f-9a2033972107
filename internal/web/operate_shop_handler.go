package web

import (
	"strconv"

	"github.com/gin-gonic/gin"

	"enter/internal/errs"
	"enter/internal/service"
	"enter/internal/web/vo"
	"enter/pkg/ginx"
)

type OperateShopHandler struct {
	operateShopSvc service.OperateShopService
}

func NewOperateShopHandler(operateShopSvc service.OperateShopService) *OperateShopHandler {
	return &OperateShopHandler{
		operateShopSvc: operateShopSvc,
	}
}

// RegisterRoutes 注册路由
func (h *OperateShopHandler) RegisterRoutes(engine *gin.Engine) {
	group := engine.Group("/api/v1/operate-shops")
	{
		group.POST("", ginx.WrapWithReq(h.CreateOperateShop))
		group.PUT("/:id", ginx.WrapWithReq(h.UpdateOperateShop))
		group.DELETE("/:id", ginx.Wrap(h.DeleteOperateShop))
		group.GET("/:id", ginx.Wrap(h.GetOperateShop))
		group.GET("", ginx.Wrap(h.ListOperateShopsWrapper))
	}
}

func (h *OperateShopHandler) CreateOperateShop(ctx *gin.Context, req *vo.CreateOperateShopReq) (ginx.Result, error) {
	// 转换为领域对象
	shop := req.ToDomain()

	// 调用服务层创建店铺
	id, err := h.operateShopSvc.Create(ctx.Request.Context(), shop)
	if err != nil {
		if errs.IsBizError(err) {
			return ginx.Result{
				Code:    errs.InvalidParam,
				Message: errs.GetBizErrorMessage(err),
			}, nil
		}
		return ginx.Result{
			Code:    errs.ServerError,
			Message: "创建店铺失败",
		}, err
	}

	return ginx.Result{
		Code:    errs.OK,
		Message: "创建成功",
		Data: map[string]int64{
			"id": id,
		},
	}, nil
}

func (h *OperateShopHandler) UpdateOperateShop(ctx *gin.Context, req *vo.UpdateOperateShopReq) (ginx.Result, error) {
	// 转换为领域对象
	shop := req.ToDomain()

	// 调用服务层更新店铺
	err := h.operateShopSvc.Update(ctx.Request.Context(), shop)
	if err != nil {
		if errs.IsBizError(err) {
			return ginx.Result{
				Code:    errs.InvalidParam,
				Message: errs.GetBizErrorMessage(err),
			}, nil
		}
		return ginx.Result{
			Code:    errs.ServerError,
			Message: "更新店铺失败",
		}, err
	}

	return ginx.Result{
		Code:    errs.OK,
		Message: "更新成功",
	}, nil
}

func (h *OperateShopHandler) DeleteOperateShop(ctx *gin.Context) (ginx.Result, error) {
	// 获取路径参数
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return ginx.Result{
			Code:    errs.InvalidParam,
			Message: "店铺ID格式错误",
		}, nil
	}

	// 调用服务层删除店铺
	err = h.operateShopSvc.Delete(ctx.Request.Context(), id)
	if err != nil {
		if errs.IsBizError(err) {
			return ginx.Result{
				Code:    errs.InvalidParam,
				Message: errs.GetBizErrorMessage(err),
			}, nil
		}
		return ginx.Result{
			Code:    errs.ServerError,
			Message: "删除店铺失败",
		}, err
	}

	return ginx.Result{
		Code:    errs.OK,
		Message: "删除成功",
	}, nil
}

func (h *OperateShopHandler) GetOperateShop(ctx *gin.Context) (ginx.Result, error) {
	// 获取路径参数
	idStr := ctx.Param("id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		return ginx.Result{
			Code:    errs.InvalidParam,
			Message: "店铺ID格式错误",
		}, nil
	}

	// 调用服务层获取店铺
	shop, err := h.operateShopSvc.GetByID(ctx.Request.Context(), id)
	if err != nil {
		if errs.IsBizError(err) {
			return ginx.Result{
				Code:    errs.NotFound,
				Message: errs.GetBizErrorMessage(err),
			}, nil
		}
		return ginx.Result{
			Code:    errs.ServerError,
			Message: "获取店铺失败",
		}, err
	}

	// 转换为响应对象
	resp := &vo.OperateShopResp{}
	resp = resp.FromDomain(shop)

	return ginx.Result{
		Code:    errs.OK,
		Message: "获取成功",
		Data:    resp,
	}, nil
}

func (h *OperateShopHandler) ListOperateShops(ctx *gin.Context, req *vo.ListOperateShopReq) (ginx.Result, error) {
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 10
	}

	// 转换为服务层请求
	serviceReq := service.ListOperateShopReq{
		Page:     req.Page,
		PageSize: req.PageSize,
		Name:     req.Name,
		City:     req.City,
		Platform: req.Platform,
		Status:   req.Status,
		UserID:   req.UserID,
		BrandID:  req.BrandID,
	}

	// 调用服务层获取列表
	shops, total, err := h.operateShopSvc.List(ctx.Request.Context(), serviceReq)
	if err != nil {
		return ginx.Result{
			Code:    errs.ServerError,
			Message: "获取店铺列表失败",
		}, err
	}

	// 转换为响应对象
	resp := &vo.ListOperateShopResp{}
	resp = resp.FromDomainList(shops, total, req.Page, req.PageSize)

	return ginx.Result{
		Code:    errs.OK,
		Message: "获取成功",
		Data:    resp,
	}, nil
}

func (h *OperateShopHandler) ListOperateShopsWrapper(ctx *gin.Context) (ginx.Result, error) {
	var req vo.ListOperateShopReq
	if err := ctx.ShouldBindQuery(&req); err != nil {
		return ginx.Result{
			Code:    errs.InvalidParam,
			Message: "参数错误: " + err.Error(),
		}, nil
	}

	return h.ListOperateShops(ctx, &req)
}

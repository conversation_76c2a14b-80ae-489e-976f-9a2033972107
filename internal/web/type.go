package web

import (
	"enter/pkg/ginx"
	"errors"
	"time"

	"github.com/gin-gonic/gin"
)

type Result = ginx.Result

type handler interface {
	RegisterRoutes(*gin.Engine)
}

func ParseStartTimeAndEndTime(ctx *gin.Context) (time.Time, time.Time, error) {
	// 默认时间范围前1天
	sTime := time.Now().AddDate(0, 0, -1)
	eTime := time.Now()

	startTime := ctx.Query("start_time")
	if startTime != "" {
		newTime, err := time.ParseInLocation(time.DateTime, startTime, time.Local)
		if err != nil {
			return time.Time{}, time.Time{}, errors.New("开始时间格式不正确")
		}
		sTime = newTime
	}

	endTime := ctx.Query("end_time")
	if endTime != "" {
		newTime, err := time.ParseInLocation(time.DateTime, endTime, time.Local)
		if err != nil {
			return time.Time{}, time.Time{}, errors.New("结束时间格式不正确")
		}
		eTime = newTime
	}
	return sTime, eTime, nil
}

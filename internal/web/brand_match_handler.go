package web

import (
	"net/http"

	"github.com/gin-gonic/gin"

	"enter/internal/service"
	"enter/internal/web/vo"
)

type BrandMatchHandler struct {
	brandMatchService service.BrandMatchService
}

func NewBrandMatchHandler(brandMatchService service.BrandMatchService) *BrandMatchHandler {
	return &BrandMatchHandler{
		brandMatchService: brandMatchService,
	}
}

// RegisterRoutes 注册路由
func (h *BrandMatchHandler) RegisterRoutes(engine *gin.Engine) {
	group := engine.Group("/api/v1")
	{
		group.POST("/brand/match", h.matchBrands)
	}
}

// @Summary 匹配店铺品牌
// @Description 通过店铺名称匹配品牌名称，并更新店铺的品牌ID
// @Tags 品牌管理
// @Accept json
// @Produce json
// @Param request body vo.BrandMatchReq true "匹配请求参数"
// @Success 200 {object} vo.BrandMatchResp "匹配结果"
// @Router /api/v1/brand/match [post]
func (h *BrandMatchHandler) matchBrands(c *gin.Context) {
	var req vo.BrandMatchReq
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	matchCount, err := h.brandMatchService.MatchAndUpdateBrands(c.Request.Context(), req.BatchSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	c.JSON(http.StatusOK, vo.BrandMatchResp{
		MatchCount: matchCount,
	})
}

package jwt

import (
	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

type Info struct {
	Uid  int64
	UA   string
	Flag string
}
type TokenClaim struct {
	jwt.RegisteredClaims
	Info Info
}

func (tc *TokenClaim) GetUid() int64 {
	return tc.Info.Uid
}

func (tc *TokenClaim) GetFlag() string {
	return tc.Info.Flag
}

type JWT interface {
	SetAccessToken(ctx *gin.Context, info Info) error
	GetAccessToken(ctx *gin.Context, info Info) (string, error)
	ClearToken(ctx *gin.Context) error
	ExtractToken(ctx *gin.Context) (string, error)
	ParseToken(ctx *gin.Context, claims jwt.Claims) error
}

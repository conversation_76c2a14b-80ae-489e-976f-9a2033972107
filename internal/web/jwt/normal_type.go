package jwt

import (
	"errors"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/golang-jwt/jwt/v5"
)

type NormalJWT struct {
	key         []byte
	tokenExpire time.Duration
	headerKey   string // 返回给前端的响应头的 key
	tokenKey    string // 前端携带的 token 的请求头的 key
}

type Option func(normalJWT *NormalJWT)

func WithTokenExpire(duration time.Duration) Option {
	return func(normalJWT *NormalJWT) {
		normalJWT.tokenExpire = duration
	}
}

func WithKey(key []byte) Option {
	return func(normalJWT *NormalJWT) {
		normalJWT.key = key
	}
}

func WithHeaderKey(key string) Option {
	return func(normalJWT *NormalJWT) {
		normalJWT.headerKey = key
	}
}

func WithTokenKey(key string) Option {
	return func(normalJWT *NormalJWT) {
		normalJWT.tokenKey = key
	}
}

func NewNormalJWT(opts ...Option) *NormalJWT {
	res := &NormalJWT{
		key:         []byte("sq@!^&*"),
		tokenExpire: time.Hour * 24,
		headerKey:   "x-jwt-token",
		tokenKey:    "Authorization",
	}
	for _, opt := range opts {
		opt(res)
	}
	return res
}

func (n *NormalJWT) GetAccessToken(ctx *gin.Context, info Info) (string, error) {
	token := jwt.NewWithClaims(jwt.SigningMethodHS256, TokenClaim{
		RegisteredClaims: jwt.RegisteredClaims{
			ExpiresAt: jwt.NewNumericDate(time.Now().Add(n.tokenExpire)),
		},
		Info: info,
	})
	jwtToken, err := token.SignedString(n.key)
	if err != nil {
		return "", err
	}
	return jwtToken, nil
}

func (n *NormalJWT) SetAccessToken(ctx *gin.Context, info Info) error {
	token, err := n.GetAccessToken(ctx, info)
	if err != nil {
		return err
	}
	ctx.Header(n.headerKey, token)
	return nil
}

func (n *NormalJWT) ClearToken(ctx *gin.Context) error {
	// 设置 header 值为空，前端拿到空值之后会更新本地存储的 x-jwt-token 从而达到删除的效果
	ctx.Header(n.headerKey, "")
	return nil
}

func (n *NormalJWT) ExtractToken(ctx *gin.Context) (string, error) {
	// 处理 jwt
	jwtOriStr := ctx.Request.Header.Get(n.tokenKey)
	if jwtOriStr == "" {
		return "", errors.New("获取不到 token")
	}
	jwtSlice := strings.SplitN(jwtOriStr, " ", 2)
	if len(jwtSlice) != 2 {
		return "", errors.New("token 不合法")
	}
	return jwtSlice[1], nil
}

// ParseToken 解析 token
// claims 必须是指针类型
func (n *NormalJWT) ParseToken(ctx *gin.Context, claims jwt.Claims) error {
	jwtStr, err := n.ExtractToken(ctx)
	if err != nil {
		return err
	}
	token, err := jwt.ParseWithClaims(jwtStr, claims, func(token *jwt.Token) (interface{}, error) {
		return n.key, nil
	})
	if err != nil {
		return err
	}

	if token == nil || !token.Valid {
		return errors.New("token 无效")
	}
	return nil
}
